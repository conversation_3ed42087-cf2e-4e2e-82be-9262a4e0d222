<template>
    <div class="permissionBox">
       <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/permission-management' }">权限管理</el-breadcrumb-item>
            <el-breadcrumb-item>授权</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-button type="primary" @click="saveBtn">保存</el-button>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableContant">
            <el-table
                :data="tableData"
                style="width: 100%;"
                row-key="menuId"
                border
                default-expand-all
                :tree-props="{children: 'childList', hasChildren: 'hasChildren'}">
                <el-table-column prop="name" label="菜单"></el-table-column>
                <el-table-column prop="checked" label="是否允许访问">
                    <template slot-scope="scope">
                        <el-checkbox v-model="scope.row.checked" @change="handleIsAllow(scope.$index,scope.row)"></el-checkbox>
                    </template>
                </el-table-column>
                <el-table-column prop="radio" label="数据权限">
                    <template slot-scope="scope">
                        <el-radio-group v-model="scope.row.radio" @change="handleChange(scope.$index,scope.row.radio)">
                            <el-radio :label="0">全部</el-radio>
                            <el-radio :label="1">plant</el-radio>
                            <el-radio :label="2">VSM</el-radio>
                            <el-radio :label="3">workshop</el-radio>
                        </el-radio-group>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- table end -->
    </div>
</template>
<script>
export default {
    data(){
        return{
            // 表格
            tableData:[],
            rnumber:''
        }
    },
    computed: {

    },
    methods:{
        // 渲染数据 菜单列表
        initData(){
            this.$http.queryMenuAll(null,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    // this.tableData = res.data;
                    var resData = res.data;

                    const addKey = resData => resData.map(item => ({
                        ...item,
                        checked: false,
                        radio:0,
                        childList: addKey(item.childList || [])
                    }))

                    this.tableData = addKey(resData)
                    this.queryRoleMenu()
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // 根据角色编号查询角色与菜单绑定关系
        queryRoleMenu(){
            let data = {
                    roleNumber:this.rnumber
                }
            this.$http.queryRoleMenuByRoleNumber(data,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    var newArray =  this.jsonToArray(this.tableData);
                    for(let i = 0; i<res.data.length;i++){
                        for(let j = 0;j<newArray.length;j++){
                            if(res.data[i].menuId == newArray[j].menuId){
                                newArray[j].checked = true;
                                newArray[j].radio = res.data[i].limits;
                            }
                        }
                    }
                    this.tableData = this.arrayToJson(newArray);
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        arrayToJson(arr) {
            var r=[];
            if(!Array.isArray(arr)) {
                return r
            }
            arr.forEach(item => {
                delete item.childList;
            });
            let map = {};
            arr.forEach(item => {
                map[item.menuId] = item;
            });
            arr.forEach(item => {
                let parent = map[item.pid];
                if(parent) {
                    (parent.childList || (parent.childList = [])).push(item);
                } else {
                    r.push(item);
                }
            });
            return r;
        },

        jsonToArray(arr) {
            var r=[];
            if (Array.isArray(arr)) {
                for (var i=0; i<arr.length; i++) {
                    r.push(arr[i]);
                    if (Array.isArray(arr[i]["childList"]) && arr[i]["childList"].length>0)
                    r = r.concat(this.jsonToArray(arr[i]["childList"]));
                        delete arr[i]["childList"]
                }
            } 
            return r;
        },

        // 保存
        saveBtn(){
            console.log(this.tableData)
            var newSouce = [];
            var newArray =  this.jsonToArray(this.tableData);
            console.log(newArray)
            for (let i = 0;i<newArray.length;i++){
                if(newArray[i].checked == true){
                    // if(newArray[i].radio == ''){
                    //     this.$message.error("允许访问后请选择数据权限")
                    //     return
                    // }else{
                        var newObj = {
                            limits:newArray[i].radio,
                            menuId:newArray[i].menuId,
                            roleNumber:this.rnumber
                        }
                        newSouce.push(newObj)
                    // }  
                }else{

                }
            }
            this.$http.addRoleMenuList(newSouce,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    this.$message.success(res.message);
                    this.$router.push({ path:'/permission-management'})
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        handleIsAllow(index,val){
            console.log(val)
            if(val.childList){
                if(val.checked == true){
                    for(let i = 0;i<val.childList.length;i++){
                        val.childList[i].checked = true;
                    }
                }else if(val.checked == false){
                    for(let i = 0;i<val.childList.length;i++){
                        val.childList[i].checked = false;
                    }
                }
            }else{
                if(val.pid != '0'){
                    for(let j = 0;j<this.tableData.length;j++){
                        if(val.pid == this.tableData[j].menuId){
                            var trueArr = [];
                            var dataArr = this.tableData[j].childList;
                            for(let k = 0;k<dataArr.length;k++){
                                if(dataArr[k].checked == false)
                                trueArr.push(dataArr[k].checked);
                            }

                            if(trueArr.length == dataArr.length){
                                this.tableData[j].checked = false;
                            }else{
                                this.tableData[j].checked = true;
                            }
                        }
                    }
                }
            }
        },
        handleChange(index,val){
            console.log('index:'+index)
            console.log('handleChange:'+val)
        }
    },
    mounted(){
        this.rnumber = this.$route.query.rnumber;
        this.initData();
        // this.queryRoleMenu();
    }
}
</script>
