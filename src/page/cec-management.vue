<template>
    <div class="supervisorSapBox">
        <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>管理员设置</el-breadcrumb-item>
            <el-breadcrumb-item>CEC签核权限管理</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
                <el-form-item label="主管SAP编号：" prop="leaderSapNo">
                    <el-input v-model="formInline.leaderSapNo" placeholder="请输入主管SAP编号"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="initData(currentPage = 1)">查询</el-button>
                    <el-button type="primary" @click="addBtn">新建</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableContent">
            <table>
                <thead>
                    <tr>
                        <th>主管SAP编号</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="tableData.length <= 0">
                        <td colspan="2">暂无数据!</td>
                    </tr>
                    <tr v-for="(item,index) in tableData" :key='index' v-else>
                        <td>{{item.leaderSapNo}}</td>
                        <td>
                            <el-button type="text" @click="handleDelete(item.id)">删除</el-button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- table end -->

        <!-- 分页 start -->
        <div class="paginationBox">
            <el-pagination v-show="total!=0" background layout="total, prev, pager, next, sizes, jumper" :total="total" :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData" @current-change="initData"></el-pagination>
        </div>
        <!-- 分页 end -->

        <!-- 新建/编辑主管SAP dialog start -->
        <el-dialog
            :title="dialogTitle == 0 ? '新建主管SAP编号' : '编辑主管SAP编号'"
            :visible.sync="dialogAdd"
            width="60%"
            :close-on-click-modal='false'
            @close="resetForm('ruleAddForm')"
            >
            <el-form :model="ruleAddForm" :rules="rulesAdd" ref="ruleAddForm" label-width="120px" class="demo-ruleForm">
                <el-form-item label="主管SAP编号：" prop="leaderSapNo">
                    <el-input v-model="ruleAddForm.leaderSapNo" placeholder="请输入主管SAP编号"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleAddForm')">确定</el-button>
                    <el-button @click="resetForm('ruleAddForm')">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 新建/编辑主管SAP dialog end -->
    </div>
</template>
<script>
export default {
    data(){
        return{
            // 头部查询
            formInline: {
              leaderSapNo:'',
            },
            // 表格
            tableData:[],
            // 分页
            currentPage:1,
            pageSize:10,
            total:0,
            dialogAdd:false,//新建/编辑主管SAP dialog
            // 新建/编辑主管SAP Form
            ruleAddForm:{
              leaderSapNo:''
            },
            rulesAdd:{
              leaderSapNo: [
                    { required: true, message: '请输入主管SAP编号', trigger: 'blur' }
                ]
            },
            dialogTitle:'', // 0: 新建, 1: 编辑
            id:'' // 当前编辑项的ID
        }
    },
    methods:{
        // 渲染数据 分页
        initData(){
            let data = {
                leaderSapNo: this.formInline.leaderSapNo,
                offset:this.pageSize,
                rows:this.currentPage,
            }
            // TODO: 替换为实际的查询API
            this.$http.queryCecPage(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.tableData = res.data.data;
                    this.total = res.data.count;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },

        // 新建按钮点击
        addBtn() {
            this.dialogAdd = true;
            this.dialogTitle = 0;
            this.ruleAddForm.leaderSapNo = '';//清空
            this.id = ''; // 清除可能存在的ID
        },

        // table编辑按钮点击
        handleEdit(item) {
            this.dialogAdd = true;
            this.dialogTitle = 1;
            this.ruleAddForm.leaderSapNo = item.leaderSapNo; // 回填数据
            this.id = item.id; // 记录ID
        },

        // 添加/编辑主管SAP确定
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if(this.dialogTitle == 0){ // 新建
                        let data = {
                          leaderSapNo: this.ruleAddForm.leaderSapNo
                        }
                        // TODO: 替换为实际的新增API
                        this.$http.addCec(data,(res)=>{
                            if(res.code == 'SUCCESS'){
                                this.dialogAdd = false;
                                this.$refs[formName].resetFields();
                                this.initData() // 刷新列表
                                this.$message.success(res.message);
                            }else{
                                this.$message.error(res.message);
                            }
                        },(errRes)=>{
                            this.$message.error(errRes.message);
                        })
                    }else{ // 编辑
                        let data = {
                            id: this.id,
                            leaderSapNo: this.ruleAddForm.leaderSapNo
                        }
                        this.$http.updateCec(data,(res)=>{
                            if(res.code == 'SUCCESS'){
                                this.dialogAdd = false;
                                this.$refs[formName].resetFields();
                                this.initData() // 刷新列表
                                this.$message.success(res.message);
                            }else{
                                this.$message.error(res.message);
                            }
                        },(errRes)=>{
                            this.$message.error(errRes.message);
                        })
                    }
                } else {
                    return false;
                }
            });
        },

        // 取消按钮点击 / Dialog 关闭
        resetForm(formName){
            this.$refs[formName].resetFields();
            this.dialogAdd = false;
        },

        // table删除按钮点击
        handleDelete(id) {
            this.$confirm('确认删除？','提示',{
                type: 'warning',
            }).then(() => {
                let data = {
                    id:id
                }
                 // TODO: 替换为实际的删除API
                this.$http.deleteCec(data,(res)=>{
                    if(res.code == 'SUCCESS'){
                        this.initData(); // 刷新列表
                        this.$message.success(res.message);
                    }else{
                        this.$message.error(res.message);
                    }
                },(errRes)=>{
                    this.$message.error(errRes.message);
                })
            }).catch(() => {
                // 用户取消删除
            });
        },
    },
    mounted(){
        this.initData(); // 组件加载时初始化数据
    }
}
</script>
<style scoped>
/* 可以添加或修改样式 */
.supervisorSapBox {
    padding: 20px;
}
.searchFrom {
    margin-top: 20px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
}
.tableContent {
    margin-top: 20px;
}
table {
    width: 100%;
    border-collapse: collapse;
}
th, td {
    border: 1px solid #ebeef5;
    padding: 12px 10px;
    text-align: left;
}
th {
    background-color: #f5f7fa;
    color: #909399;
}
.paginationBox {
    margin-top: 20px;
    text-align: right;
}
</style>
