<template>
    <div class="shiftBox">
        <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>考勤设置</el-breadcrumb-item>
            <el-breadcrumb-item>班次管理</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
                <el-form-item label="班次名称：" prop="name">
                    <el-input v-model="formInline.name" placeholder="请输入班次名称"></el-input>
                </el-form-item>
                <!-- <el-form-item label="有效期：" prop="dataTime">
                    <el-date-picker
                        v-model="formInline.dataTime"
                        type="daterange"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd"
                        range-separator="~"
                        :clearable = false>
                    </el-date-picker>
                </el-form-item> -->
                <el-form-item>
                    <el-button type="primary" @click="initData(currentPage = 1)">查询</el-button>
                    <el-button type="primary" @click="addBtn">新建</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableContent">
            <table>
                <thead>
                    <tr>
                        <th>班次名称</th>
                        <th>创建人</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="tableData.length <= 0">
                        <td colspan="4">暂无数据!</td>
                    </tr>
                    <tr v-for="(item,index) in tableData" :key='index' v-else>
                        <td>{{item.name}}</td>
                        <td>{{item.createdBy}}</td>
                        <td>{{item.createdTime}}</td>
                        <td>
                            <el-button type="text" @click="handleEdit(item)">编辑</el-button>
                            <el-button type="text" @click="handleDelete(item.id)">删除</el-button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- table end -->

        <!-- 分页 start -->
        <div class="paginationBox">
            <el-pagination v-show="total!=0" background layout="total, prev, pager, next, sizes, jumper" :total="total" :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData" @current-change="initData"></el-pagination>
        </div>
        <!-- 分页 end -->

        <!-- 新建班次dialog start -->
        <el-dialog
            :title="dialogTitle == 0 ? '新建班次' : '编辑班次'"
            :visible.sync="dialogAdd"
            width="60%"
            :close-on-click-modal='false'
            @close="resetForm('ruleAddForm')"
            >
            <el-form :model="ruleAddForm" :rules="rulesAdd" ref="ruleAddForm" label-width="120px" class="demo-ruleForm">
                <el-form-item label="班次名称：" prop="name">
                    <el-input v-model="ruleAddForm.name" placeholder="请输入班次名称"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleAddForm')">确定</el-button>
                    <el-button @click="resetForm('ruleAddForm')">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 新建班次dialog end -->
    </div>
</template>
<script>
export default {
    data(){
        return{
            // 头部查询
            formInline: {
                name:'',
                // dataTime:''
            },
            // 表格
            tableData:[],
            // 分页
            currentPage:1,
            pageSize:10,
            total:0,
            dialogAdd:false,//新建班次dialog
            // 新建班次Form
            ruleAddForm:{
                name:''
            },
            rulesAdd:{
                name: [
                    { required: true, message: '请输入班次名称', trigger: 'blur' }
                ]
            },
            dialogTitle:'',
            id:''
        }
    },
    methods:{
        // 渲染数据 分页
        initData(){
            let data = {
                name: this.formInline.name,
                offset:this.pageSize,
                rows:this.currentPage,
            }
            this.$http.queryWorkTypePage(data,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    this.tableData = res.data.data;
                    this.total = res.data.count;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // 新建
        addBtn() {
            this.dialogAdd = true;
            this.dialogTitle = 0;
            this.ruleAddForm.name = '';//清空
        },

        // table编辑
        handleEdit(item) {
            this.dialogAdd = true;
            this.dialogTitle = 1;
            this.ruleAddForm.name = item.name;
            this.id = item.id;
        },

        // 添加/编辑班次确定
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if(this.dialogTitle == 0){
                        let data = {
                            name: this.ruleAddForm.name
                        }
                        this.$http.addWorkType(data,(res)=>{
                            
                            if(res.code == 'SUCCESS'){
                                this.dialogAdd = false;
                                this.$refs[formName].resetFields();
                                this.initData()
                                this.$message.success(res.message);
                            }else{
                                this.$message.error(res.message);
                            }
                        },(errRes)=>{
                            
                            this.$message.error(errRes.message);
                        })
                    }else{
                        let data = {
                            id: this.id,
                            name: this.ruleAddForm.name
                        }
                        this.$http.updateWorkType(data,(res)=>{
                            
                            if(res.code == 'SUCCESS'){
                                this.dialogAdd = false;
                                this.$refs[formName].resetFields();
                                this.initData()
                                this.$message.success(res.message);
                            }else{
                                this.$message.error(res.message);
                            }
                        },(errRes)=>{
                            
                            this.$message.error(errRes.message);
                        })
                    }
                } else {
                    return false;
                }
            });
        },

        // 取消
        resetForm(formName){
            this.$refs[formName].resetFields();
            this.dialogAdd = false;
        },

        // table删除
        handleDelete(id) {
            this.$confirm('确认删除？','提示',{
                type: 'warning',
            }).then(() => {
                let data = {
                    id:id
                }
                this.$http.deleteWorkType(data,(res)=>{
                    
                    if(res.code == 'SUCCESS'){
                        this.initData();
                        this.$message.success(res.message);
                    }else{
                        this.$message.error(res.message);
                    }
                },(errRes)=>{
                    
                    this.$message.error(errRes.message);
                })
            }).catch(() => {

            });
        },
    },
    mounted(){
        this.initData();
    }
}
</script>