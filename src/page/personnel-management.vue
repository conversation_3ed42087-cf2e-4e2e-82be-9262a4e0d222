<template>
    <div class="permissionBox">
       <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/permission-management' }">权限管理</el-breadcrumb-item>
            <el-breadcrumb-item>人员管理</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-button type="primary" @click="importBtn">导入</el-button>
            <el-button type="primary" @click="exportBtn">导出</el-button>
            <el-button type="primary" @click="addBtn">新增</el-button>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableContent">
            <table>
                <thead>
                    <tr>
                        <th>工号</th>
                        <th>姓名</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="tableData.length <= 0">
                        <td colspan="3">暂无数据!</td>
                    </tr>
                    <tr v-for="(item,index) in tableData" :key='index' v-else>
                        <td>{{item.empId}}</td>
                        <td>{{item.name}}</td>
                        <td>
                            <el-button type="text" @click="handleDelete(item.empId)">删除</el-button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- table end -->

        <!-- 分页 start -->
        <div class="paginationBox">
            <el-pagination v-show="total!=0" background layout="total, prev, pager, next, sizes, jumper" :total="total" :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData" @current-change="initData"></el-pagination>
        </div>
        <!-- 分页 end -->

        <!-- 新增dialog start -->
        <el-dialog
            title="新增"
            :visible.sync="dialogAdd"
            width="60%"
            :close-on-click-modal='false'
            @close="resetForm('ruleAddForm')"
            >
            <el-form :model="ruleAddForm" :rules="rulesAdd" ref="ruleAddForm" label-width="120px" class="demo-ruleForm">
                <el-form-item label="工号：" prop="workNum">
                        <el-select 
                            v-model="ruleAddForm.workNum" 
                            filterable 
                            placeholder="请输入工号的4位尾数" 
                            :filter-method="filterMethod"
                             style="width:100%;">
                            <el-option
                                v-for="item in workOptions"
                                :key="item.empId"
                                :label="item.empId"
                                :value="item.empId">
                            </el-option>
                        </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button @click="resetForm('ruleAddForm')">取消</el-button>
                    <el-button type="primary" @click="submitForm('ruleAddForm')">确定</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 新增dialog end -->

        <!-- 导入dialog start -->
        <el-dialog
            title="导入"
            :visible.sync="dialogImport"
            width="60%"
            :close-on-click-modal='false'
            @close="cancelUpload()"
            >
            <el-form label-width="100px" class="demo-ruleForm">
                <el-form-item label="人员管理：">
                    <el-upload
                        class="upload-demo"
                        ref="uploadExcel"
                        action="/api/admin/roleMenuController/importRoleEmployee"
                        :headers="header"
                        :data="{roleNumber:rnumber}"
                        :limit="limitNum"
                        accept=".XLS,.xlsx"
                        :file-list="fileList"
                        :before-upload="beforeFileUpload"
                        :on-exceed="exceedFile"
                        :on-success="handleFileSuccess"
                        :auto-upload="false"
                        :on-change="handleChange">
                        <el-button slot="trigger" type="text">选择文件</el-button>
                        <div slot="tip" class="el-upload__tip el-icon-info">导入文件格式为Excel</div>
                    </el-upload>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitUpload()">确定</el-button>
                    <el-button @click="cancelUpload()">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 导入dialog end -->
    </div>
</template>
<script>
var axios = require('axios')// 引用axios
import utils from '../utils/index.js'//引用常用工具文件
export default {
    data(){
        return{
            // 表格
            tableData:[],
            // 分页
            currentPage:1,
            pageSize:10,
            total:0,
            dialogAdd:false,//添新增dialog
            // 新增Form
            ruleAddForm:{
                workNum:''
            },
            rulesAdd:{
                workNum: [
                    { required: true, message: '请输入工号', trigger: ['blur','change'] }
                ]
            },
            workOptions:[], 
            rnumber:'',
            // 导入dialog
            dialogImport:false,
            limitNum: 1,
            fileList: [],
            header:{
                'Authorization' : localStorage.getItem('Auth-Token')
            }
        }
    },
    methods:{
        // 渲染数据 菜单列表
        initData(){
            let data = {
                offset:this.pageSize,
                rows:this.currentPage,
                roleNumber:this.rnumber
            }
            this.$http.queryEmployeeByRole(data,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    this.tableData = res.data.data;
                    this.total = res.data.count;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // 新增
        addBtn() {
            this.dialogAdd = true;
            this.ruleAddForm = {
                workNum:''
            }
        },

        // 新增确定
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let data = {
                        roleNumber: this.rnumber,
                        empId: this.ruleAddForm.workNum
                    }
                    this.$http.addEmployeeByRole(data,(res)=>{
                        
                        if(res.code == 'SUCCESS'){
                            this.dialogAdd = false;
                            this.$refs[formName].resetFields();
                            this.initData()
                            this.$message.success(res.message);
                        }else{
                            this.$message.error(res.message);
                        }
                    },(errRes)=>{
                        
                        this.$message.error(errRes.message);
                    })
                } else {
                    return false;
                }
            });
        },

        // 取消
        resetForm(formName){
            this.$refs[formName].resetFields();
            this.dialogAdd = false;
        },

        // table删除
        handleDelete(empId) {
            this.$confirm('确认删除？','提示',{
                type: 'warning',
            }).then(() => {
                let data = {
                    roleNumber: this.rnumber,
                    empId: empId
                }
                this.$http.deleteEmployeeByRole(data,(res)=>{
                    
                    if(res.code == 'SUCCESS'){
                        this.initData();
                        this.$message.success(res.message);
                    }else{
                        this.$message.error(res.message);
                    }
                },(errRes)=>{
                    
                    this.$message.error(errRes.message);
                })
            }).catch(() => {

            });
        },

        filterMethod(query){
            if(query.length >= 4){
                this.workList(query);
            }
        },

        // 工号 下拉选
        workList(val){
            let data = {
                empId:val
            }
            this.$http.queryEmployeeByWorkTeam(data,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    this.workOptions = res.data.data;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // 导入
        importBtn(){
            this.dialogImport = true;
        },
        // 上传文件之前的钩子，参数为上传的文件,若返回 false 或者返回 Promise 且被 reject，则停止上传
        beforeFileUpload (file){
            const extension = file.name.split('.')[1];
            if ((extension !== 'XLS') && (extension !== 'xlsx')) {
                this.$message.warning('上传模板只能是 XLS、xlsx格式!')
                return false
            }else{

            }
        },
        // 文件超出个数限制时的钩子
        exceedFile(files, fileList) {
            this.$message.warning({
                title: '警告',
                message: `只能选择 ${this.limitNum} 个文件，当前共选择了 ${files.length + fileList.length} 个`
            });
        },
        // 上传成功后的钩子
        handleFileSuccess(res, file) {
            if(res.code == 'SUCCESS'){
                this.dialogImport = false;//关闭弹框
                this.initData();
                this.$message.success(res.message);  
            }else{
                this.$message.error(res.message);
            }
        },
        // 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用
        handleChange(file, fileList) {
            this.fileList = fileList;
        },

        // 确定
        submitUpload(){
            if(this.fileList == ''){
                this.$message.error("请上传文件");
            }else{
                this.$refs.uploadExcel.submit();
            }
        },
        // 取消
        cancelUpload(){
            this.fileList = [];
            this.dialogImport = false;
        },

        // 导出
        exportBtn(){
            let data = {
                offset:this.pageSize,
                rows:this.currentPage,
                roleNumber:this.rnumber
            }
            axios({
                url: '/api/admin/roleMenuController/downloadRoleEmployee',
                method: "POST",
                data:data,
                responseType:"blob",
                headers:{
                    // 'Content-Type':'application/json;charset=UTF-8',
                    'Authorization' : localStorage.getItem('Auth-Token')
                },
            }).then(function (res) {
                if(!res){
                    return
                }
                utils.downloadExcel(res.data,'人员管理导出 ','xlsx')//调用公共方法
            })
        },
    },
    mounted(){
        this.rnumber = this.$route.query.rnumber;
        this.initData();
    }
}
</script>
