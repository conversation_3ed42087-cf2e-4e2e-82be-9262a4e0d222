<template>
    <div class="calendarBox">
       <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>考勤设置</el-breadcrumb-item>
            <el-breadcrumb-item>工作日历管理</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <div class="initBtn">
            <el-button type="primary" @click="initData" :disabled="disabled">生成下一年</el-button>
        </div>

        <div class="initBtn">
            <el-date-picker
                v-model="nowDate"
                type="month"
                placeholder="选择月份"
                value-format="yyyy-MM"
                :clearable = false
                @change="queryMonth"
                >
            </el-date-picker>
        </div>

        <!-- 工作日历 start -->
        <div id="data">
            <!-- <el-calendar v-model="value">
                <template
                    slot="dateCell"
                    slot-scope="{data}">
                    <div class="divData">{{data.day.split('-').pop()}}</div>
                    <p class="pChange"> 
                        <span class="work" :class="isActive == data.day+'0' ? 'workActive' : ''" @click="getData(data.day+0)">工作</span>
                        <span class="relax" :class="isActive == data.day+'1' ? 'relaxActive' : ''" @click="getData(data.day+1)">休息</span>
                        <span class="vacation" :class="isActive == data.day+'2' ? 'vacationActive' : ''" @click="getData(data.day+2)">国假</span>
                    </p>
                </template>
            </el-calendar> -->
            
            <!-- <full-calendar class="test-fc"
                :config="config"
                :events="monthData"
                first-day='1' locale="fr"
                @changeMonth="changeMonth"
                @eventClick="eventClick"   
                @dayClick="dayClick"
                @moreClick="moreClick">
            </full-calendar> --> 

            <ul id="title">
                <li>Su</li>
                <li>Mo</li>
                <li>Tu</li>
                <li>We</li>
                <li>Th</li>
                <li>Fr</li>
                <li>Sa</li>
            </ul>
            <ul id="date">
                <li v-for="list in dataEmpty"></li>
                <li v-for="(item,index) in dataList" :key='index'>
                    <span class="divData">{{item.day}}</span><br/>
                    <span class="pChange">
                        <i class="work" :class="item.type == 1 ? 'workActive' : ''" @click="changeData(item,index,1)">工作</i>
                        <i class="relax" :class="item.type == 2 ? 'relaxActive' : ''" @click="changeData(item,index,2)">休息</i>
                        <i class="vacation" :class="item.type == 3 ? 'vacationActive' : ''" @click="changeData(item,index,3)">国假</i>
                    </span>
                </li>
            </ul>
        </div>
        <!-- 工作日历 end -->

        <div class="initBtn">
            <el-button type="primary" @click="modifyCalendar">保存</el-button>
        </div>
    </div>
</template>
<script>
export default {
    data(){
        return{
            disabled:false,//初始化按钮状态
            nowDate:'',
            dataList:[],
            dataEmpty:[],//空的li
            modifyData:[],//修改集合全部
            modifyArr:[]//修改集合去重
        }
    },
    methods:{
        // 初始化本年的周末与休息日
        initData(){
            this.disabled = true;
            this.$http.initWorkCalendar(null,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.disabled = false;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },

        // 查询月份的工作日历
        queryMonth(){
            this.dataEmpty = [];//清空空的li
            let data = {
                workDate:this.nowDate
            }
            this.$http.queryWorkCalendarList(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    for(let i = 0;i<res.data.length;i++){
                        res.data[i]["day"] = res.data[i].workDate.split('-')[2];
                    }
                    this.dataList = res.data;
                    var datas = res.data[0].workDate.split('-');
                    var years = datas[0];
                    var months = datas[1];
                    var setZhou = new Date(years,months-1,1).getDay(); //获取当前月第一天 是 周几
                    for(let m = 0;m<=setZhou-1;m++){
                        this.dataEmpty.push({})
                    }
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },

        // 批量修改工作日历
        modifyCalendar(){
            let data = {
                dataList:this.modifyArr
            }
            this.$http.updateWorkCalendarList(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.queryMonth();
                    this.$message.success(res.message);
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },
        
        // 修改样式
        changeData(item,index,num){
            this.dataList[index].type = num;//切换样式
            var modifyObj = {
                id:item.id,
                type:num,
                workDate:item.workDate
            }
            this.modifyData.push(modifyObj)
            this.sortArr(this.modifyData, 'workDate');
        },

        // 按照特定方式格式化
        sortArr(arr, str) {
            var _arr = [],
                _t = [],
                // 临时的变量
                _tmp;
        
            // 按照特定的参数将数组排序将具有相同值得排在一起
            arr = arr.sort(function(a, b) {
                var s = a[str],
                    t = b[str];
        
                return s < t ? -1 : 1;
            });
        
            if ( arr.length ){
                _tmp = arr[0][str];
            }

            // 将相同类别的对象添加到统一个数组
            for (var i in arr) {
                if ( arr[i][str] === _tmp ){
                    _t.push( arr[i] );
                } else {
                    _tmp = arr[i][str];
                    _arr.push( _t );
                    _t = [arr[i]];
                }
            }
            // 将最后的内容推出新数组
            _arr.push( _t );
            var newArr = [];
            for(let m = 0;m<_arr.length;m++){
                let mArr = _arr[m];
                newArr.push(mArr[mArr.length-1])
            }
            this.modifyArr = newArr;
        }
    },
    mounted(){
        var datD = new Date(); //当前格林时间 
        var nianD = datD.getFullYear();//当前年份 
        var yueD = datD.getMonth()+1; //当前月 （需要加1）
        // var tianDtianD = datD.getDate(); //当前天 这保存的年月日 是为了 当到达当前日期 有对比  
        var newYue = (yueD > 9) ? yueD : '0'+yueD;
        this.nowDate = nianD+ '-'+newYue;
        this.queryMonth()
    }
}
</script>
