<template>
  <div class="workforceBox">
    <!-- 面包屑 start -->
    <el-breadcrumb separator="/">
      <el-breadcrumb-item>考勤设置</el-breadcrumb-item>
      <el-breadcrumb-item>排班管理</el-breadcrumb-item>
    </el-breadcrumb>
    <!-- 面包屑 end -->

    <!-- search start -->
    <div class="searchFrom">
      <el-form
        :inline="true"
        :model="formInline"
        ref="formInline"
        class="demo-form-inline"
      >
        <el-form-item label="时间：" prop="fmonth">
          <!-- <el-form-item prop="fyear">
												<el-date-picker
														v-model="formInline.fyear"
														type="year"
														placeholder="请选择年份"
														:clearable = false>
												</el-date-picker>
										</el-form-item> -->
          <!-- <el-form-item prop="fmonth"> -->
          <el-date-picker
            v-model="formInline.fmonth"
            type="month"
            placeholder="请选择年月"
            value-format="yyyy-MM"
            :clearable="false"
          >
          </el-date-picker>
          <!-- </el-form-item> -->
        </el-form-item>
        <!-- <el-form-item label="姓名：" prop="name">
										<el-input v-model="formInline.name" placeholder="请输入姓名"></el-input>
								</el-form-item>
								<el-form-item label="工号：" prop="number">
										<el-input v-model="formInline.number" placeholder="请输入工号"></el-input>
								</el-form-item>
								<el-form-item label="生产线：" prop="productLine">
										<el-input v-model="formInline.productLine" placeholder="请输入生产线"></el-input>
								</el-form-item> -->
        <!-- <el-form-item label="部门：" prop="department">
										<el-input v-model="formInline.department" placeholder="请输入部门"></el-input>
								</el-form-item> -->
        <el-form-item label="班组：" prop="team">
          <!-- <el-input
						v-model="formInline.team"
						placeholder="请输入班组"
					></el-input> -->
          <el-select
            v-model="formInline.team"
            filterable
            placeholder="请选择班组"
          >
            <el-option
              v-for="item in teamOption"
              :key="item.id"
              :label="item.workTeam"
              :value="item.workTeam"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="initData((currentPage = 1))"
            >查询</el-button
          >
          <el-button @click="resetForm('formInline')">重置</el-button>
        </el-form-item>
        <el-form-item>
          <el-button @click="importBtn">导入</el-button>
          <el-button @click="exportBtn">导出排班</el-button>
          <el-button @click="exportEmployeeBtn">导出员工排班</el-button>
          <el-button @click="ruleBtn">排班规则</el-button>
          <!-- <el-button @click="initWorkforce" :disabled="disabled"
            >自动排班</el-button
          > -->
        </el-form-item>
      </el-form>
    </div>
    <!-- search end -->

    <div class="tableContant">
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        v-loading="loading"
      >
        <!-- <el-table-column fixed prop="plant" label="plant"></el-table-column>
				<el-table-column
					fixed
					prop="workshop"
					label="workshop"
				></el-table-column> -->
        <el-table-column fixed prop="workTeam" label="班组">
          <template slot-scope="scope">
            <span @click="bindModify(scope.row)">{{ scope.row.workTeam }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-for="(item, index) in dateLength"
          :key="index"
          :label="item"
          :prop="item"
        >
          <template slot-scope="scope">
            <span
              :class="{
                tdActive:
                  scope.row[item].isWeek == 0 || scope.row[item].isWeek == 6,
              }"
              @click="workforceEdit(scope.row, item)"
              >{{ scope.row[item].work_type }}</span
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- table start -->
    <!-- <div class="tableFixed">
			<table class="tb1">
				<thead>
					<tr v-if="tableData.length > 0">
						<th>plant</th>
						<th>workshop</th>
						<th>班组</th>
					</tr>
				</thead>
			</table>
			<table class="tb2">
				<thead>
					<tr v-if="tableData.length > 0">
						<th v-for="(m, index2) in dateLength" :key="index2">
							{{ m.schedule_date }}
						</th>
					</tr>
				</thead>
			</table>
			<table class="tb3">
				<tbody>
					<tr v-for="(item, index3) in tableData" :key="index3">
						<td>{{ item.plant }}</td>
						<td>{{ item.workshop }}</td>
						<td @click="bindModify(item)">{{ item.workTeam }}</td>
					</tr>
				</tbody>
			</table>
			<table class="tb4">
				<tbody>
					<tr v-for="(item, index4) in tableData" :key="index4">
						<td :colspan="dateLength.length" v-if="item.dateList == null">
							&nbsp;&nbsp;&nbsp;
						</td>
						<td
							v-for="(n, index6) in item.dateList"
							:key="index6"
							:class="{ tdActive: n.isWeek == 0 || n.isWeek == 6 }"
							@click="workforceEdit(item, n)"
							v-else
						>
							{{ n.work_type }}
						</td>
					</tr>
				</tbody>
			</table>
		</div> -->
    <!-- table end -->

    <!-- 分页 start -->
    <div class="paginationBox">
      <el-pagination
        v-show="total != 0"
        background
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        @size-change="initData"
        @current-change="initData"
      ></el-pagination>
    </div>
    <!-- 分页 end -->

    <!-- 编辑dialog start -->
    <el-dialog
      title="编辑"
      :visible.sync="dialogEdit"
      width="60%"
      :close-on-click-modal="false"
      @close="resetFormE('ruleForm')"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="120px"
        class="demo-ruleForm"
      >
        <!-- <el-form-item label="姓名：">
										{{ruleForm.name}}
								</el-form-item> -->
        <el-form-item label="日期：">
          {{ ruleForm.dateTime }}
        </el-form-item>
        <el-form-item label="班次：" prop="shift">
          <!-- <el-autocomplete
												v-model="ruleForm.shift"
												placeholder="请选择班次"
												:fetch-suggestions="querySearch"
												:trigger-on-focus="false"
												@select="handleSelect"
												></el-autocomplete> -->
          <el-select
            v-model="ruleForm.shift"
            filterable
            placeholder="请选择班次"
            style="width: 100%"
          >
            <el-option
              v-for="item in shiftOptions"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm('ruleForm')"
            >确定</el-button
          >
          <el-button @click="resetFormE('ruleForm')">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 编辑dialog end -->

    <!-- 导入dialog start -->
    <el-dialog
      title="导入"
      :visible.sync="dialogImport"
      width="60%"
      :close-on-click-modal="false"
      @close="cancelUpload()"
    >
      <el-form label-width="150px" class="demo-ruleForm">
        <el-form-item label="排班管理：">
          <el-upload
            class="upload-demo"
            ref="uploadExcel"
            action="/api/admin/scheduleController/importSchedule"
            :headers="header"
            :limit="limitNum"
            accept=".XLS,.xlsx"
            :file-list="fileList"
            :before-upload="beforeFileUpload"
            :on-exceed="exceedFile"
            :on-success="handleFileSuccess"
            :auto-upload="false"
            :on-change="handleChange"
          >
            <el-button slot="trigger" type="text">选择文件</el-button>
            <div slot="tip" class="el-upload__tip el-icon-info">
              导入文件格式为Excel
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitUpload()">确定</el-button>
          <el-button @click="cancelUpload()">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 导入dialog end -->

    <!-- 导出员工排班dialog start -->
    <!-- <el-dialog
            title="导出员工排班"
            :visible.sync="dialogEmployee"
            width="60%"
            :close-on-click-modal='false'
            @close="resetFormEmp('ruleEmpForm')"
            >
            <el-form :model="ruleEmpForm" :rules="rulesEmp" ref="ruleEmpForm" label-width="100px" class="demo-ruleForm">
                <el-form-item label="厂区：" prop="factory">
                    <el-select v-model="ruleEmpForm.factory" placeholder="请选择厂区" style="width:100%;">
                        <el-option
                            v-for="item in factoryOption"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="车间：" prop="workshop">
                    <el-select v-model="ruleEmpForm.workshop" filterable placeholder="请输入并选择车间" style="width:100%;">
                        <el-option
                            v-for="item in workshopOption"
                            :key="item.workshop +'_'+item.plant"
                            :label="item.workshop"
                            :value="item.workshop">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitFormEmp('ruleEmpForm')">确定</el-button>
                    <el-button @click="resetFormEmp('ruleEmpForm')">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog> -->
    <!-- 导出员工排班dialog end -->
  </div>
</template>
<script>
var axios = require("axios"); // 引用axios
import utils from "../utils/index.js"; //引用常用工具文件
import dayjs from "dayjs";
export default {
  data() {
    // 班次验证
    var checkShift = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请选择班次!"));
      } else {
        callback();
      }
    };
    return {
      // 头部查询
      formInline: {
        // fyear:'',
        fmonth: "",
        // name:'',
        // number:'',
        // productLine:'',
        // department:'',
        team: "",
      },
      teamOption: [],
      // 表格
      tableData: [],
      // 分页
      currentPage: 1,
      pageSize: 10,
      total: 0,
      dialogEdit: false, //编辑dialog
      ruleForm: {
        // name:'11',
        dateTime: "",
        shift: "",
      },
      rules: {
        shift: [{ validator: checkShift, trigger: "blur" }],
      },
      // restaurants: [],//班次模糊查询列表
      shiftOptions: [],
      disabled: false, //初始化按钮状态
      scheduleDate: "",
      plant: "",
      workTeam: "",
      workshop: "",
      // 导入dialog
      dialogImport: false,
      limitNum: 1,
      fileList: [],
      header: {
        Authorization: localStorage.getItem("Auth-Token"),
      },
      dateLength: [],
      loading: false,

      // dialogEmployee:false,
      // ruleEmpForm:{
      //     factory:'',
      //     workshop:''
      // },
      // rulesEmp:{
      //     factory: [
      //         { required: true, message: '请选择厂区', trigger: 'change' }
      //     ],
      // },
      // factoryOption:[],
      // workshopOption:[]
    };
  },
  methods: {
    // 初始化当月排班
    initWorkforce() {
      this.disabled = true;
      this.$http.initPlanSchedule(
        null,
        (res) => {
          if (res.code == "SUCCESS") {
            this.disabled = false;
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 重置
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.currentPage = 1;
      this.initData();
    },

    // 渲染数据 分页
    initData() {
      this.tableData = [];
      this.dateLength = [];

      this.loading = true;

      let data = {
        month:
          this.formInline.fmonth == null
            ? ""
            : this.formInline.fmonth.split("-")[1],
        plant: "",
        workTeam: this.formInline.team,
        workType: "",
        workshop: "",
        year:
          this.formInline.fmonth == null
            ? ""
            : this.formInline.fmonth.split("-")[0],
        offset: this.pageSize,
        rows: this.currentPage,
        empId: "",
        scheduleDate: "",
      };
	  
      let myYear = data.year;
      let myMonth = data.month;
      let startDate = "";
      let endDate = "";
      if (data.month == 1) {
        myYear -= 1;
        myMonth = 12;
        startDate = myYear + "-" + myMonth + "-" + "16";
        endDate = data.year + "-" + data.month + "-" + "16";
      } else {
        startDate = data.year + "-" + (data.month - 1) + "-" + "16";
        endDate = data.year + "-" + data.month + "-" + "16";
      }

      let currentDay = startDate;
      let dateArray = [];
      do {
        dateArray.push(dayjs(currentDay).format("YYYY-MM-DD"));
        currentDay = dayjs(currentDay).add(1, "day");
      } while (dayjs(currentDay).isBefore(dayjs(endDate)));
	  
      this.dateLength = dateArray;

      this.$http.queryPlanSchedulePage(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            var resData = res.data.data;

            var lengthArr = []; //每个dateList
            for (let i = 0; i < resData.length; i++) {
              if (resData[i].dateList != null) {
                lengthArr.push(resData[i].dateList); //日期

                // 排班标识
                for (let j = 0; j < resData[i].dateList.length; j++) {
                  var scheduleDate = resData[i].dateList[j].schedule_date;
                  var isWeek = new Date(scheduleDate).getDay(); //0-周日，6-周六
                  resData[i].dateList[j]["isWeek"] = isWeek;

                  resData[i][scheduleDate] = {
                    work_type: resData[i].dateList[j].work_type,
                    isWeek: isWeek,
                  };
                }
              }
            }
            this.tableData = resData;
            this.total = res.data.count;
          } else {
            this.$message.error(res.message);
          }
          this.loading = false;
        },
        (errRes) => {
          this.loading = false;
          this.$message.error(errRes.message);
        }
      );
    },

    // 导入
    importBtn() {
      this.dialogImport = true;
    },
    // 上传文件之前的钩子，参数为上传的文件,若返回 false 或者返回 Promise 且被 reject，则停止上传
    beforeFileUpload(file) {
      const extension = file.name.split(".")[1];

      if (extension !== "XLS" && extension !== "xlsx") {
        this.$message.warning("上传模板只能是 XLS、xlsx格式!");
        return false;
      } else {
      }
    },
    // 文件超出个数限制时的钩子
    exceedFile(files, fileList) {
      this.$message.warning({
        title: "警告",
        message: `只能选择 ${this.limitNum} 个文件，当前共选择了 ${
          files.length + fileList.length
        } 个`,
      });
    },
    // 上传成功后的钩子
    handleFileSuccess(res, file) {
      if (res.code == "SUCCESS") {
        this.dialogImport = false; //关闭弹框
        this.initData();
        this.$message.success(res.message);
      } else {
        this.$message.error(res.message);
      }
    },
    // 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用
    handleChange(file, fileList) {
      this.fileList = fileList;
    },

    // 确定
    submitUpload() {
      if (this.fileList == "") {
        this.$message.error("请上传文件");
      } else {
        this.$refs.uploadExcel.submit();
      }
    },
    // 取消
    cancelUpload() {
      this.fileList = [];
      this.dialogImport = false;
    },

    // 导出
    exportBtn() {
      let data = {
        month: this.formInline.fmonth.split("-")[1],
        workTeam: this.formInline.team,
        year: this.formInline.fmonth.split("-")[0],
        offset: this.pageSize,
        rows: this.currentPage,
        empId: "",
        plant: "",
        scheduleDate: "",
        workType: "",
      };
      axios({
        url: "api/admin/scheduleController/downloadSchedule",
        method: "POST",
        data: data,
        responseType: "blob",
        headers: {
          // 'Content-Type':'application/json;charset=UTF-8',
          Authorization: localStorage.getItem("Auth-Token"),
        },
      }).then(function (res) {
        if (!res) {
          return;
        }
        utils.downloadExcel(res.data, "排班管理 ", "xlsx"); //调用公共方法
      });
    },

    // 排班规则
    ruleBtn() {
      this.$router.push({ path: "/scheduling-rules" });
    },

    // 班次 下拉选
    shiftList() {
      this.$http.queryWorkTypeAll(
        null,
        (res) => {
          if (res.code == "SUCCESS") {
            // this.restaurants = res.data.data;
            this.shiftOptions = res.data.data;
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // // 返回输入建议的方法，仅当你的输入建议数据 resolve 时，通过调用 callback(data:[]) 来返回它
    // querySearch(queryString, cb) {
    //     var restaurants = this.restaurants;
    //     // 因为autocomplete只识别value字段并在下拉列中显示，添加value字段
    //     for(let i=0; i<restaurants.length; i++){
    //         restaurants[i]["value"] = restaurants[i].name;
    //     }
    //     var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
    //     // 调用 callback 返回建议列表的数据
    //     cb(results);
    // },

    // createFilter(queryString) {
    //     return (restaurant) => {
    //         return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
    //     };
    // },

    // // 在 Input 值改变时触发
    // handleSelect(item) {
    //     console.log(item);
    // },

    // 编辑班次
    workforceEdit(item, n) {
      this.dialogEdit = true;
      this.ruleForm.dateTime = n.slice(0, n.lastIndexOf("-"));
      this.ruleForm.shift = item[n].work_type;
      this.scheduleDate = n;
      this.plant = item.plant;
      this.workTeam = item.workTeam;
      this.workshop = item.workshop;
    },

    // 编辑确定
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = {
            plant: this.plant,
            scheduleDate: this.scheduleDate,
            workTeam: this.workTeam,
            workType: this.ruleForm.shift,
            workshop: this.workshop,
          };
          this.$http.updateInternetWorkTeamSchedule(
            data,
            (res) => {
              if (res.code == "SUCCESS") {
                this.dialogEdit = false;
                this.initData();
                this.$message.success(res.message);
              } else {
                this.$message.error(res.message);
              }
            },
            (errRes) => {
              this.$message.error(errRes.message);
            }
          );
        } else {
          return false;
        }
      });
    },

    // 取消
    resetFormE(formName) {
      this.$refs[formName].resetFields();
      this.dialogEdit = false;
    },

    // 批量修改班次
    bindModify(item) {
      var queryItem = {
        plant: item.plant,
        workTeam: item.workTeam,
        workshop: item.workshop,
      };
      this.$router.push({
        path: "/batch-modification",
        query: {
          item: JSON.stringify(queryItem),
          fmonth: this.formInline.fmonth,
        },
      });
    },

    // 导出员工排班
    exportEmployeeBtn() {
      // this.dialogEmployee = true;
      let data = {
        month: this.formInline.fmonth
          ? this.formInline.fmonth.split("-")[1]
          : "",
        year: this.formInline.fmonth
          ? this.formInline.fmonth.split("-")[0]
          : "",
      };
      axios({
        url: "api/admin/scheduleController/downloadEmployeeSchedule",
        method: "POST",
        data: data,
        responseType: "blob",
        headers: {
          Authorization: localStorage.getItem("Auth-Token"),
        },
      }).then(function (res) {
        if (!res) {
          return;
        }
        utils.downloadExcel(res.data, "员工排班", "xlsx"); //调用公共方法
      });
    },
    // // 确定
    // submitFormEmp(formName) {
    //     var that = this;
    //     that.$refs[formName].validate((valid) => {
    //         if (valid) {
    //             let data = {
    //                 month:that.formInline.fmonth ? that.formInline.fmonth.split('-')[1] : '',
    //                 plant:that.ruleEmpForm.factory,
    //                 workshop:that.ruleEmpForm.workshop,
    //                 year:that.formInline.fmonth ? that.formInline.fmonth.split('-')[0] : ''
    //             }
    //             axios({
    //                 url: 'api/admin/scheduleController/downloadEmployeeSchedule',
    //                 method: "POST",
    //                 data:data,
    //                 responseType:"blob",
    //                 headers:{
    //                     'Authorization' : localStorage.getItem('Auth-Token')
    //                 },
    //             }).then(function (res) {
    //                 if(!res){
    //                     return
    //                 }
    //                 utils.downloadExcel(res.data,'员工排班','xlsx')//调用公共方法
    //                 setTimeout(function(){
    //                     that.dialogEmployee = false;
    //                 },3000)
    //             })
    //         } else {
    //             return false;
    //         }
    //     });
    // },
    // // 取消
    // resetFormEmp(formName){
    //     this.$refs[formName].resetFields();
    //     this.dialogEmployee = false;
    // },

    // // 厂区
    // factoryList(){
    //     this.$http.queryPlantList(null,(res)=>{
    //         if(res.code == 'SUCCESS'){
    //             this.factoryOption = res.data;
    //         }else{
    //             this.$message.error(res.message);
    //         }
    //     },(errRes)=>{

    //         this.$message.error(errRes.message);
    //     })
    // },

    // // 车间
    // workshopList(){
    //     var data = {
    //         plant:''
    //     }
    //     this.$http.queryPlantWorkshopAll(data,(res)=>{
    //         if(res.code == 'SUCCESS'){
    //             this.workshopOption = res.data.data;
    //         }else{
    //             this.$message.error(res.message);
    //         }
    //     },(errRes)=>{

    //         this.$message.error(errRes.message);
    //     })
    // }
    // 班组 下拉选
    teamList() {
      this.$http.queryWorkGroupAll(
        null,
        (res) => {
          if (res.code == "SUCCESS") {
            this.teamOption = res.data.data;
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },
  },
  mounted() {
    var datD = new Date(); //当前格林时间
    var nianD = datD.getFullYear(); //当前年份
    var yueD = datD.getMonth(); //当前月 （需要加1）
    this.formInline.fmonth = nianD + "-" + (yueD + 1);
    this.initData();
    this.shiftList();

    this.teamList();

    // this.factoryList()
    // this.workshopList()
  },
};
</script>
