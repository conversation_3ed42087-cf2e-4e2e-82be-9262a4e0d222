<template>
  <div class="leaveApprovalBox">
    <div class="leaveApprovalDes">
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        class="demo-ruleForm"
      >
        <el-form-item class="formItem defFormItem" label="厂区">
          <div>{{ ruleForm.plant }}</div>
        </el-form-item>
        <el-form-item class="formItem defFormItem" label="部门">
          <div>{{ ruleForm.department }}</div>
        </el-form-item>
        <el-form-item class="formItem defFormItem" label="车间">
          <div>{{ ruleForm.workshop }}</div>
        </el-form-item>
        <el-form-item prop="leaveType" class="formItem defFormItem" label="请假类型">
          <el-select v-model="ruleForm.leaveType" filterable placeholder="请选择"
            @change="selectLeaveType(ruleForm.leaveType)" v-reset-page>
            <el-option v-for="item in leaveTypeOptions" :key="item.leaveTypeId" :label="item.leaveTypeName" :value="item.leaveTypeId +
              '&' +
              item.explains +
              '&' +
              item.leaveTypeName
              ">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="formItem"
          v-show="ruleForm.leaveNotice != '' && ruleForm.leaveNotice != 'null'"
        >
          <div class="leaveNotice">{{ ruleForm.leaveNotice }}</div>
        </el-form-item>
        <el-form-item class="formItem" v-show="ruleForm.leaveNotice != '' &&
          ruleForm.leaveNotice != 'null' &&
          isHolidayBalance == true
          ">
          <div class="leaveNotice">
            剩余：{{ ruleForm.balanceAvailable }} 小时 更新日期：{{
              ruleForm.update_time
            }}
          </div>
        </el-form-item>
        <el-form-item prop="startTime" class="formItem timeFormItem" label="开始时间">
          <el-input type="text" v-model="ruleForm.startTime" placeholder="请选择开始时间" @focus="startShow()"
            readonly="readonly">
          </el-input>
          <i class="el-icon-date"></i>
        </el-form-item>
        <el-form-item
          prop="endTime"
          class="formItem timeFormItem"
          label="结束时间"
        >
          <el-input
            type="text"
            v-model="ruleForm.endTime"
            placeholder="请选择开始时间"
            @focus="endShow()"
            readonly="readonly"
          >
          </el-input>
          <i class="el-icon-date"></i>
        </el-form-item>
        <!-- 时间段选择 start -->
        <div class="timeFormItem">
          <vue-hash-calendar
            ref="picker"
            model="dialog"
            :scroll-change-date="false"
            :visible.sync="isShowAddCalendar"
            :default-datetime="defaultDatetime"
            :is-show-week-view="false"
            format="YY-MM-DD hh:mm:ss"
            week-start="sunday"
            picker-type="datetime"
            :show-today-button="true"
            :disabled-week-view="false"
            @confirm="dateAddConfirm"
            @click="dateAddClick"
            @change="dateAddChange"
            :disabled-date="disabledDate"
            :minuteStep="30"
          >
          </vue-hash-calendar>
        </div>
        <!-- 时间段选择 end -->
        <el-form-item
          prop="leaveCount"
          class="formItem defFormItem"
          label="请假时长"
        >
          <!-- <el-input type="text" v-model="ruleForm.leaveCount" placeholder="请输入请假时长(h)" @blur="getLeaveCount($event)" v-reset-page></el-input> -->
          <el-input
            type="text"
            v-model="ruleForm.leaveCount"
            placeholder=""
            v-reset-page
            disabled
          ></el-input>
        </el-form-item>
        <el-form-item
          prop="reason"
          class="formItem lineFormItem"
          label="请假事由"
        >
          <el-input
            type="textarea"
            v-model="ruleForm.reason"
            placeholder="请输入请假事由"
            v-reset-page
          ></el-input>
        </el-form-item>
        <div class="addFormItem">
          <div class="approvalShowDes">
            <div class="titleImg">图片</div>
            <div class="imgLists">
              <div
                class="imgList emo-image__preview"
                v-for="(item, index) in imgList"
                :key="index"
              >
                <!-- <img :src="item" alt=""> -->
                <el-image :src="item" :preview-src-list="imgList"> </el-image>
                <i
                  class="el-icon-error deleteImg"
                  @click="deleteImg(item, index)"
                ></i>
              </div>
              <el-button @click="upImg"
                ><i class="el-icon-circle-plus-outline"></i
              ></el-button>
            </div>
          </div>
          <div class="approvalProcess" v-show="approvalProcess.length > 0">
            <div class="approvalTitle">审批流程</div>
            <div class="approvalProcessDes">
              <div
                class="approvalProcessList"
                v-for="(item, index) in approvalProcess"
                :key="index"
              >
                <span class="circleShow">{{ index + 1 }}</span
                >{{ item }}
              </div>
            </div>
          </div>
        </div>
        <el-form-item class="formItem bottomBtnBox">
          <el-button
            type="primary"
            @click="submitForm('ruleForm')"
            :disabled="disabled"
            >提交请假申请</el-button
          >
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
import wx from "weixin-js-sdk";
import axios from "axios";
import dayjs from "dayjs";
export default {
  data() {
    return {
      disabled: false,
      ruleForm: {
        department: "", // 部门
        leaveType: "", // 请假类型
        reason: "", // 请假事由
        startTime: "", // 开始时间
        endTime: "", // 结束时间
        leaveCount: "", // 时长
        leaveNotice: "", // 请假类型 简述
        plant: "",
        workshop: "",
        leaveCountDesc: "",
        balanceAvailable: "",
        update_time: "",
        leaveTypeName: ''
      },
      imgList: [], // 多张
      imgShow: "", // 单张
      localIds: [],
      departmentOptions: [], // 部门
      leaveTypeOptions: [], // 请假类型
      approvalProcess: [], // 审批流程
      myStatus: "",
      processinstanceid: "", // 流程实例Id
      rules: {},
      timeStatus: "", // 0 开始时间 1 结束时间
      isShowAddCalendar: false,
      defaultDatetime:  new Date(new Date().setHours(0, 0, 0, 0)),
      isHolidayBalance: false,
    };
  },
  methods: {
    // 选择假别
    selectLeaveType(value) {
      this.leaveTypeName = value.split("&")[2];
      console.log(this.ruleForm.leaveTypeName = value.split("&")[2], 'this.leaveTypeName = value.split("&")[2]');
      this.ruleForm.leaveNotice = value.split("&")[1];
      //this.getLeaveType();
      this.getLeaveCount();

      if (
        value.split("&")[2] !== "全薪病假" &&
        value.split("&")[2] !== "年假" &&
        value.split("&")[2] !== "育儿假" &&
        value.split("&")[2] !== "护理假"
      ) {
        this.isHolidayBalance = false;
      } else {
        this.isHolidayBalance = true;
        this.queryHolidayBalance(value.split("&")[0]);
      }
    },

    // 获取假别
    getLeaveType() {
      this.$http.wxqueryLeaveTypeAll(
        null,
        (res) => {
          if (res.code == "SUCCESS") {
            if (res.status == 0) {
              this.leaveTypeOptions = res.data;
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message ? errRes.message : errRes.msg);
        }
      );
    },

    queryHolidayBalance(value) {
      this.$http.wxQueryHolidayBalance(
        { type: value },
        (res) => {
          if (res.code == "SUCCESS") {
            if (res.status == 0) {
              this.ruleForm.balanceAvailable = res.data.available;
              this.ruleForm.update_time = res.data.update_time;
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message ? errRes.message : errRes.msg);
        }
      );
    },

    // 显示日历 开始时间
    startShow() {
      this.timeStatus = 0;
      this.isShowAddCalendar = true;
    },

    // 显示日历 结束时间
    endShow() {
      this.timeStatus = 1;
      this.isShowAddCalendar = true;
    },

    // 日期改变触发
    dateAddChange(date) {},

    // 点击确认按钮触发 -- 添加
    dateAddConfirm(date) {
      if (this.timeStatus == 0) {
        this.ruleForm.startTime = date;
        var startTimeShow = this.ruleForm.startTime.slice(0, 16);
        var endTimeShow = this.ruleForm.endTime.slice(0, 16);
        var startTimeDes = new Date(startTimeShow).getTime();
        var endTimeDes = new Date(endTimeShow).getTime();
        if (startTimeDes >= endTimeDes) {
          this.ruleForm.startTime = "";
          this.ruleForm.endTime = "";
          this.$message.warning("开始时间不得大于结束时间");
          this.ruleForm.leaveCount = "";
          this.ruleForm.leaveCountDesc = "";
        }
      } else if (this.timeStatus == 1) {
        this.ruleForm.endTime = date;
        var startTimeShow = this.ruleForm.startTime.slice(0, 16);
        var endTimeShow = this.ruleForm.endTime.slice(0, 16);
        var startTimeDes = new Date(startTimeShow).getTime();
        var endTimeDes = new Date(endTimeShow).getTime();
        if (endTimeDes <= startTimeDes) {
          this.ruleForm.startTime = "";
          this.ruleForm.endTime = "";
          this.$message.warning("结束时间不得小于开始时间");
          this.ruleForm.leaveCount = "";
          this.ruleForm.leaveCountDesc = "";
        }
      }
      this.getLeaveCount();
    },

    // 点击日期时按钮触发 -- 添加
    dateAddClick(date) {},

    // 禁用的日期
    disabledDate(date) {
      // 开始时间小于结束时间
      if (this.timeStatus == 0) {
        let endTime;
        if (this.ruleForm.endTime != "") {
          let endTimeIndex = this.ruleForm.endTime;
          if (endTimeIndex != undefined) {
            endTime = endTimeIndex.slice(0, 16);
            let timestamp = date.getTime();
            if (timestamp > new Date(endTime).getTime()) {
              return true;
            }
            return false;
          }
        }
      } else if (this.timeStatus == 1) {
        // 结束时间大于开始时间
        let startTime;
        if (this.ruleForm.startTime != "") {
          let startTimeIndex = this.ruleForm.startTime;
          if (startTimeIndex != undefined) {
            startTime = startTimeIndex.slice(0, 16);
            let timestamp = date.getTime();
            if (timestamp < new Date(startTime).getTime() - 86400000) {
              return true;
            }
            return false;
          }
        }
      }
    },

    // // 请假时长
    // getLeaveCount(e){
    //     let boolean = e.target.value.replace(/[^0-9.]/g,'')
    //     if(!boolean){
    //         this.$message.warning("请输入数字值");
    //         this.ruleForm.leaveCount = '';
    //     }else{
    //         this.ruleForm.leaveCount = boolean
    //         this.getProcess()
    //     }
    // },

    getLeaveCount() {
      if (
        this.ruleForm.startTime != "" &&
        this.ruleForm.endTime != "" &&
        this.ruleForm.leaveType != ""
      ) {
      } else {
        return;
      }
      let data = {
        startTime: this.ruleForm.startTime,
        endTime: this.ruleForm.endTime,
        type: this.ruleForm.leaveType.split("&")[0],
      };
      this.$http.wxsumLeaveCount(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            if (res.status == 0) {
              this.ruleForm.leaveCount = res.data.day;
              this.ruleForm.leaveCountDesc = res.data.hours;
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.ruleForm.leaveCount = "";
          this.ruleForm.leaveCountDesc = "";
          this.$message.error(errRes.message ? errRes.message : errRes.msg);
        }
      );
    },

    //JSSDK
    getConfig() {
      let data = {
        requestURL: location.href.split("#")[0],
      };
      this.$http.wxjssdk(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            wx.config({
              debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
              appId: res.data.appId, // 必填，企业号的唯一标识，此处填写企业号corpid
              timestamp: parseInt(res.data.timestamp), // 必填，生成签名的时间戳
              nonceStr: res.data.nonceStr, // 必填，生成签名的随机串
              signature: res.data.signature, // 必填，签名，见附录1
              jsApiList: ["chooseImage", "uploadImage"], // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
            });
          } else {
            // this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message ? errRes.message : errRes.msg);
        }
      );
    },

    // 上传图片
    upImg() {
      var that = this;
      wx.chooseImage({
        count: 1,
        sizeType: ["original", "compressed"], // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ["album", "camera"], // 可以指定来源是相册还是相机，默认二者都有  album 从相册选图，camera 使用相机，默认二者都有
        success: function (res) {
          that.localIds = res.localIds[0]; // 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片
          that.uploadImg();
        },
      });
    },

    // 上传图片
    uploadImg() {
      var that = this;
      wx.uploadImage({
        localId: that.localIds, // 需要上传的图片的本地ID，由chooseImage接口获得
        isShowProgressTips: 1, // 默认为1，显示进度提示
        success: function (res) {
          var serverId = res.serverId; // 返回图片的服务器端ID
          let data = {
            mediaId: serverId,
          };
          that.$http
            .wxuploadMedia(
              data,
              (res) => {
                if (res.code == "SUCCESS") {
                  that.imgList.push(res.data.path);
                  that.imgShow = that.imgList.join(",");
                } else {
                  that.$message.error(res.message);
                }
              },
              (errRes) => {
                this.$message.error(
                  errRes.message ? errRes.message : errRes.msg
                );
              }
            )
            .then((data) => {})
            .catch((error) => {});
        },
      });
    },

    // 删除图片
    deleteImg(item, index) {
      if (index !== -1) {
        this.imgList.splice(index, 1);
      }
      this.imgShow = this.imgList.join(",");
    },

    // 审批流程
    getProcess() {
      let data = {
        leaveCount: this.ruleForm.leaveCount,
        type: this.ruleForm.leaveType.split("&")[0],
      };
      this.$http.wxqueryProcessPerson(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            if (res.status == 0) {
              this.approvalProcess = res.data;
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message ? errRes.message : errRes.msg);
        }
      );
    },

    // 验证时间限制
    validateTimeRestrictions() {
      // 提取开始时间的时分
      const startTimeStr = this.ruleForm.startTime;
      const endTimeStr = this.ruleForm.endTime;

      if (!startTimeStr || !endTimeStr) {
        return { valid: false, message: "请选择开始时间和结束时间！" };
      }

      // 提取时间部分 (HH:mm)
      const startTimeParts = startTimeStr.split(' ');
      const endTimeParts = endTimeStr.split(' ');

      if (startTimeParts.length < 2 || endTimeParts.length < 2) {
        return { valid: false, message: "时间格式不正确！" };
      }

      const startTime = startTimeParts[1].substring(0, 5);
      const endTime = endTimeParts[1].substring(0, 5);

      // 禁止的开始时间
      const forbiddenStartTimes = ['08:00', '17:00', '20:00'];
      // 禁止的结束时间
      const forbiddenEndTimes = ['05:30', '17:30', '20:30', '08:30'];

      if (forbiddenStartTimes.includes(startTime)) {
        return { valid: false, message: `开始时间不能选择 ${startTime}！` };
      }

      if (forbiddenEndTimes.includes(endTime)) {
        return { valid: false, message: `结束时间不能选择 ${endTime}！` };
      }

      return { valid: true, message: "" };
    },

    // 提交请假申请
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.ruleForm.leaveCountDesc == "") {
            this.$message.warning("请选择正确的日期！");
            return;
          }
          if (this.ruleForm.leaveTypeName == '事假' && dayjs(this.ruleForm.endTime).diff(dayjs(this.ruleForm.startTime), 'day') > 5) {
            this.$message.warning("事假不得超过5个工作日！");
            return;
          }

          // 验证时间限制
          const timeValidation = this.validateTimeRestrictions();
          if (!timeValidation.valid) {
            this.$message.warning(timeValidation.message);
            return;
          }
          let data = {
            endTime: this.ruleForm.endTime,
            imgUrl:	this.imgShow,
            leaveCount: this.ruleForm.leaveCountDesc,
            reason: this.ruleForm.reason,
            remark: "",
            startTime: this.ruleForm.startTime,
            type: this.ruleForm.leaveType.split("&")[0],
          };
          this.disabled = true;
          this.$http.wxaddLeave(
            data,
            (res) => {
              if (res.code == "SUCCESS") {
                if (res.status == 0) {
                  this.$message.success(res.message);
                  setTimeout(() => {
                    this.$router.push({
                      path: "/my-workbench",
                      query: { backStatus: 0 },
                    });
                  }, 1000);
                }
              } else {
                this.$message.error(res.message);
              }
            },
            (errRes) => {
              this.disabled = false;
              this.$message.error(errRes.message ? errRes.message : errRes.msg);
            }
          );
        } else {
          return false;
        }
      });
    },
  },
  mounted() {
    this.ruleForm.department = this.$route.query.department;
    this.ruleForm.plant = this.$route.query.plant;
    this.ruleForm.workshop = this.$route.query.workshop;
    this.$emit("refreshbizlines", "请假申请");
    this.getLeaveType();
    this.getConfig();
  },
};
</script>
