 <template>
       <div class="schedulingBox">
            <div class="navListEnd">
                <div class="statisticsBtn" @click="statisticsDateBtn">考勤日历</div>
                <div class="statisticsBtn" @click="statisticsBtn">考勤统计</div>
            </div>
            <div class="calendarBox">
                <!-- 里面写eleCalendar组件-->
                <ele-calendar
                    :render-content="renderContent"
                    :defaultValue="scheduleDate"
                    :data="datedef"
                    :prop="prop"
                    @date-change="dateChange"
                    @pick="pickDate"
                    value-format="yyyy-MM-dd">
                </ele-calendar>
            </div>
            <div class="checkDetails" v-show="info != undefined && info != ''">
                <span>{{workType}}</span>
                <span  v-show='borrowMark == 1'>:<i class="borrowMark">（借调）</i></span>
            </div>
       </div>
</template>
<script>
import eleCalendar from 'ele-calendar'
import 'ele-calendar/dist/vue-calendar.css'
import _ from 'lodash'
import dayjs from "dayjs";
export default {
    data(){
        return{
           datedef:[
                // {"date":"2019-12-04","content":555,"cid":'SSS'},
                // {"date":"2019-12-06","content":555,"cid":'SS'},
            ],
            prop:'schedule_date', //对应日期字段名
            scheduleDate:'',
            borrowDate:'',
            workType:'',
            borrowMark:'',
            info:'',
        }
    },
    components: {
        eleCalendar
    },
    methods: {
        // 考勤统计
        statisticsBtn(){
            this.$router.push({ path:'/attendance-statistics'})
        },

        // 考勤日历
        statisticsDateBtn() {
          this.$router.push({
            path: "/time-tag",
            query: { openId: this.openId, status: this.status },
          });
        },

        // 当前月
        thisMonth(){
            var date = new Date()
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var strDate = date.getDate();
            if (month >= 1 && month <= 9) {
                month = "0" + month;
            }
            this.scheduleDate = year + '-' + month
        },

        // 查询 年 月
        dateChange(date){
            this.scheduleDate = date.slice(0,date.lastIndexOf('-'));
            this.queryScheduleByEmployee();
        },

        // 排班 查看详情
        pickDate(date,event){
            let info = _.find(this.datedef,{'schedule_date': date});
            this.info = info;
            this.borrowDate = date;
            if(info == undefined){
                this.$message.error("还未排班不能查看");
            }else{
                if(info.workType == '' || info.workType == undefined){
                    this.$message.error("还未排班不能修改");
                }else{
                    this.workType = info.workType
                    this.queryBorrowUserDetail()
                }
            }
        },

        // 查询考勤日历排班情况
        queryScheduleByEmployee(){
            let data = {
                scheduleDate:this.scheduleDate,
            }
            this.$http.wxqueryScheduleByEmployee(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.datedef = res.data.data
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },

        // 排班数据
        renderContent(h,parmas) {
            const loop = data =>{
                return (
                    data.defvalue.value ? (<div><div>{data.defvalue.text}</div> <span>{data.defvalue.value.workType}</span></div>) : <div>{data.defvalue.text}</div>
                )
            }
            return (
                <div  style="min-height:50px;">
                {loop(parmas)}
                </div>
            );
        },

        // 考勤 查看排班
        queryBorrowUserDetail(){
            let data = {
                borrowDate:this.borrowDate,
            }
            this.$http.wxqueryBorrowUserDetail(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.borrowMark = res.data.borrow;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })

        }
    },
    mounted(){
        this.$emit('refreshbizlines','考勤日历');
        this.status = this.$route.query.status;
        this.openId = this.$route.query.openId;
        this.thisMonth();
        this.queryScheduleByEmployee();
    }
}
</script>
