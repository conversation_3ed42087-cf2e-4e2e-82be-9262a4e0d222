<template>
  <div class="secondedApplicationBox">
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      class="demo-ruleForm"
    >
      <el-form-item prop="team" class="formItem defFormItem" label="借入班组">
        <div>{{ ruleForm.workTeam }}</div>
      </el-form-item>
      <el-form-item prop="team" class="formItem defFormItem" label="借出班组">
        <div>{{ ruleForm.team }}</div>
      </el-form-item>
      <el-form-item
        class="formItem"
        v-show="
          ruleForm.leaveNotice != '' &&
          ruleForm.leaveNotice != 'null' &&
          ruleForm.leaveNotice != null
        "
      >
        <div class="leaveNotice">领班：{{ ruleForm.leaveNotice }}</div>
      </el-form-item>
      <el-form-item class="formItem">
        <div>班次：{{ ruleForm.workType }}</div>
      </el-form-item>
      <el-form-item
        prop="startTime"
        class="formItem defFormItem styleForm"
        label="开始时间"
      >
        <el-date-picker
          v-model="ruleForm.startTime"
          type="datetime"
          placeholder="请选择"
          :clearable="false"
          value-format="yyyy-MM-dd HH:mm:ss"
          disabled
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item
        prop="endTime"
        class="formItem defFormItem styleForm"
        label="结束时间"
      >
        <el-date-picker
          v-model="ruleForm.endTime"
          type="datetime"
          placeholder="请选择"
          :clearable="false"
          value-format="yyyy-MM-dd HH:mm:ss"
          disabled
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item
        prop="personNum"
        class="formItem defFormItem"
        label="借调人数"
      >
        <div>{{ ruleForm.personNum }}</div>
      </el-form-item>
      <el-form-item prop="status" class="formItem defFormItem" label="状态">
        <div>{{ ruleForm.status }}</div>
      </el-form-item>
      <div class="formItem proposerShow" label="">
        <div class="applyList">
          <div>{{ ruleForm.applyDate }}</div>
          <div>申请人:</div>
          <div>{{ ruleForm.proposer }}</div>
        </div>
        <div
          class="approveArrayList"
          v-for="(item, index) in ruleForm.approveArray"
          :key="index"
        >
          <div class="approveArray">
            <div>{{ item.approveTime }}</div>
            <div>{{ item.approveUserName }}</div>
            <div>{{ item.approveStatusName }}借调</div>
          </div>
          <div class="approveArray approveReject" v-show="item.remark != null">
            <div>拒绝原因：</div>
            <div>{{ item.remark }}</div>
          </div>
        </div>
      </div>

      <!-- 操作 -->
      <el-form-item class="formItem bottomBtnBox" v-show="statusShow == 1">
        <el-button @click="rejectBtn('ruleForm')">拒绝</el-button>
        <el-button type="primary" @click="confirmBtn('ruleForm')"
          >确认</el-button
        >
      </el-form-item>
      <!-- 操作 -->
    </el-form>
    <!-- 借调人员 start -->
    <el-dialog
      title="选择员工"
      :visible.sync="dialogRemark"
      :close-on-click-modal="false"
      :show-close="false"
      class="dialogRemark"
    >
      <el-form
        :model="ruleFormEmployees"
        ref="ruleFormEmployees"
        class="demo-ruleForm employees-ruleForm"
      >
        <el-form-item
          v-for="(item, index) in ruleFormEmployees.employeeList"
          :key="index"
          :label="'借调员工' + (index + 1)"
          :prop="'employeeList.' + index + '.employees'"
          :rules="{
            required: true,
            message: '请选择借调员工!',
            trigger: 'change',
          }"
        >
          <el-select
            v-model="item.employees"
            filterable
            placeholder="请选择"
            v-reset-page
          >
            <el-option
              v-for="items in employeesOptions"
              :key="items.empId"
              :label="items.name"
              :value="items.empId"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item class="footBtn">
          <el-button
            type="text"
            @click="cancelBtnEmployees('ruleFormEmployees')"
            >取消</el-button
          >
          <el-button
            type="text"
            @click="submitFormEmployees('ruleFormEmployees')"
            >确定</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 借调人员 end -->

    <!-- 拒绝原因 start -->
    <el-dialog
      title="拒绝原因"
      :visible.sync="dialogReject"
      :close-on-click-modal="false"
      :show-close="false"
      class="dialogRemark dialogReject"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        class="demo-ruleForm"
      >
        <el-form-item prop="remark" class="remark">
          <el-input
            type="textarea"
            :rows="3"
            v-model="ruleForm.remark"
            auto-complete="off"
            placeholder="输入内容"
            v-reset-page
          >
          </el-input>
        </el-form-item>

        <el-form-item class="footBtn">
          <el-button type="text" @click="cancelBtn('ruleForm')"
            >取 消</el-button
          >
          <el-button type="text" @click="submitForm('ruleForm')"
            >确认拒绝</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 拒绝原因 end -->
  </div>
</template>
<script>
export default {
  data() {
    var checkRemark = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("输入内容!"));
      } else {
        callback();
      }
    };
    return {
      ruleForm: {
        team: "",
        startTime: "",
        endTime: "",
        leaveNotice: "",
        personNum: "",
        status: "",
        proposer: "",
        applyDate: "",
        approveArray: [],
        workTeam: "",
        workType: "",
      },
      statusShow: "",
      processinstanceid: "",
      dialogRemark: false,
      employeesOptions: [],
      ruleFormEmployees: {
        employeeList: [],
      },
      dialogReject: false,
      ruleForm: {
        remark: "",
      },
      rules: {
        remark: [{ validator: checkRemark, trigger: "blur" }],
      },
      checkEmployee: "",
    };
  },
  methods: {
    // 查询当前主管所有下级员工
    queryChildUser() {
      let data = {
        scheduleDate: "",
      };
      this.$http.wxqueryChildUser(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            this.employeesOptions = res.data;
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 查询借调申请明细
    queryBorrowDetail() {
      let data = {
        processinstanceid: this.processinstanceid,
      };
      this.$http.wxqueryBorrowDetail(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            this.ruleForm.team = res.data.data.borrowWorkTeam;
            this.ruleForm.startTime = res.data.data.startTime;
            this.ruleForm.endTime = res.data.data.endTime;
            this.ruleForm.leaveNotice = res.data.data.leaderName;
            this.ruleForm.personNum = res.data.data.passivityCount;
            this.ruleForm.status = res.data.data.statusName;
            this.ruleForm.proposer = res.data.data.applyUserName;
            this.ruleForm.applyDate = res.data.data.applyDate;
            this.statusShow = res.data.data.status;
            this.ruleForm.approveArray = res.data.approveArray;
            this.ruleForm.workTeam = res.data.data.workTeam;
            this.ruleForm.workType = res.data.data.workType;
            for (let i = 0; i < this.ruleForm.personNum; i++) {
              this.ruleFormEmployees.employeeList.push({
                employees: "",
              });
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 拒绝
    rejectBtn() {
      this.dialogReject = true;
    },

    // 拒绝 取消
    cancelBtn(formName) {
      this.dialogReject = false;
      this.$refs[formName].resetFields();
    },

    // 借调 拒绝
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = {
            processinstanceid: this.processInstanceIds,
            rejectReason: this.ruleForm.remark,
            status: 2, // 1：同意 2：拒绝,默认同意
          };
          this.$http.wxapproveBorrow(
            data,
            (res) => {
              if (res.code == "SUCCESS") {
                if (res.status == 0) {
                  this.$message.success(res.message);
                  setTimeout(() => {
                    this.$router.push({
                      path: "/overtime-approval",
                      query: { pendingStatus: 5 },
                    });
                  }, 1000);
                  this.dialogRemark = false;
                  this.$refs[formName].resetFields();
                }
              } else {
                this.$message.error(res.message);
              }
            },
            (errRes) => {
              if (errRes.code == 500) {
                this.$message.error(errRes.msg);
              }
              this.$message.error(errRes.message);
            }
          );
        } else {
          return false;
        }
      });
    },

    // 借调确认
    confirmBtn() {
      if (this.checkEmployee == "false") {
        this.$confirm("确认借调申请？", "提示", {
          type: "warning",
        })
          .then(() => {
            let data = {
              processinstanceid: this.processInstanceIds,
              status: 1,
            };
            this.$http.wxapproveBorrow(
              data,
              (res) => {
                if (res.code == "SUCCESS") {
                  this.$message.success(res.message);
                  setTimeout(() => {
                    this.$router.push({
                      path: "/overtime-approval",
                      query: { pendingStatus: 5 },
                    });
                  }, 1000);
                } else {
                  this.$message.error(res.message);
                }
              },
              (errRes) => {
                this.$message.error(errRes.message);
              }
            );
          })
          .catch(() => {});
      } else {
        this.disabled = false;
        this.dialogRemark = true;
      }
    },

    // 取消
    cancelBtnEmployees(formName) {
      this.dialogRemark = false;
      this.$refs[formName].resetFields();
    },

    // 借调确认提交
    submitFormEmployees(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var borrowUserArr = [];
          for (let i = 0; i < this.ruleFormEmployees.employeeList.length; i++) {
            borrowUserArr.push(
              this.ruleFormEmployees.employeeList[i].employees
            );
          }
          var borrowUserOne = borrowUserArr.join(",") + ",";
          for (var j = 0; j < borrowUserArr.length; j++) {
            if (
              borrowUserOne
                .replace(borrowUserArr[j] + ",", "")
                .indexOf(borrowUserArr[j] + ",") > -1
            ) {
              this.$message.warning("借调人员不得重复");
              return;
            }
          }

          let data = {
            processinstanceid: this.processInstanceIds,
            status: 1,
            borrowUser: borrowUserArr.join(","),
          };
          this.$http.wxapproveBorrow(
            data,
            (res) => {
              if (res.code == "SUCCESS") {
                if (res.status == 0) {
                  this.$message.success(res.message);
                  setTimeout(() => {
                    this.$router.push({
                      path: "/my-workbench",
                      query: { backStatus: 0 },
                    });
                  }, 1000);
                  this.dialogRemark = false;
                }
              } else {
                this.$message.error(res.message);
              }
            },
            (errRes) => {
              if (errRes.code == 500) {
                this.$message.error(errRes.msg);
              }
              this.$message.error(errRes.message);
            }
          );
        } else {
          return false;
        }
      });
    },
  },
  mounted() {
    this.checkEmployee = localStorage.getItem("checkEmployee");
    console.log(this.checkEmployee);
    this.$emit("refreshbizlines", "借调申请");
    if (this.$route.query.processinstanceid) {
      this.processinstanceid = this.$route.query.processinstanceid;
      let newStr = this.$route.query.processinstanceid.split(",");
      this.processInstanceIds = newStr;
    }
    if (this.$route.query.processInstanceIds) {
      this.processInstanceIds = JSON.parse(
        this.$route.query.processInstanceIds
      );
    }
    this.queryBorrowDetail();
    this.queryChildUser();
  },
};
</script>