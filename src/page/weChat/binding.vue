<template>
    <div class="bindingBox">
        <div class="bindingDes">
            <el-form  :model="ruleForm" :rules="rules" ref="ruleForm" class="demo-ruleForm loginForm">
                <el-form-item prop="userName" class="formItemLine" label="姓名" >
                    <el-input type="text" v-model="ruleForm.userName" placeholder="请输入姓名" autocomplete="off" v-reset-page></el-input>
                </el-form-item>
                <el-form-item prop="workNum" class="formItemLine" label="工号">
                    <el-input type="text" v-model="ruleForm.workNum" placeholder="请输入工号" autocomplete="off" v-reset-page></el-input>
                </el-form-item>
                <el-form-item class="formItemLine bottomBtnBox">
                    <el-button type="primary" @click="submitForm('ruleForm')">绑定</el-button>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>
<script>
export default {
    data(){
         // 姓名
        var checkUserName = (rule,value,callback) => {
            if(!value){
                callback(new Error('请输入姓名！'))
            }else{
                callback()
            }
        };
        // 工号
        var checkWorkNum = (rule,value,callback) => {
            if(!value){
                callback(new Error('请输入工号!'))
            }else{
                callback()
            }
        };
        return{
            openId:'',
            ruleForm:{
                userName:'',
                workNum:'',
            },
            rules: {
                userName:[
                    { validator: checkUserName, trigger: 'blur' }
                ],
                workNum:[
                    { validator: checkWorkNum, trigger: 'blur' }
                ],
            }
        }
    },
    methods: {
        // 绑定
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let data = {
                        empId:this.ruleForm.workNum,
                        name :this.ruleForm.userName,
                        openId: this.openId,
                    }
                    this.$http.wxupdateEmployeeOpenId(data,(res)=>{
                        if(res.code == 'SUCCESS'){
                            if(res.status == 0){
                                this.$message.success(res.message);
                                localStorage.setItem('openId',this.openId);
                                setTimeout(() =>{
                                    WeixinJSBridge.call("closeWindow");
                                },1500);
                            }
                        }else{
                            this.$message.error(res.message);
                        }
                    },(errRes)=>{
                        
                        if(errRes.code == 500){
                            this.$message.error(errRes.msg);
                        }
                        this.$message.error(errRes.message);
                        
                    })
                } else {
                    return false;
                }
            });
        },
    },
    mounted(){
        this.$emit('refreshbizlines','绑定','returnNone'); 
        this.openId = this.$route.query.openId;
    }
}
</script>