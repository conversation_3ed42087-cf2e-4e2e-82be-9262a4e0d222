<template>
  <div class="applyCardBox">
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" class="demo-ruleForm">
      <el-form-item label="补卡日期" prop="cardTime" class="formItem timeFormItem">
        <el-input type="text" v-model="ruleForm.cardTime" placeholder="请选择开始时间" @focus="startShow()" readonly="readonly"
          v-reset-page>
        </el-input>
        <i class="el-icon-date"></i>
      </el-form-item>
      <el-form-item label="班次日期" prop="workTypeDate" class="formItem timeFormItem">
        <el-input type="text" v-model="ruleForm.workTypeDate" placeholder="请选择开始时间" @focus="workShow()"
          readonly="readonly" v-reset-page>
        </el-input>
        <i class="el-icon-date"></i>
      </el-form-item>
      <!-- 时间段选择 start -->
      <div class="timeFormItem">
        <vue-hash-calendar ref="picker" model="dialog" :scroll-change-date="true" :visible.sync="isShowAddCalendar"
          :is-show-week-view="false" :format="format" week-start="sunday" :picker-type="pickerType"
          :show-today-button="true" :disabled-week-view="false" @confirm="dateAddConfirm" @click="dateAddClick"
          @change="dateAddChange" :disabled-date="disabledDate" :minuteStep='30'>
        </vue-hash-calendar>
      </div>
      <!-- 时间段选择 end -->
      <el-form-item label="出入" class="formItem replenishStatus">
        <el-radio-group v-model="ruleForm.replenishStatus">
          <el-radio :label="2">出</el-radio>
          <el-radio :label="1">入</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="补卡原因" class="formItem listItem">
        <el-radio-group v-model="ruleForm.applyType">
          <el-radio :label="1">班车迟到</el-radio>
          <el-radio :label="2">忘记打卡</el-radio>
          <el-radio :label="3">系统数据丢失</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注" prop="reason" class="formItem defFormItem">
        <el-input type="textarea" v-model="ruleForm.reason" placeholder="请输入" v-reset-page></el-input>
      </el-form-item>
      <div class="addFormItem">
        <div class="approvalShowDes">
          <div class="titleImg">图片</div>
          <div class="imgLists">
            <div class="imgList emo-image__preview" v-for="(item, index) in imgList" :key="index">
              <!-- <img :src="item" alt=""> -->
              <el-image :src="item" :preview-src-list="imgList"> </el-image>
              <i class="el-icon-error deleteImg" @click="deleteImg(item, index)"></i>
            </div>
            <el-button @click="upImg"><i class="el-icon-circle-plus-outline"></i></el-button>
          </div>
        </div>
      </div>
      <el-form-item class="formItem bottomBtnBox">
        <el-button type="primary" @click="submitForm('ruleForm')" :disabled="disabled">提交补卡申请</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import wx from "weixin-js-sdk";
export default {
  data() {
    return {
      nowDate: '',
      disabled: false,
      ruleForm: {
        cardTime: '',
        startTime: '',
        endTime: '',
        workTypeDate: '',
        reason: '',
        applyType: '',
      },

      rules: {

      },
      isShowAddCalendar: false,
      format: '',
      pickerType: '',
      timeStatus: '',
      approvalProcess: [],
      imgList: [],
      imgShow: ''
    }
  },
  methods: {
    // 显示日历 补卡日期
    startShow() {
      this.timeStatus = 0;
      this.format = 'YY-MM-DD hh:mm:ss';
      this.pickerType = 'datetime';
      this.isShowAddCalendar = true;
    },

    // 显示日历 班次日期
    workShow() {
      this.timeStatus = 1;
      this.format = 'YY-MM-DD';
      this.pickerType = 'date';
      this.isShowAddCalendar = true;
    },

    // 日期改变触发
    dateAddChange(date) {
    },

    // 点击确认按钮触发 -- 添加
    dateAddConfirm(date) {
      if (this.timeStatus == 0) {
        this.ruleForm.cardTime = date;
      } else if (this.timeStatus == 1) {
        this.ruleForm.workTypeDate = date;
      }
    },

    // 点击日期时按钮触发 -- 添加
    dateAddClick(date) {
    },

    // 禁用的日期
    disabledDate(date) {
      let timestamp = date.getTime();
      if (timestamp > new Date().getTime()) {
        return true
      }
      return false
    },
    //JSSDK
    getConfig() {
      let data = {
        requestURL: location.href.split("#")[0],
      };
      this.$http.wxjssdk(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            wx.config({
              debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
              appId: res.data.appId, // 必填，企业号的唯一标识，此处填写企业号corpid
              timestamp: parseInt(res.data.timestamp), // 必填，生成签名的时间戳
              nonceStr: res.data.nonceStr, // 必填，生成签名的随机串
              signature: res.data.signature, // 必填，签名，见附录1
              jsApiList: ["chooseImage", "uploadImage"], // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
            });
          } else {
            // this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },
    // 上传图片
    upImg() {
      var that = this;
      wx.chooseImage({
        count: 1,
        sizeType: ["original", "compressed"], // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ["album", "camera"], // 可以指定来源是相册还是相机，默认二者都有  album 从相册选图，camera 使用相机，默认二者都有
        success: function (res) {
          that.localIds = res.localIds[0]; // 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片
          that.uploadImg();
        },
      });
    },
    // 上传图片
    uploadImg() {
      var that = this;
      wx.uploadImage({
        localId: that.localIds, // 需要上传的图片的本地ID，由chooseImage接口获得
        isShowProgressTips: 1, // 默认为1，显示进度提示
        success: function (res) {
          var serverId = res.serverId; // 返回图片的服务器端ID
          let data = {
            mediaId: serverId,
          };
          that.$http
            .wxuploadMedia(
              data,
              (res) => {
                if (res.code == "SUCCESS") {
                  that.imgList.push(res.data.path);
                  that.imgShow = that.imgList.join(",");
                } else {
                  that.$message.error(res.message);
                }
              },
              (errRes) => {
                this.$message.error(errRes.message);
              }
            )
            .then((data) => { })
            .catch((error) => { });
        },
      });
    },

    // 删除图片
    deleteImg(item, index) {
      if (index !== -1) {
        this.imgList.splice(index, 1);
      }
      this.imgShow = this.imgList.join(",");
    },

    // 提交补卡申请
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = {
            // endTime:this.ruleForm.endTime,
            reason: this.ruleForm.reason,
            // replenishDate:this.ruleForm.cardTime,
            replenishTime: this.ruleForm.cardTime,
            workTypeDate: this.ruleForm.workTypeDate,
            replenishStatus: this.ruleForm.replenishStatus,
            // startTime:this.ruleForm.startTime,
            type: this.ruleForm.applyType,
            attaUrl: this.imgShow,
          }
          this.disabled = true;
          this.$http.wxSubmitReplenish(data, (res) => {
            if (res.code == 'SUCCESS') {
              this.$message.success(res.message);
              setTimeout(() => {
                this.$router.push({ path: '/my-workbench', query: { backStatus: 0 } })
              }, 1000);
            } else {
              this.$message.error(res.message);
            }
          }, (errRes) => {

            this.disabled = false;
            this.$message.error(errRes.message);

          })
        } else {
          return false;
        }
      });
    },
  },
  mounted() {
    this.$emit('refreshbizlines', '补卡申请');
    this.getConfig();
  }
}
</script>