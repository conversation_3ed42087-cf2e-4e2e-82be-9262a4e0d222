<template>
    <div class="personalBox">
        <div class="personalShow" v-show="personalShow == true">
            <div class=" personalInfo">
                <div class="personalDes">
                    <div class="personalDesLeft">
                        <div class="personalList">
                            <div class="personalLeft">姓名：</div>
                            <div class="personalRight">{{userName}}</div>
                        </div>
                        <div class="personalList">
                            <div class="personalLeft">工号：</div>
                            <div class="personalRight">{{workNum}}</div>
                        </div>
                        <div class="personalList">
                            <div class="personalLeft">部门：</div>
                            <div class="personalRight">{{department}}</div>
                        </div>
                        <div class="personalList">
                            <div class="personalLeft">职位：</div>
                            <div class="personalRight">{{position}}</div>
                        </div>
                        <div class="personalList">
                            <div class="personalLeft">plant：</div>
                            <div class="personalRight">{{plant}}</div>
                        </div>
                    </div>
                    <div class="personImg" v-show="status == 0">
                        <img @click="upImg" :src="personImg" alt="" >
                        <div v-show="personImg != null && personImg != '' " class="imgNotice">请点击上传人脸照片</div>
                    </div>
                </div>
                <div class="personalList">
                    <div class="personalLeft">Workshop：</div>
                    <div class="personalRight">{{workshop}}</div>
                </div>
                <div class="personalList">
                    <div class="personalLeft">生产线：</div>
                    <div class="personalRight">{{productionLine}}</div>
                </div>
                <div class="personalList">
                    <div class="personalLeft">班组：</div>
                    <div class="personalRight">{{team}}</div>
                </div>
            </div>
            <div class="route">
                <div class="lineShow">
                    <div class="routeLeft">
                        <div>班车路线：</div>
                        <el-select v-model="shuttleRoute" filterable placeholder="请选择" @change="selectShuttleRoute(shuttleRoute)" v-reset-page>
                            <el-option
                                v-for="item in shuttleRouteOptions"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id" >
                            </el-option>
                        </el-select>
                    </div>
                    <!-- <el-button>选择班车</el-button> -->
                </div>
                <div class="lineShow">
                    <div class="routeLeft">
                        <div>站点：</div>
                        <el-select v-model="site" filterable placeholder="请选择"  @change="selectSite(site)" v-reset-page>
                            <el-option
                                v-for="item in siteOptions"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id" >
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div v-show="staff == 0">
                    <div class="lineShow">
                        <div class="routeLeft">
                            <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" class="demo-ruleForm">
                                <el-form-item label="代理人工号：" prop="approvalAgent">
                                    <el-input type="text" v-model="ruleForm.approvalAgent" placeholder="请输入员工号" v-reset-page @blur="approvalAgent(ruleForm.approvalAgent)"></el-input>
                                </el-form-item>
                            </el-form>
                        </div>
                    </div>
                    <div class="lineShow">
                        <div class="routeLeft">
                            <div>代理人姓名：</div>
                            <div>{{agentName}}</div>
                        </div>
                    </div>
                
                    <div class="lineShow">
                        <div class="routeLeft">
                            <div>代理人：</div>
                            <el-switch 
                                v-model="agent"  
                                :active-value="1" 
                                :inactive-value="2"
                                @change="setAgent(agent)">
                            </el-switch>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bottomBtnBox" v-show="bindingShow == 1">
            <div class="notice">当前尚未绑定员工信息，请点击下方按钮进行绑定</div>
            <el-button type="primary" @click="binding">微信绑定</el-button>
        </div>

        <!-- 上传图片提示 start -->
        <el-dialog
            title="注意事项"
            :visible.sync="dialogNotice"
            :close-on-click-modal = false
            :show-close="false"
            class="dialogNotice">
            <div class="noticeShow">
                <div class="noticeTitle">注意事项：</div>
                <div class="noticeContent"> 1、请拍摄脸部正面照片</div>
                <div class="noticeContent"> 2、拍照时不要戴眼镜，不要遮住眉毛</div>
                <div class="noticeContent"> 3、照片上需要看到人的两耳轮廓和上到头顶   （包含全部头发）下到颈部下端的范围</div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="conformBtn">确认</el-button>
                <el-button @click="cancelBtn">取 消</el-button>
            </span>
        </el-dialog>
        <!-- 上传图片提示 end -->

        <!-- 不是本企业员工提示 start -->
        <el-dialog
            title="提示"
            :visible.sync="dialogNoticeSmall"
            :close-on-click-modal = false
            :show-close="false"
            class="dialogNotice dialogNoticeSmall">
            <div class="noticeShow">
                <div>{{noticeShow}}</div>
            </div>
            <span slot="footer" class="dialog-footer dialog-footerCenter">
                <el-button type="text" @click="conformNoticeBtn">确定</el-button>
            </span>
        </el-dialog>
        <!-- 不是本企业员工提示 end -->
    </div>
</template>
<script>
import wx from 'weixin-js-sdk';
export default {
    data(){
        var checkApprovalAgent = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('请输入代理人工号'));
            }else {
                setTimeout(() => {
                    if (value != this.ruleForm.empId) {
                        callback(new Error('请输入正确的代理人工号'));
                    } else {
                        callback();
                    }
                }, 500);
            }
        };
        return{
            personalShow:false, // 个人信息显示
            status:'',
            userName:'',
            workNum:'',
            department:'',
            position:'',
            plant:'',
            workshop:'',
            productionLine:'',
            team:'',
            personImg:'../static/images/weChat/headImg.png',
            shuttleRoute:'',
            site:'',
            agentName:'',
            agent:'',
            staff:'',  // 0：不是员工
            dialogNotice:false,
            openId:'',
            bindingShow:'',
            dialogNoticeSmall:false,
            localIds:'',
            imgFilePath:'',
            noticeShow:'',
            shuttleRouteOptions:'',
            siteOptions:'',
            ruleForm:{
                approvalAgent:'',
                empId:''
            },
            rules: {
                approvalAgent:[
                    { validator: checkApprovalAgent, trigger: 'blur' }
                ]
            },
        }
    },
    methods: {
        // 班车路线
        queryLineAll(){
            this.$http.wxqueryLineAll(null,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.shuttleRouteOptions = res.data.data
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },
        
        // 选择班车
        selectShuttleRoute(id){
            this.shuttleRoute = id;
            this.queryStationByLineAll();
        },

        // 根据线路ID查询所有站点信息
        queryStationByLineAll(){
            let data = {
                lineId:this.shuttleRoute
            }
            this.$http.wxqueryStationByLineAll(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.siteOptions = res.data.data;
                    if(this.siteOptions.length <= 0){
                        this.site = ''
                    }
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },

        // 选择路线
        selectSite(id){
            this.site = id;
            this.addEmployeeLineStation()
        },
        
        // 添加路线站点员工绑定
        addEmployeeLineStation(){
            let data = {
                line:this.shuttleRoute,
                station: this.site	
            }
            this.$http.addEmployeeLineStation(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.$message.success(res.message);
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },

        // 输入员工号
        approvalAgent(approvalAgent){
            if(approvalAgent == ''){
                this.ruleForm.empId =''
                this.updateEmployeeAssignee();
                this.agentName = ''; 
            }else{
                this.queryDetailByEmpId(approvalAgent)
            }
        },

        // H5根据员工号查询员工信息详情
        queryDetailByEmpId(approvalAgent){
            let data = {
                id:approvalAgent
            }
            this.$http.wxqueryDetailByEmpId(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    if(res.data == 'null'){
                        this.agentName = ''; 
                        this.ruleForm.empId = res.data.empId;
                    }else{
                        this.ruleForm.empId = res.data.empId;
                        this.agentName = res.data.name;  
                        this.updateEmployeeAssignee(); 
                    }
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },
        
        // H5设置员工代理人
        updateEmployeeAssignee(){
            let data = {
                assignee:this.ruleForm.empId
            }
            this.$http.wxupdateEmployeeAssignee(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.$message.success(res.message);
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },

        // 是否需要代理
        setAgent(value){
            let data = {
                agency:this.agent // 是否需要代理 1：代理 2：未代理
            }
            this.$http.wxupdateEmployeeAgency(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.$message.success(res.message);
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })

        },

        // 数据渲染
        initData(){
            let data = {
                id: localStorage.getItem('openId'),	
            }
            this.$http.wxqueryPhoneEmployeeDetail(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    if(res.status == 0){
                        if(res.data.data == null){
                            localStorage.clear();
                            this.bindingShow = 1;
                            this.personalShow = false;
                        }else{
                            this.status = res.status;
                            this.userName = res.data.data.name;
                            this.workNum = res.data.data.empId;
                            this.department = res.data.data.department;
                            this.position = res.data.data.directName;
                            this.plant = res.data.data.plant;
                            this.workshop = res.data.data.workshop;
                            this.productionLine = res.data.data.productionLine;
                            this.team = res.data.data.workTeam;
                            this.agent = res.data.data.agency;
                            this.staff = res.data.data.staff;
                            this.ruleForm.approvalAgent = res.data.data.assignee;
                            this.agentName = res.data.data.assigneeName;
                            if(res.data.data.faceUrl == null) {
                                this.personImg='../static/images/weChat/headImg.png' 
                            }else{
                                this.personImg = res.data.data.faceUrl;  
                            }
                        }
                    }
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                if(errRes.code == 500){
                    if(errRes.msg = 'You have a openId is undefined'){
                        this.bindingShow = 1;
                        this.personalShow = false;
                        this.$message.error(errRes.msg);
                    }
                }
                this.$message.error(errRes.message);
            })
        },

        // 班车路线 ，站点
        queryEmployeeLineDetail(){
            this.$http.wxqueryEmployeeLineDetail(null,(res)=>{
                if(res.code == 'SUCCESS'){
                    // this.$message.success(res.message);
                    this.shuttleRoute = res.data.data.line;
                    this.site = res.data.data.station;
                    this.queryStationByLineAll();
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },

        // 绑定
        binding(){
            this.$router.push({ name:'binding',query:{openId:this.openId}})
        },

        //JSSDK
        getConfig(){
            let data = {
                requestURL:location.href.split('#')[0],
            }
            this.$http.wxjssdk(data, (res) => {
                if (res.code == 'SUCCESS') {
                    wx.config({
                        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                        appId: res.data.appId, // 必填，企业号的唯一标识，此处填写企业号corpid
                        timestamp: parseInt(res.data.timestamp), // 必填，生成签名的时间戳
                        nonceStr: res.data.nonceStr, // 必填，生成签名的随机串
                        signature: res.data.signature,// 必填，签名，见附录1
                        jsApiList: ['chooseImage','uploadImage'] // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
                    });
                } else {
                    // this.$message.error(res.message);
                }
            }, (errRes) => {
                this.$message.error(errRes.message)
            })
        },
        
        // 上传图片
        upImg(){
            this.dialogNotice = true;
        },
        
        // 上传图片
        uploadImg(){
            var that = this;
            wx.uploadImage({
                localId: that.localIds, // 需要上传的图片的本地ID，由chooseImage接口获得
                isShowProgressTips: 1, // 默认为1，显示进度提示
                success: function (res) {
                    var serverId = res.serverId; // 返回图片的服务器端ID
                    let data = {
                        mediaId: serverId
                    }
                    that.$http.wxuploadMedia(data, (res) => {
                        if (res.code == 'SUCCESS') {
                            that.personImg = res.data.path
                            that.$router.push({ name:'face-management',query:{imgUrl:that.personImg,empId:that.workNum}})
                        } else {
                            that.$message.error(res.message);
                        }
                        
                    }, (errRes) => {
                        this.$message.error(errRes.message)
                    })
                    .then((data) => {

                    })
                    .catch((error) => {
                    })
                }
            });
        },

        // 注意事项 确认
        conformBtn(){
            var that = this;
            wx.chooseImage({
                count: 1,
                sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
                sourceType: ['camera'], // 可以指定来源是相册还是相机，默认二者都有 album 从相册选图，camera 使用相机，默认二者都有
                success: function (res) {
                    that.localIds  = res.localIds[0]// 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片
                    that.uploadImg();
                }
            });
            this.dialogNotice = false;
        },

        //
        cancelBtn(){
            this.dialogNotice = false;
        },

        //  不是本企业员工
        conformNoticeBtn(){
            setTimeout(() =>{
                WeixinJSBridge.call("closeWindow");
            },500);
            setTimeout(() =>{
                this.dialogNoticeSmall = false;
            },600);
        }
    },
    mounted(){
        this.$emit('refreshbizlines','个人信息','returnNone'); 
        this.status = this.$route.query.status;
        this.openId = this.$route.query.openId;
        let openId = localStorage.getItem('openId');
        // this.status = 0;
        // this.openId = 'XieChao';
        // let openId = 'XieChao';
        // this.openId = 'GeHui';
        // let openId = 'GeHui';
        if(this.status == 0){
            if(openId == '' || openId == null){
                this.bindingShow = 1;
                this.personalShow = false;
            }else{
                this.personalShow = true;
                this.getConfig();
                localStorage.setItem('openId',this.openId);
                this.initData();
                this.queryLineAll();
                this.queryEmployeeLineDetail();
            }
        }else if(this.status == 1){
            this.noticeShow = '您不是本企业员工，如有问题，请联系管理员'
            this.dialogNoticeSmall = true;
            this.personalShow = false;
            localStorage.clear();
        }else if(this.status == 2){
            this.noticeShow = '本工号员工已离职，如有问题，请联系管理员'
            this.dialogNoticeSmall = true;
            this.personalShow = false;
            localStorage.clear();
        }
    }
}
</script>