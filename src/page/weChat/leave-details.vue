<template>
  <div class="leaveDetailsBox">
    <div class="leaveApprovalLists">
      <div class="list">
        <div>申请人</div>
        <div>{{ applyUserName }}</div>
      </div>
      <div class="list">
        <div>所在部门</div>
        <div>{{ department }}</div>
      </div>
      <div class="list">
        <div>请假类型</div>
        <div>{{ leaveType }}</div>
      </div>
      <div class="list">
        <div class="leaveNotice">{{ leaveNotice }}</div>
      </div>
      <div class="list">
        <div>开始时间</div>
        <div>{{ startTime }}</div>
      </div>
      <div class="list">
        <div>结束时间</div>
        <div>{{ endTime }}</div>
      </div>
      <div class="list">
        <div>请假时长</div>
        <div>{{ leaveCount }}（h）</div>
      </div>
      <div class="list">
        <div>请假事由</div>
        <div>{{ reason }}</div>
      </div>
    </div>
    <div class="processBox">
      <div class="approvalShowDes" v-show="imgArray.length > 0">
        <div class="titleImg">图片</div>
        <div class="approvalImgUrl">
          <div
            class="imgList emo-image__preview"
            v-for="(item, index) in imgArray"
            :key="index"
          >
            <!-- <img :src="item" alt=""> -->
            <el-image :src="item" :preview-src-list="imgArray"> </el-image>
          </div>
        </div>
      </div>
      <div class="approvalProcess">
        <div class="approvalTitle">
          当前状态：<span>{{ approvalStatus }}</span>
        </div>
        <div class="approvalProcessDes">
          <div
            class="approvalProcessList"
            v-for="(item, index) in approvalProcess"
            :key="index"
          >
            <div class="approvalProcessContent">
              <div>{{ item.createTime }}</div>
              <div>{{ item.approveUserName }}</div>
              <div>{{ item.approveStatusName }}请假申请</div>
              <div>{{ item.status }}</div>
            </div>
            <div class="approvalProcessReject" v-show="item.remark != null">
              <div>驳回原因：</div>
              <div>{{ item.remark }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 操作 start -->
    <div
      class="bottomBtnBox"
      v-show="(leaveStatus == 1 || leaveStatus == 2) && myStatus == 1"
    >
      <el-button type="primary" @click="withdrawBtn">撤回</el-button>
    </div>
    <!-- 操作 end -->

    <!-- 操作 start -->
    <div
      class="bottomBtnBox"
      v-show="(leaveStatus == 1 || leaveStatus == 2) && myStatus != 1"
    >
      <el-button @click="rejectBtn('ruleForm')">驳回</el-button>
      <el-button type="primary" @click="adoptBtn">通过</el-button>
    </div>
    <!-- 操作 end -->

    <!-- 驳回原因 start -->
    <el-dialog
      title="驳回原因"
      :visible.sync="dialogRemark"
      :close-on-click-modal="false"
      :show-close="false"
      class="dialogRemark"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        class="demo-ruleForm"
      >
        <el-form-item prop="remark" class="remark">
          <el-input
            type="textarea"
            :rows="3"
            v-model="ruleForm.remark"
            auto-complete="off"
            placeholder="输入内容"
            v-reset-page
          >
          </el-input>
        </el-form-item>

        <el-form-item class="footBtn">
          <el-button type="text" @click="cancelBtn('ruleForm')"
            >取 消</el-button
          >
          <el-button type="text" @click="submitForm('ruleForm')"
            >确认驳回</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 驳回原因 end -->
  </div>
</template>
<script>
export default {
  data() {
    var checkRemark = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("输入内容!"));
      } else {
        callback();
      }
    };
    return {
      department: "",
      reason: "",
      leaveType: "",
      dialogRemark: false,
      startTime: "",
      endTime: "",
      approvalStatus: "", // 当前状态
      leaveNotice: "",
      leaveStatus: "",
      leaveCount: "",
      approvalProcess: [],
      applyUserName: "",
      imgArray: [],
      ruleForm: {
        remark: "",
      },
      rules: {
        remark: [{ validator: checkRemark, trigger: "blur" }],
      },
      processinstanceid: "",
      myStatus: "", // 1、我的请假申请; 2、我的加班申请; 3、我的补卡申请
      pendingStatus: "", // 待处理 1、请假审批; 2、加班审批; 3、加班确认; 4、补卡审批
      alreadyStatus: "", // 已处理 1、请假审批; 2、加班审批; 3、加班确认; 4、补卡审批
      processInstanceIds: [],
    };
  },
  methods: {
    // 请假审批 详情
    queryLeaveDetail() {
      let data = {
        processinstanceid: this.processinstanceid,
      };
      this.$http.wxqueryLeaveDetail(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            var dataDes = res.data.leave;
            this.department = dataDes.department;
            this.reason = dataDes.reason;
            this.leaveType = dataDes.leaveTypeName;
            this.startTime = dataDes.startTime;
            this.endTime = dataDes.endTime;
            this.leaveNotice = dataDes.explains;
            this.approvalProcess = res.data.approveHistoryList;
            this.approvalStatus = dataDes.statusName;
            this.imgArray = dataDes.imgArray;
            this.leaveCount = dataDes.leaveCount;
            this.applyUserName = dataDes.applyUserName;
            this.leaveStatus = dataDes.status;
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 撤回
    withdrawBtn() {
      let data = {
        processinstanceid: this.processInstanceIds,
      };
      this.$http.wxcancelLeave(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            this.$message.success(res.message);
            setTimeout(() => {
              this.$router.push({
                path: "/my-workbench",
                query: { backStatus: 0 },
              });
            }, 1000);
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 通过
    adoptBtn() {
      let data = {
        processinstanceid: this.processInstanceIds,
        approveStatus: 1, // 审批状态结果 1：同意 2：驳回
        approveType: 1, // 审批流程类型 1：请假流程 2：加班流程
      };
      this.$http.wxapproveProcess(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            this.$message.success(res.message);
            setTimeout(() => {
              this.$router.push({
                path: "/overtime-approval",
                query: { pendingStatus: 1 },
              });
            }, 1000);
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    //  驳回
    rejectBtn() {
      this.dialogRemark = true;
    },

    // 取消驳回
    cancelBtn(formName) {
      this.dialogRemark = false;
      this.$refs[formName].resetFields();
    },

    // 确认驳回
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = {
            processinstanceid: this.processInstanceIds,
            approveStatus: 2, // 审批状态结果 1：同意 2：驳回
            approveType: 1, // 审批流程类型 1：请假流程 2：加班流程
            remark: this.ruleForm.remark,
          };
          this.$http.wxapproveProcess(
            data,
            (res) => {
              if (res.code == "SUCCESS") {
                this.$message.success(res.message);
                setTimeout(() => {
                  this.$router.push({
                    path: "/overtime-approval",
                    query: { pendingStatus: 1 },
                  });
                }, 1000);
                this.dialogRemark = false;
                this.$refs[formName].resetFields();
              } else {
                this.$message.error(res.message);
              }
            },
            (errRes) => {
              this.$message.error(errRes.message);
            }
          );
        } else {
          return false;
        }
      });
    },
  },
  mounted() {
    this.$emit("refreshbizlines", "请假申请");
    if (this.$route.query.processinstanceid) {
      this.processinstanceid = this.$route.query.processinstanceid;
      let newStr = this.$route.query.processinstanceid.split(",");
      this.processInstanceIds = newStr;
    }
    if (this.$route.query.processInstanceIds) {
      this.processInstanceIds = JSON.parse(
        this.$route.query.processInstanceIds
      );
    }
    this.myStatus = this.$route.query.myStatus; // myStatus 1、我的请假申请; 2、我的加班申请; 3、我的补卡申请
    this.pendingStatus = this.$route.query.pendingStatus; // 待处理 pendingStatus 1、请假审批; 2、加班审批; 3、加班确认; 4、补卡审批
    this.alreadyStatus = this.$route.query.alreadyStatus; // 已处理 alreadyStatus 1、请假审批; 2、加班审批; 3、加班确认; 4、补卡审批
    this.queryLeaveDetail();
    if (
      this.myStatus == undefined &&
      this.pendingStatus == undefined &&
      this.alreadyStatus == undefined
    ) {
      this.pendingStatus = 1;
    }
  },
};
</script>