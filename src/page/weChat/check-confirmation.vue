<template>
  <div class="overtimeConfirmationBox backcolorBox bottomBox">
    <div class="overtimeConfirmationLists">
      <div class="list">
        <div>部门</div>
        <div>{{ department }}</div>
      </div>
      <div class="list">
        <div>加班事由</div>
        <div>{{ reason }}</div>
      </div>
      <div class="list">
        <div>加班类型</div>
        <div>{{ overtimeType }}</div>
      </div>

      <!-- 领班确认加班员工 start-->
      <div class="foremanApplyBox">
        <div class="addLists foremanLists">
          <div class="lstTop">
            <div class="listTopLeft">
              <div class="timeShow">{{ startTime }}</div>
              -
              <div class="timeShow">{{ endTime }}</div>
            </div>
          </div>
          <div class="overTimeEmployees" @click="foremanCheckEmployees">
            <label for="">加班员工</label>
            <div class="overTimeEmployeesRight">
              <div class="edit">
                <span
                  v-for="(em, emindex) in employeesLists"
                  :key="emindex"
                  :class="[
                    em.status == '2' ? 'greenMark' : '',
                    em.status == '3' ? 'redMark' : '',
                    em.status == '1' ? 'grayMark' : '',
                  ]"
                >
                  {{ em.overtimeUser + em.workType }}
                </span>
              </div>
              <i class="el-icon-arrow-right"></i>
            </div>
          </div>
        </div>
      </div>
      <!-- 领班确认加班员工 end-->

      <!-- 操作记录 start -->
      <div class="operateRecord" v-if="operateRecord.length > 0">
        <div>操作记录</div>
        <div class="recordList">
          <div
            class="recordDes"
            v-for="(re, reindex) in operateRecord"
            :key="reindex"
          >
            <div>{{ re.approveUserName + re.approveStatusName }}审批</div>
            <div>{{ re.approveTime }}</div>
          </div>
        </div>
      </div>
      <!-- 操作记录 end -->
    </div>

    <!-- 员工确认状态 start -->
    <div class="overTimeEmployeeColor">
      <div class="greenMark"><span class="greenBgMark"></span>已确认</div>
      <div class="redMark"><span class="redBgMark"></span>拒绝</div>
      <div class="grayMark"><span class="grayBgMark"></span>未答复</div>
    </div>
    <!-- 员工确认状态 end -->

    <!-- 领班 操作 start -->
    <div class="bottomBtnBox" v-show="foremanOperate == 2">
      <el-button @click="withdrawBtn">撤回</el-button>
      <el-button type="primary" @click="submitBtn">提交审批</el-button>
    </div>
    <!-- <div class="bottomBtnBox" v-show="foremanOperate == 1 || foremanOperate == 3 || foremanOperate == 4">
      <el-button type="primary" @click="foremanConfirm">确认</el-button>
    </div> -->
    <!-- 领班 操作 end -->
  </div>
</template>
<script>
export default {
  data() {
    return {
      isLeader: true,
      department: "", // 部门
      reason: "", // 原因
      multiple: "", // 倍
      startTime: "", // 开始时间
      endTime: "", // 结束时间
      employeesLists: [], // 领班查看员工
      overtimeType: "", // 加班类型
      processinstanceid: "377501",
      foremanOperate: "",
      operateRecord: [],
      processInstanceIds: [],
    };
  },
  methods: {
    // 领班 数据渲染
    initForemanData() {
      let data = {
        processinstanceid: this.processInstanceIds,
        status: 1,
      };
      this.$http.wxqueryOvertimeDetail(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            if (res.status == 0) {
              //res.data.apply.status 领班状态 1：确认 2：领班确认 3：领班取消 4：经理确认 5：经理取消 6：过时自动取消
              var dataShow = res.data.apply;
              var applyList = res.data.applyList;
              this.department = dataShow.department;
              this.reason = dataShow.reason;
              this.overtimeType = dataShow.overtimeTypeName;
              this.multiple = dataShow.multiple;
              this.startTime = dataShow.startTime;
              this.endTime = dataShow.endTime;
              this.employeesLists = applyList;
              this.foremanOperate = dataShow.status;
              this.operateRecord = res.data.approveArray;
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          if (errRes.code == 500) {
            this.$message.error(errRes.msg);
          }
          this.$message.error(errRes.message);
        }
      );
    },

    // 领班撤回
    withdrawBtn() {
      let data = {
        processinstanceid: this.processInstanceIds,
        status: 2, // 1：同意 2：拒绝,默认同意
      };
      this.$http.wxapproveOvertime(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            if (res.status == 0) {
              this.$message.success(res.message);
              setTimeout(() => {
                this.$router.push({
                  path: "/my-workbench",
                  query: { backStatus: 0 },
                });
              }, 1000);
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 领班提交审批
    submitBtn() {
      let data = {
        processinstanceid: this.processInstanceIds,
        status: 1, // 1：同意 2：拒绝,默认同意
      };
      this.$http.wxapproveOvertime(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            if (res.status == 0) {
              this.$message.success(res.message);
              setTimeout(() => {
                this.$router.push({
                  path: "/my-workbench",
                  query: { backStatus: 0 },
                });
              }, 1000);
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          if (errRes.code == 500) {
            this.$message.error(errRes.msg);
          }
          this.$message.error(errRes.message);
        }
      );
    },

    // 领班查看员工确认状态
    foremanCheckEmployees() {
      let processInstanceIds = JSON.stringify(this.processInstanceIds);
      this.$router.push({
        path: "/overtime-status",
        query: {
          processinstanceid: this.processinstanceid,
          processInstanceIds: processInstanceIds,
        },
      });
    },

    // 领班确认
    foremanConfirm() {
      this.$router.push({
        path: "/overtime-approval",
        query: {
          myStatus: 2,
        },
      });
    },
  },

  mounted() {
    this.$emit("refreshbizlines", "加班审批");
    this.processinstanceid = this.$route.query.processinstanceid;
    this.processInstanceIds = JSON.parse(this.$route.query.processInstanceIds);
    this.department = this.$route.query.department;
    this.initForemanData();
  },
};
</script>
