 <template>
       <div class="schedulingBox">
           <div class="navScheduling">
               <div class="schedulingNavActive" @click="personnelBtn">按人员排班</div>
               <div class="schedulingNav" @click="dateBtn">按日期排班</div>
           </div>
           <div class="calendarBox calendarDefinedBox">
                <!-- 里面写eleCalendar组件-->
                <ele-calendar
                    :render-content="renderContent"
                    :defaultValue="scheduleDate" 
                    :data="datedef"
                    :prop="prop"
                    @date-change="dateChange"
                    @pick="pickDate"
                    value-format="yyyy-MM-dd">
                </ele-calendar>
            </div>

            <!-- 选择班次 start -->
            <el-dialog 
                title="选择班次" 
                :visible.sync="dialogRemark" 
                :close-on-click-modal = false
                :show-close="false"
                class="dialogRemark">
                <el-form :model="ruleForm" :rules="rules" ref="ruleForm" class="demo-ruleForm">
                    <el-form-item prop="team" class="remark" label="班次:">
                        <el-select v-model="ruleForm.team" placeholder="请选择班次" v-reset-page>
                            <el-option
                                v-for="item in teamOptions"
                                :key="item.name"
                                :label="item.name"
                                :value="item.name" >
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item class="footBtn">
                        <el-button type="text" @click="cancelBtn('ruleForm')">取 消</el-button>
                        <el-button type="text" @click="submitForm('ruleForm')">确 认</el-button>
                    </el-form-item>
                </el-form>
            </el-dialog>
            <!-- 选择班次 end -->
       </div>
</template>
<script>
import eleCalendar from 'ele-calendar'
import 'ele-calendar/dist/vue-calendar.css' 
import _ from 'lodash'
export default {
    data(){
        var checkTeam = (rule,value,callback) => {
            if(value == ''){
                callback(new Error('请选择班次!'))
            }else{
                callback()
            }
        };
        return{
            datedef:[
                // {"date":"2019-12-04","content":555,"cid":'SSS'},
                // {"date":"2019-12-06","content":555,"cid":'SS'},
            ],
            prop:'schedule_date', //对应日期字段名
            scheduleDate:'',
            dialogRemark:false,
            teamOptions:[],
            ruleForm:{
              team:''
            },
            rules:{
                team:[
                    { validator: checkTeam, trigger: 'blur' }
                ]
            }
        }
    },
    components: {
        eleCalendar
    },
    methods: {
        // 按照人员排班 nav
        personnelBtn(){
            this.$router.push({ path:'/scheduling'})
        },

        // 按照日期排班 nav
        dateBtn(){
            this.$router.push({ path:'/scheduling-date'})
        },
        
        // 查询所有班次信息
        queryPhoneWorkTypeAll(){
         this.$http.wxqueryPhoneWorkTypeAll(null,(res)=>{
                if(res.code == 'SUCCESS'){
                  this.teamOptions = res.data.data
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },
        
        // 查询 年 月
        dateChange(date){
            this.scheduleDate = date.slice(0,date.lastIndexOf('-'));
            this.queryPlanScheduleByDate();
        },

        // 排班 
        pickDate(date,event){
            let info = _.find(this.datedef,{'schedule_date': date});
            if(info == undefined){
                this.$message.error("还未排班不能修改");
            }else{
                if(info.work_type == '' || info.work_type == undefined){
                    this.$message.error("还未排班不能修改");
                }else{
                    this.dialogRemark = true;
                    this.scheduleDateDes = date;
                    this.ruleForm.team = info.work_type;
                }
            }
        },
        
        // H5按日期查询排班(只需排班日期和员工号)
        queryPlanScheduleByDate(){
            let data = {
                scheduleDate:this.scheduleDate,
                empId:this.empId,
            }
            this.$http.wxqueryPlanScheduleByDate(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.datedef = res.data.data;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },
        
        // 排班数据
        renderContent(h,parmas) {
            const loop = data =>{
                return (
                    data.defvalue.value ? (<div><div>{data.defvalue.text}</div> <span>{data.defvalue.value.work_type}</span></div>) : <div>{data.defvalue.text}</div>
                )
            }
            return (
                <div  style="min-height:50px;">
                {loop(parmas)}
                </div>
            );
        },

        // 选择班次 取消
        cancelBtn(formName){
            this.dialogRemark = false;
            this.$refs[formName].resetFields();
        },

        // 选择班次  确认
        submitForm(formName){
             this.$refs[formName].validate((valid) => {
                if (valid) {
                    let data = {
                        empId: this.empId,
                        scheduleDate:this.scheduleDateDes,
                        workType: this.ruleForm.team, 
                    }
                    this.$http.wxupdateUserSchedule(data,(res)=>{
                        if(res.code == 'SUCCESS'){
                            if(res.status == 0){
                                this.$message.success(res.message);
                                this.dialogRemark = false;
                                this.$refs[formName].resetFields();
                                this.queryPlanScheduleByDate();
                            }
                        }else{
                            this.$message.error(res.message);
                        }
                    },(errRes)=>{
                        this.$message.error(errRes.message);
                    })  
                } else {
                    return false;
                }
            });
        },
    },
    mounted(){
        this.$emit('refreshbizlines','排班'); 
        this.empId = this.$route.query.id;
        this.scheduleDate = this.$route.query.date;
        this.queryPlanScheduleByDate();
        this.queryPhoneWorkTypeAll()
    }
}
</script>