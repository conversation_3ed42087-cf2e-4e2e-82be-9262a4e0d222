<template>
    <div class="timeBox">
       <div class="navListEnd">
           <div class="statisticsBtn" @click="scheduleBtn">查看排班</div>
           <div class="statisticsBtn" @click="statisticsBtn">考勤统计</div>
       </div>
       <div class="calendarShow">

         <Calendar
           class="calendarBox"
           v-on:choseDay="clickDay"
           v-on:changeMonth="changeDate"
           v-slot:day="scope"
           :markDateMore="clockInMoreArr"
         >
           <!-- :markDate="clockInArr" -->
           <!-- :markDateMore="clockInMoreArr" -->
           <template>
             <div class="point"></div>
           </template>
         </Calendar>
       </div>
       <div class="clockInShow">
         <div class="clockTypeList">
           <div
             v-for="(item, index) in clockTypeList"
             :key="index"
             :class="['clockType', item.key]"
           >
             {{ item.value }}
           </div>
         </div>

           <div class="clockInToday">今日打卡<span>{{clockIn}}</span>次</div>
           <div class="clockInLists">
               <div class="clockInList" v-for="(item,index) in clockInLists" :key="index">
                   <div class="clockInTime">打卡时间：{{item.attendantTime}}</div>
                   <div class="attendanceMachine">{{item.machineName}}</div>
               </div>
               <div v-show="clockInLists == ''" style="padding-top:10px;text-align: center;">暂无打卡记录</div>
           </div>
       </div>

       <!-- 不是本企业员工提示 start -->
        <el-dialog
            title="提示"
            :visible.sync="dialogNoticeSmall"
            :close-on-click-modal = false
            :show-close="false"
            class="dialogNotice dialogNoticeSmall">
            <div class="noticeShow">
                <div>{{noticeShow}}</div>
            </div>
            <span slot="footer" class="dialog-footer dialog-footerCenter">
                <el-button type="text" @click="conformNoticeBtn">确定</el-button>
            </span>
        </el-dialog>
        <!-- 不是本企业员工提示 end -->
    </div>
</template>
<script src='/dist/Calendar.umd.min.js'>
</script>
<script>
import Calendar from 'vue-calendar-component';
import dayjs from "dayjs";
export default {
    components: {
        Calendar
    },
    data() {
        return {
            clockIn:'',
            attendantDate:'',
            currentMonth:'',
            clockInLists:[],
            calendarData:[],
            dialogNoticeSmall:false,
            noticeShow:'',
            clockInArr: [],
            clockInMoreArr: [
              { date: "2023-5-29", className: "markGreen" },
              { date: "2023/5/30", className: "markRed" },
            ],
            empId: "",
            clockTypeList: [],
            // value1: '',
            // highPrice: '', //最高价格
            // normalPrice: '', //正常价格
            // datedef: [], //选中样式数组 只要按他的格式放入数组中 就会被渲染
            // value: new Date(),
            // prop: 'date', //对应日期字段名

            // dayStart: '', //入住日期字符串(yyyy-mm-dd)
            // startTime: 0, //入住日期时间戳
            // startMoney: 0, //入住日期租金

            // dayEnd: '', //离开日期字符串(yyyy-mm-dd)
            // endTime: 0, //离开日期时间戳
            // endMoney: 0, //离开日期租金

            // totalMoney: 0, //总金额
            // clickNum: 0 //点击计数器
        }
    },
    methods: {
        // 获取员工信息
        queryEmployeeDetail(){
            this.$http.wxqueryEmployeeDetail(null,(res)=>{
                if(res.code == 'SUCCESS'){
                    // console.log(res.data.positionNum )
                    if(res.data.positionNum == 1){ // positionNum：1：不允许看 2：可以看
                        this.noticeShow = '您无权限查看人脸考勤记录，如有疑问，请联系管理员。'
                        this.dialogNoticeSmall = true;
                    }
                    this.thisMonth();
                    this.queryAttendanceMonth();
                    this.initData();
                    this.getClockIn();
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },
        getClockIn() {
          var clockInata = {
            attendantDate: this.attendantDate,
          };
          this.$http.wxqueryAttendance(
            clockInata,
            (res) => {
              if (res.code == "SUCCESS") {
                if (res.status == 0) {
                  this.clockInLists = res.data;
                  this.clockIn = res.data.length;
                } else {
                  this.$message.error(res.message);
                }
              } else {
                this.$message.error(res.message);
              }
            },
            (errRes) => {
              if (errRes.code == 500) {
                if (
                  errRes.msg == "You have a openId is undefined" ||
                  errRes.msg == "H5根据openId查询员工信息详情查询异常"
                ) {
                  this.$message.error(errRes.msg);
                  this.$router.push({
                    name: "binding",
                    query: { openId: this.openId },
                  });
                }
              }
              this.$message.error(errRes.message);
            }
          );
        },
        // 数据渲染
        initData() {
          var data = {
            attendantDate: this.attendantDate,
          };
          this.$http.wxqueryAttendanceByKayan(
            data,
            (res) => {
              if (res.code == "SUCCESS") {
                if (res.status == 0) {
                  if (res.data) {
                    var arr = [];
                    Object.keys(res.data).forEach(function (key) {
                      arr.push({ key: key, value: res.data[key] });
                    });
                    this.clockTypeList = arr;
                  } else {
                    this.clockTypeList = [];
                  }
                } else {
                  this.$message.error(res.message);
                }
              } else {
                this.$message.error(res.message);
              }
            },
            (errRes) => {
              if (errRes.message) {
                this.$message.error(errRes.message);
                if (
                  errRes.message == "You have a openId is undefined" ||
                  errRes.message == "H5根据openId查询员工信息详情查询异常"
                ) {
                  this.$router.push({
                    name: "binding",
                    query: { openId: this.openId },
                  });
                }
              }
              if (errRes.code == 500) {
                this.$message.error(errRes.msg);
                if (
                  errRes.msg == "You have a openId is undefined" ||
                  errRes.msg == "H5根据openId查询员工信息详情查询异常"
                ) {
                  this.$router.push({
                    name: "binding",
                    query: { openId: this.openId },
                  });
                }
              }
            }
          );
        },
        getDate(date){
            this.attendantDate = date;
            this.initData();
        },

        // 当前日期
        thisMonth(){
            var date = new Date()
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var strDate = date.getDate();
            if (month >= 1 && month <= 9) {
                month = "0" + month;
            }
            if (strDate >= 0 && strDate <= 9) {
                strDate = "0" + strDate;
            }
            this.attendantDate = year + '-' + month + '-' + strDate
            this.currentMonth = year + '-' + month
        },
        // 查询某员工某月打卡日志
        queryAttendanceMonth() {
          var data = {
            attendantDate: this.currentMonth,
          };
          this.$http.wxqueryAttendanceMonthByKayan(
            data,
            (res) => {
              if (res.code == "SUCCESS") {
                if (res.status == 0) {
                  this.clockInMoreArr = [];
                  for (let i = 0; i < res.data.length; i++) {
                    this.clockInMoreArr.push({
                      date: res.data[i].attendanceDate,
                      className: res.data[i].attendanceStatus,
                    });
                  }
                }
              } else {
                this.$message.error(res.message);
              }
            },
            (errRes) => {
              this.$message.error(errRes.message);
            }
          );
        },
        // 去重
        unique(arr) {
            const res = new Map();
            return arr.filter((arr) => !res.has(arr.months) && res.set(arr.months, 1))
        },

        // 考勤统计
        statisticsBtn() {
          this.$router.push({
            path: "/attendance-statistics",
            query: { openId: this.openId, status: this.status },
          });
        },

        // 查看排班
        scheduleBtn() {
          this.$router.push({
            path: "/scheduling-check",
            query: { openId: this.openId, status: this.status },
          });
        },

        //选中某天
        clickDay(data) {
            var yearShow = data.split('/')[0]
            var monthShow = data.split('/')[1]
            var dateShow = data.split('/')[2]
            if (monthShow >= 1 && monthShow <= 9) {
                monthShow = "0" + monthShow;
            }
            if (dateShow >= 1 && dateShow <= 9) {
                dateShow = "0" + dateShow;
            }
            this.currentMonth = yearShow + "-" + monthShow;
            this.attendantDate = yearShow + "-" + monthShow + "-" + dateShow;
            this.initData();
            this.getClockIn();
        },

        //左右点击切换月份
        changeDate(data) {
            var yearShow = data.split('/')[0]
            var monthShow = data.split('/')[1]
            var dateShow = data.split('/')[2]
            if (monthShow >= 1 && monthShow <= 9) {
                monthShow = "0" + monthShow;
            }
            if (dateShow >= 1 && dateShow <= 9) {
                dateShow = "0" + dateShow;
            }
            this.currentMonth = yearShow + "-" + monthShow;
            this.attendantDate = yearShow + "-" + monthShow + "-" + dateShow;
            this.clockInLists = [];
            this.clockIn = 0;
            this.clockTypeList = [];
            this.queryAttendanceMonth();
            var currentMonthTime = dayjs().startOf("month").format("MM");
            if (currentMonthTime === monthShow) {
              this.attendantDate = dayjs().format("YYYY-MM-DD");
              this.initData();
              this.getClockIn();
            }
        },

        clickToday(data) {
        },

        //  不是本企业员工
        conformNoticeBtn(){
            setTimeout(() =>{
                //这个可以关闭安卓系统的手机
                document.addEventListener('WeixinJSBridgeReady', function(){ WeixinJSBridge.call('closeWindow'); }, false);
                //这个可以关闭ios系统的手机
                WeixinJSBridge.call("closeWindow");
            },500);
            setTimeout(() =>{
                this.dialogNoticeSmall = false;
            },600);
        }
    },
    mounted(){
        this.$emit('refreshbizlines','考勤日历','returnNone');
        this.status = this.$route.query.status;
        this.openId = this.$route.query.openId;
        var openId = localStorage.getItem('openId');
        if(this.status == 0){
            if(openId == '' || openId == null){
                this.$router.push({ name:'binding',query:{openId:this.openId}})
            }else{
                // localStorage.setItem('openId',this.openId);
                this.queryEmployeeDetail();
                // this.thisMonth();
                // this.queryAttendanceMonth();
                // this.initData();
            }
        }else if(this.status == 1){
            this.noticeShow = '您不是本企业员工，如有问题，请联系管理员'
            this.dialogNoticeSmall = true;
            localStorage.clear();
        }else if(this.status == 2){
            this.noticeShow = '本工号员工已离职，如有问题，请联系管理员'
            this.dialogNoticeSmall = true;
            localStorage.clear();
        }
    }
}
</script>
<style  >
.calendarBox .BLUE::after {
  position: absolute;
  top: 20px;
  left: 20;
  content: "●";
  color: #1890ff;
  font-size: 5px;
}
.calendarBox .RED::after {
  position: absolute;
  top: 20px;
  left: 20;
  content: "●";
  color: red;
  font-size: 5px;
}
.calendarBox .ORANGE::after {
  position: absolute;
  top: 20px;
  left: 20;
  content: "●";
  color: orange;
  font-size: 5px;
}
.calendarBox .YELLOW::after {
  position: absolute;
  top: 20px;
  left: 20;
  content: "●";
  color: yellow;
  font-size: 5px;
}
.calendarBox .GREEN::after {
  position: absolute;
  top: 20px;
  left: 20;
  content: "●";
  color: #33cc00;
  font-size: 5px;
}
.clockTypeList {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 0 10px 15px;
}
.clockType {
  margin-right: 5px;
}
.clockType::before {
  margin-right: 5px;
  content: "●";
  font-size: 20px;
}
.clockTypeList .BLUE::before {
  color: #1890ff;
}
.clockTypeList .GREEN::before {
  color: #33cc00;
}
.clockTypeList .RED::before {
  color: red;
}
.clockTypeList .YELLOW::before {
  color: yellow;
}
.clockTypeList .ORANGE::before {
  color: orange;
}
</style>
