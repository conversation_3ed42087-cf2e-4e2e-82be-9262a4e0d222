 <template>
  <div class="schedulingBox">
    <div class="navScheduling">
      <div class="schedulingNavActive" @click="personnelBtn">按人员排班</div>
      <div class="schedulingNav" @click="dateBtn">按日期排班</div>
    </div>
    <div class="schedulingEmployeesBox">
      <div class="block">
        <el-date-picker
          v-model="selectDate"
          type="month"
          placeholder="选择月"
          :clearable="false"
          @change="selectTime"
          value-format="yyyy-MM"
        >
        </el-date-picker>
      </div>
      <div class="checkEmployees">
         <el-input
          placeholder="请输入工号或者姓名"
          v-model="empId"
          clearable
          @input="queryChildUser"
        >
        </el-input>
      </div>
      <div class="employeesLists">
        <div
          class="employeesList"
          v-if="employeesLists.length <= 0"
          style="justify-content: center"
        >
          <div>暂无数据！</div>
        </div>
        <div
          v-else
          class="employeesList"
          v-for="(item, index) in employeesLists"
          :key="index"
          @click="checkScheduling(item.empId)"
        >
          <div>{{ item.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      selectDate: "",
      employeesLists: [],
      employeesOption: [],
      empId: "",
    };
  },
  methods: {
    // 按照人员排班 nav
    personnelBtn() {
      this.$router.push({ path: "/scheduling" });
    },

    // 按照日期排班 nav
    dateBtn() {
      this.$router.push({ path: "/scheduling-date" });
    },

    // 查询考勤
    selectTime(value) {
      this.selectDate = value;
      this.queryChildUser();
    },

    // 当前月
    thisMonth() {
      var date = new Date();
      var year = date.getFullYear();
      var month = date.getMonth() + 1;
      var strDate = date.getDate();
      if (month >= 1 && month <= 9) {
        month = "0" + month;
      }
      this.selectDate = year + "-" + month;
    },

    // 查询当前主管所有下级员工
    queryChildUser() {
      let data = {
        scheduleDate: "",
        empId: this.empId,
      };
      this.$http.wxqueryChildUser(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            this.employeesLists = res.data;
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 查询当前主管]下级员工
    getChildUser(empId) {
      if (empId !== "") {
        let data = {
          empId: empId,
        };
        this.$http.wxqueryChildUser(
          data,
          (res) => {
            if (res.code == "SUCCESS") {
              this.employeesOption = res.data;
            } else {
              this.employeesOption = [];
            }
          },
          (errRes) => {
            this.employeesOption = [];
          }
        );
      } else {
        this.options = [];
      }
    },

    // 选择日期排班
    checkScheduling(id) {
      this.$router.push({
        path: "/scheduling-dateshow",
        query: { id, date: this.selectDate },
      });
    },
  },
  mounted() {
    this.$emit("refreshbizlines", "排班");
    this.thisMonth();
    this.queryChildUser();
  },
};
</script>