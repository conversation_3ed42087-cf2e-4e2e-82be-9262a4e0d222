<template>
  <div class="overtimeConfirmationBox backcolorBox bottomBox">
    <div class="overtimeConfirmationLists">
      <div class="list">
        <div>部门</div>
        <div>{{ department }}</div>
      </div>
      <div class="list">
        <div>加班事由</div>
        <div>{{ reason }}</div>
      </div>
      <div class="list">
        <div>加班类型</div>
        <div>{{ overtimeType }}</div>
      </div>

      <!-- 加班员工 start -->
      <div class="addLists">
        <div class="lstTop">
          <div class="listTopLeft">
            <div class="timeShow">{{ startTime }}</div>
            -
            <div class="timeShow">{{ endTime }}</div>
          </div>
        </div>
        <div class="overTimeEmployees">
          <label for="">班次</label>
          <div class="edit">
            <span v-for="(em, emindex) in employeesLists" :key="emindex">
              {{ em.workType }}
            </span>
          </div>
        </div>
        <div class="overTimeEmployees">
          <label for="">加班员工</label>
          <div class="edit">
            <span
              v-for="(em, emindex) in employeesLists"
              :key="emindex"
              :class="[
                em.status == '2' ? 'greenMark' : '',
                em.status == '3' ? 'redMark' : '',
                em.status == '1' ? 'grayMark' : '',
              ]"
            >
              {{ em.overtimeUser }}
            </span>
          </div>
        </div>
      </div>
      <!-- 加班员工 end -->

      <!-- 员工确认状态 start -->
      <div
        class="confirmStatus"
        v-show="employeeStatus == 2 || employeeStatus == 3"
      >
        <div
          class="statusList"
          v-show="confirmStatus != '' && confirmStatus != null"
        >
          <div>当前状态：</div>
          <div>{{ confirmStatus }}</div>
        </div>
        <div
          class="statusList"
          v-show="rejectReason != '' && rejectReason != null"
        >
          <div>拒绝原因：</div>
          <div>{{ rejectReason }}</div>
        </div>
      </div>
      <!-- 员工确认状态 end -->
    </div>

    <!-- 员工确认状态 start -->
    <div class="overTimeEmployeeColor">
      <div class="greenMark"><span class="greenBgMark"></span>已确认</div>
      <div class="redMark"><span class="redBgMark"></span>拒绝</div>
      <div class="grayMark"><span class="grayBgMark"></span>未答复</div>
    </div>
    <!-- 员工确认状态 end -->

    <!-- 员工操作 start -->
    <div class="bottomBtnBox">
      <el-button v-show="operateStatus == 1" @click="rejectBtn">拒绝</el-button>
      <el-button v-show="operateStatus == 1" type="primary" @click="confirmBtn"
        >确认</el-button
      >
      <el-button
        v-show="employeeStatus == 3"
        type="primary"
        @click="withdrawReject"
        >撤回拒绝操作</el-button
      >
      <el-button
        v-show="employeeStatus == 2"
        type="primary"
        @click="withdrawSubmit"
        >撤回确认操作</el-button
      >
    </div>
    <!-- 员工操作 end -->

    <!-- 拒绝原因 start -->
    <el-dialog
      title="拒绝原因"
      :visible.sync="dialogRemark"
      :close-on-click-modal="false"
      :show-close="false"
      class="dialogRemark"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        class="demo-ruleForm"
      >
        <el-form-item prop="remark" class="remark">
          <el-input
            type="textarea"
            :rows="3"
            v-model="ruleForm.remark"
            auto-complete="off"
            placeholder="输入内容"
            v-reset-page
          >
          </el-input>
        </el-form-item>

        <el-form-item class="footBtn">
          <el-button type="text" @click="cancelBtn('ruleForm')"
            >取 消</el-button
          >
          <el-button type="text" @click="submitForm('ruleForm')"
            >确认拒绝</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 拒绝原因 end -->
  </div>
</template>
<script>
export default {
  data() {
    var checkRemark = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("输入内容!"));
      } else {
        callback();
      }
    };
    return {
      isLeader: true,
      employeeStatus: "", // 显示员工 操作状态  3 撤回拒绝操作 2 撤回确认操作
      department: "", // 部门
      reason: "", // 原因
      multiple: "", // 倍
      startTime: "", // 开始时间
      endTime: "", // 结束时间
      employeesLists: [], // 员工
      overtimeType: "", // 加班类型
      confirmStatus: "", // 员工操作状态
      operateStatus: "", // 确认、拒绝状态
      rejectReason: "", // 员工操作状态 原因
      processinstanceid: "",
      dialogRemark: false,
      ruleForm: {
        remark: "",
      },
      rules: {
        remark: [{ validator: checkRemark, trigger: "blur" }],
      },
      processInstanceIds: [],
    };
  },
  methods: {
    // 员工数据渲染
    initData() {
      let data = {
        processinstanceid: this.processInstanceIds,
      };
      this.$http.wxqueryOvertimeDetail(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            if (res.status == 0) {
              var dataShow = res.data.apply;
              var applyList = res.data.applyList;
              this.department = dataShow.department;
              this.reason = dataShow.reason;
              this.overtimeType = dataShow.overtimeTypeName;
              this.multiple = dataShow.multiple;
              this.startTime = dataShow.startTime;
              this.endTime = dataShow.endTime;
              this.employeesLists = applyList;
              // if (dataShow.status == 1) {
              if (applyList.length > 0) {
                if (applyList[0].status == 1 && res.data.apply.status != 4 && res.data.apply.status != 6) {
                  this.operateStatus = 1;
                }
                this.employeeStatus = applyList[0].status; // 状态 1：未答复 2：已确认 3：拒绝 4：领班确认 5：领班取消 6：经理通过 7：经理驳回 8：超时自动取消
                this.confirmStatus = applyList[0].statusName;
                this.rejectReason = applyList[0].remark;
              }
              // }
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          if (errRes.code == 500) {
            this.$message.error(errRes.msg);
          }
          this.$message.error(errRes.message);
        }
      );
    },

    // 员工确认
    confirmBtn() {
      let data = {
        processinstanceid: this.processInstanceIds,
        status: 1, // 1：同意 2：拒绝,默认同意
      };
      this.$http.wxemployeeApprove(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            if (res.status == 0) {
              this.$message.success(res.message);
              setTimeout(() => {
                this.$router.push({
                  path: "/overtime-approval",
                  query: { pendingStatus: 3 },
                });
              }, 1000);
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          if (errRes.code == 500) {
            this.$message.error(errRes.msg);
          }
          this.$message.error(errRes.message);
        }
      );
    },

    // 拒绝
    rejectBtn() {
      this.dialogRemark = true;
    },
    // 拒绝 驳回 取消
    cancelBtn(formName) {
      this.dialogRemark = false;
      this.$refs[formName].resetFields();
    },

    // 确认 拒绝  驳回
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = {
            processinstanceid: this.processInstanceIds,
            remark: this.ruleForm.remark,
            status: 2, // 1：同意 2：拒绝,默认同意
          };
          this.$http.wxemployeeApprove(
            data,
            (res) => {
              if (res.code == "SUCCESS") {
                if (res.status == 0) {
                  this.$message.success(res.message);
                  setTimeout(() => {
                    this.$router.push({
                      path: "/overtime-approval",
                      query: { pendingStatus: 3 },
                    });
                  }, 1000);
                  this.dialogRemark = false;
                  this.$refs[formName].resetFields();
                }
              } else {
                this.$message.error(res.message);
              }
            },
            (errRes) => {
              if (errRes.code == 500) {
                this.$message.error(errRes.msg);
              }
              this.$message.error(errRes.message);
            }
          );
        } else {
          return false;
        }
      });
    },

    // 撤回拒绝操作
    withdrawReject() {
      let data = {
        processinstanceid: this.processInstanceIds,
        status: 2, // 1：同意 2：拒绝,默认同意
      };
      this.$http.wxcancelApprove(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            if (res.status == 0) {
              this.$message.success(res.message);
              setTimeout(() => {
                this.$router.push({
                  path: "/my-workbench",
                  query: { backStatus: 0 },
                });
              }, 1000);
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          if (errRes.code == 500) {
            this.$message.error(errRes.msg);
          }
          this.$message.error(errRes.message);
        }
      );
    },

    // 撤回确认操作
    withdrawSubmit() {
      let data = {
        processinstanceid: this.processInstanceIds,
        status: 1, // 1：同意 2：拒绝,默认同意
      };
      this.$http.wxcancelApprove(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            if (res.status == 0) {
              this.$message.success(res.message);
              setTimeout(() => {
                this.$router.push({
                  path: "/my-workbench",
                  query: { backStatus: 0 },
                });
              }, 1000);
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          if (errRes.code == 500) {
            this.$message.error(errRes.msg);
          }
          this.$message.error(errRes.message);
        }
      );
    },
  },
  mounted() {
    this.$emit("refreshbizlines", "加班确认");
    if (this.$route.query.processinstanceid) {
      this.processinstanceid = this.$route.query.processinstanceid;
      let newStr = this.$route.query.processinstanceid.split(",");
      this.processInstanceIds = newStr;
    }
    if (this.$route.query.processInstanceIds) {
      this.processInstanceIds = JSON.parse(
        this.$route.query.processInstanceIds
      );
    }
    if (
      this.myStatus == undefined &&
      this.pendingStatus == undefined &&
      this.alreadyStatus == undefined
    ) {
      this.pendingStatus = 3;
    }
    this.initData();
  },
};
</script>
