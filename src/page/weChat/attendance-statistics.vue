<template>
    <div class="statisticsBox">
       <div class="navList">
           <div class="ashContent">
                <div> 姓名：<span>{{userName}}</span> </div>
                <div> 班组：<span>{{team}}</span></div>
                <div>班次：<span>{{shifts}}</span></div>
            </div>
           <div class="navBtn" @click="attendanceBtn">考勤日历</div>
       </div>
       <div class="statisticsShow">
            <div class="dateList">
                <h2>{{ getStatus(confirmStatus, sendStatus) }}</h2>
                <div class="block">
                    <el-date-picker
                        v-model="attendantDate"
                        type="month"
                        placeholder="选择月"
                        :clearable = false
                        @change="selectTime"
                        value-format='yyyy-MM'
                        :picker-options="pickerOptions">
                    </el-date-picker>
                </div>
                <!-- <el-select v-model="yearShow" filterable placeholder="请选择">
                    <el-option
                    v-for="item in yearOptions"
                    :key="item"
                    :label="item"
                    :value="item">
                    </el-option>
                </el-select>
                <el-select v-model="monthShow" filterable placeholder="请选择">
                    <el-option
                    v-for="item in monthOptions"
                    :key="item"
                    :label="item"
                    :value="item">
                    </el-option>
                </el-select> -->
            </div>
            <div class="notice">以下信息来自嘉扬系统，以嘉扬系统信息为准</div>
           <div class="statisticsListShow">
             <div
               class="statisticsLists"
               v-for="(item, index) in monthlyAttendanceList"
               :key="index"
             >
               <div class="statisticsList">{{ item.key }}</div>
               <div class="statisticsList">{{ item.value }}</div>
             </div>
           </div>
       </div>
       <div
           class="bottomBtnBox"
           v-if="getStatus(confirmStatus, sendStatus) == '确认中'"
         >
           <el-button type="primary" @click="dialogNotice = true"
           >确认本月考勤</el-button
           >
       </div>
        <el-dialog
          :visible.sync="dialogNotice"
          :close-on-click-modal="false"
          :show-close="false"
          class="dialogNotice dialogNoticeMore"
          center
        >
          <div class="noticeShow">
            <div>本月考勤经本人确认，考勤无误</div>
          </div>
          <span slot="footer" class="dialog-footer dialog-footerCenter">
          <el-button class="borderNone" @click="dialogNotice = false"
          >取 消</el-button
          >
          <el-button type="text" @click="conformBtn">确定</el-button>
        </span>
        </el-dialog>
    </div>
</template>
<script>
export default {
    data() {
        return {
            attendantDate:'',
            userName:'',
            team:'',
            shifts:'',
            // yearShow:'2019',
            // yearOptions:[2019,2018,2017],
            // monthShow:'Sep',
            // monthOptions:['Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec'],
            pickerOptions: {
                disabledDate: (time) =>{
                    var date = new Date();
                    var year = date.getFullYear();
                    var month = date.getMonth() + 1;
                    if (month >= 1 && month <= 9) {
                        month = "0" + month;
                    }
                    var currentdate = year.toString()  + month.toString();

                    var timeyear = time.getFullYear();
                    var timemonth = time.getMonth() + 1;
                    if (timemonth >= 1 && timemonth <= 9) {
                        timemonth = "0" + timemonth;
                    }
                    var timedate = timeyear.toString() + timemonth.toString();
                    return currentdate < timedate;
                },
            },
            monthlyAttendanceList: [],
            confirmStatus: "",
            sendStatus: "",
            dialogNotice: false,
        };
    },
    methods: {
        // 数据渲染
        initData(){
            let data = {
              term: this.attendantDate + "-" + "01",
              empId: localStorage.getItem("openId"),
            };
            this.$http.wxMonthlyAttendanceConfirm(
              data,
              (res) => {
                if (res.code == "SUCCESS") {
                  if (res.status == 0) {
                    var arr = [];
                    if (res.data != null && JSON.stringify(res.data) !== "{}") {
                      Object.keys(res.data).forEach(function (key) {
                        if (key != "confirmStatus" && key != "sendStatus") {
                          arr.push({ key: key, value: res.data[key] });
                        }
                      });
                      // sendStatus: 0: 未发起确认 1: 已发起，确认中，2: 已完成
                      // confirmStatus: 1: 已确认 0: 未确认
                      this.confirmStatus = res.data.confirmStatus;
                      this.sendStatus = res.data.sendStatus;
                      this.monthlyAttendanceList = arr;
                    } else {
                      this.monthlyAttendanceList = [];
                      this.confirmStatus = "";
                      this.sendStatus = "";
                    }
                  } else {
                    this.$message.error(res.message);
                  }
                } else {
                  this.$message.error(res.message);
                }
              },
              (errRes) => {
                this.$message.error(errRes.message ? errRes.message : errRes.msg);
              }
            );
        },
        getStatus(confirmStatus, sendStatus) {
          // sendStatus: 0: 未发起确认 1: 已发起，确认中，2: 已完成
          // confirmStatus: 1: 已确认 0: 未确认
          if (sendStatus == 0) {
            return "考勤确认未开始";
          } else if (sendStatus == 1 && confirmStatus == 0) {
            return "确认中";
          } else if (sendStatus == 2) {
            return "已确认";
          } else {
            return " ";
          }
        },
        // H5根据员工号查询员工信息详情
        queryPhoneEmployeeDetail(){
            let data = {
                id: localStorage.getItem('openId'),
                // id: 'NB004400',
            }
            this.$http.wxqueryPhoneEmployeeDetail(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.userName = res.data.data.name;
                    this.team = res.data.data.workTeam;
                    this.shifts = res.data.data.worktype;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                if(errRes.code == 500){
                    this.$message.error(errRes.msg);
                }
            })
        },

        // 当前月
        thisMonth(){
            var date = new Date()
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            if (month >= 1 && month <= 9) {
                month = "0" + month;
            }
            this.attendantDate = year + '-' + month
        },

        // 查询考勤
        selectTime(value){
            this.attendantDate = value;
            this.initData();
        },

        // 考勤日历
        attendanceBtn(){
          this.$router.push({
            path: "/time-tag",
            query: { openId: this.openId, status: this.status },
          });
        },
        conformBtn() {
          let data = {
            term: this.attendantDate + "-" + "01",
            empId: localStorage.getItem("openId"),
          };
          this.$http.wxConfirmMonthlyAttendance(
            data,
            (res) => {
              if (res.code == "SUCCESS") {
                this.$message.success(res.message);
                this.dialogNotice = false;
                this.initData();
              } else {
                this.$message.error(res.message);
              }
            },
            (errRes) => {
              this.$message.error(errRes.message ? errRes.message : errRes.msg);
            }
          );
        },
    },

    mounted(){
        this.status = this.$route.query.status;
        this.openId = this.$route.query.openId;
        this.$emit('refreshbizlines','考勤统计');
        this.thisMonth();
        this.queryPhoneEmployeeDetail();
        this.initData();
    }
}
</script>
