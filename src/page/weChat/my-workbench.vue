<template>
  <div class="myWorkbenchBox">
    <div class="workbenchLists">
      <div class="workbenchList">
        <div class="listTitle">排班管理</div>
        <div class="listContent">
          <div class="listDes" @click="schedulingBtn">
            <div class="listSubTitle">排班</div>
          </div>
        </div>
      </div>
      <div class="workbenchList">
        <div class="listTitle">待处理</div>
        <div class="listContent">
          <div class="listDes" @click="leaveApprovalBtn">
            <div class="listSubTitle">
              请假审批
              <span
                class="noticePending"
                v-show="leaveApprovalNum != '' && leaveApprovalNum != 0"
              >
                {{ leaveApprovalNum }}
              </span>
            </div>
          </div>
          <div class="listDes" @click="overtimeApprovalBtn">
            <div class="listSubTitle">
              加班审批
              <span
                class="noticePending"
                v-show="overtimeApprovalNum != '' && overtimeApprovalNum != 0"
              >
                {{ overtimeApprovalNum }}
              </span>
            </div>
          </div>
          <div class="listDes" @click="overtimeConfirmationBtn">
            <div class="listSubTitle">
              加班确认
              <span
                class="noticePending"
                v-show="overtimeConfirmNum != '' && overtimeConfirmNum != 0"
              >
                {{ overtimeConfirmNum }}
              </span>
            </div>
          </div>
          <!-- <div class="listDes" @click="supplementarycardApprovalBtn">
            <div class="listSubTitle">
              补卡审批
              <span
                class="noticePending"
                v-show="supplementarycardNum != '' && supplementarycardNum != 0"
              >
                {{ supplementarycardNum }}
              </span>
            </div>
          </div> -->
          <div class="listDes" @click="secondedApprovalBtn">
            <div class="listSubTitle">
              借调审批
              <span
                class="noticePending"
                v-show="secondedNum != '' && secondedNum != 0"
              >
                {{ secondedNum }}
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="workbenchList">
        <div class="listTitle">已处理</div>
        <div class="listContent">
          <div class="listDes" @click="leaveApprovalAlreadyBtn">
            <div class="listSubTitle">请假审批</div>
          </div>
          <div class="listDes" @click="overtimeApprovalAlreadyBtn">
            <div class="listSubTitle">加班审批</div>
          </div>
          <div class="listDes" @click="overtimeConfirmationAlreadyBtn">
            <div class="listSubTitle">加班确认</div>
          </div>
          <!-- <div class="listDes" @click="supplementarycardApprovalAlreadyBtn">
            <div class="listSubTitle">补卡审批</div>
          </div> -->
          <div class="listDes" @click="secondedAlreadyBtn">
            <div class="listSubTitle">借调审批</div>
          </div>
        </div>
      </div>
      <div class="workbenchList">
        <div class="listTitle">发起新的申请</div>
        <div class="listContent">
          <div class="listDes" @click="leaveApplyBtn">
            <div class="listSubTitle">请假</div>
          </div>
          <div class="listDes" @click="overtimeApplyBtn">
            <div class="listSubTitle">加班</div>
          </div>
          <!-- <div class="listDes" @click="supplementarycardApplyBtn">
            <div class="listSubTitle">补卡</div>
          </div> -->
          <div class="listDes" @click="secondedApplyBtn">
            <div class="listSubTitle">借调</div>
          </div>
        </div>
      </div>
      <div class="workbenchList">
        <div class="listTitle">我的申请记录</div>
        <div class="listContent">
          <div class="listDes" @click="myLeaveBtn">
            <div class="listSubTitle">我的请假申请</div>
          </div>
          <div class="listDes" @click="myOvertimeBtn">
            <div class="listSubTitle">我的加班申请</div>
          </div>
          <!-- <div class="listDes" @click="mySupplementarycard">
            <div class="listSubTitle">我的补卡申请</div>
          </div> -->
          <div class="listDes" @click="mySecondedBtn">
            <div class="listSubTitle">我的借调申请</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 不是本企业员工提示 start -->
    <el-dialog
      title="提示"
      :visible.sync="dialogNoticeSmall"
      :close-on-click-modal="false"
      :show-close="false"
      class="dialogNotice dialogNoticeSmall"
    >
      <div class="noticeShow">
        <div>{{ noticeShow }}</div>
      </div>
      <span slot="footer" class="dialog-footer dialog-footerCenter">
        <el-button type="text" @click="conformNoticeBtn">确定</el-button>
      </span>
    </el-dialog>
    <!-- 不是本企业员工提示 end -->
  </div>
</template>
<script>
export default {
  data() {
    return {
      dialogNoticeSmall: false,
      noticeShow: "",
      leaveApprovalNum: "",
      overtimeApprovalNum: "",
      overtimeConfirmNum: "",
      supplementarycardNum: "",
      secondedNum: "",
      department: "",
      plant: "", //厂区
      workshop: "", //车间
      workTeam: "",
      isLeader: "",
      backStatus: "",
      staff: "", // 0：不是员工
    };
  },
  methods: {
    // 待处理 数量
    initData() {
      this.$http.wxqueryProcessNumber(
        null,
        (res) => {
          if (res.code == "SUCCESS") {
            this.leaveApprovalNum = res.data.leaveCount;
            this.overtimeApprovalNum = res.data.overtimeCount;
            this.overtimeConfirmNum = res.data.overtime;
            this.supplementarycardNum = res.data.replenish;
            this.secondedNum = res.data.borrow;
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          if (errRes.code == 500) {
            if ((errRes.msg = "You have a openId is undefined")) {
              this.$router.push({
                name: "binding",
                query: { openId: this.openId },
              });
              this.$message.error(errRes.msg);
            }
          }
          this.$message.error(errRes.message);
        }
      );
    },

    // H5根据员工号查询员工信息详情 (获取部门 车间 厂区)
    queryPhoneEmployeeDetail() {
      let data = {
        id: localStorage.getItem("openId"),
      };
      this.$http.wxqueryPhoneEmployeeDetail(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            this.department = res.data.data.department;
            this.plant = res.data.data.plant;
            this.workshop = res.data.data.workshop;
            this.staff = res.data.data.staff;
            this.workTeam = res.data.data.workTeam;
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          if (errRes.code == 500) {
            this.$message.error(errRes.msg);
          }
        }
      );
    },

    // 查询员工信息详情 (判断是否领班)
    checkEmployeeDetail() {
      this.$http.wxcheckEmployeeDetail(
        null,
        (res) => {
          if (res.code == "SUCCESS") {
            this.isLeader = res.data.isLeader;
            localStorage.setItem("checkEmployee", res.data.isLeader);
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 排班
    schedulingBtn() {
      if (this.isLeader == false) {
        this.$message.warning("您不是领班");
      } else {
        this.$router.push({ path: "/scheduling" });
      }
    },

    // 待处理 请假审批
    leaveApprovalBtn() {
      // 待处理 pendingStatus 1、请假审批; 2、加班审批; 3、加班确认; 4、补卡审批; 5、借调审批
      if (this.leaveApprovalNum != 0 || this.leaveApprovalNum != "") {
        this.$router.push({
          path: "/overtime-approval",
          query: { pendingStatus: 1 },
        });
      }
    },

    // 已处理 请假审批
    leaveApprovalAlreadyBtn() {
      // 已处理 alreadyStatus 1、请假审批; 2、加班审批; 3、加班确认; 4、补卡审批  5、借调审批
      this.$router.push({
        path: "/overtime-approval",
        query: { alreadyStatus: 1 },
      });
    },

    // 请假申请
    leaveApplyBtn() {
      this.$router.push({
        path: "/leave-approval",
        query: {
          department: this.department,
          plant: this.plant,
          workshop: this.workshop,
        },
      });
    },

    // 我的请假申请  // myStatus 1、我的请假申请; 2、我的加班申请; 3、我的补卡申请; 4、我的借调申请
    myLeaveBtn() {
      this.$router.push({ path: "/overtime-approval", query: { myStatus: 1 } });
    },

    // 待处理 加班审批
    overtimeApprovalBtn() {
      if (this.overtimeApprovalNum != 0 || this.overtimeApprovalNum != "") {
        this.$router.push({
          path: "/overtime-approval",
          query: { pendingStatus: 2 },
        });
      }
    },

    // 已处理 加班审批
    overtimeApprovalAlreadyBtn() {
      this.$router.push({
        path: "/overtime-approval",
        query: { alreadyStatus: 2 },
      });
    },

    // 加班申请
    overtimeApplyBtn() {
      if (this.isLeader == false) {
        this.$message.warning("您不是领班");
      } else {
        this.$router.push({
          path: "/overtime-application",
          query: {
            department: this.department,
            plant: this.plant,
            workshop: this.workshop,
          },
        });
      }
    },

    // 我的加班申请
    myOvertimeBtn() {
      if (this.isLeader == false) {
        this.$message.warning("您不是领班");
      } else {
        this.$router.push({
          path: "/overtime-approval",
          query: { myStatus: 2 },
        });
      }
    },

    // 待处理 加班确认
    overtimeConfirmationBtn() {
      if (this.overtimeConfirmNum != 0 || this.overtimeConfirmNum != "") {
        this.$router.push({
          path: "/overtime-approval",
          query: { pendingStatus: 3 },
        });
      }
    },

    // 已处理 加班确认
    overtimeConfirmationAlreadyBtn() {
      this.$router.push({
        path: "/overtime-approval",
        query: { alreadyStatus: 3 },
      });
    },

    // 待处理 补卡审批
    supplementarycardApprovalBtn() {
      if (this.supplementarycardNum != 0 || this.supplementarycardNum != "") {
        this.$router.push({
          path: "/overtime-approval",
          query: { pendingStatus: 4 },
        });
      }
    },

    // 已处理 补卡审批
    supplementarycardApprovalAlreadyBtn() {
      this.$router.push({
        path: "/overtime-approval",
        query: { alreadyStatus: 4 },
      });
    },

    // 补卡
    supplementarycardApplyBtn() {
      this.$router.push({ path: "/supplementarycard-application" });
    },

    // 我的补卡申请
    mySupplementarycard() {
      this.$router.push({ path: "/overtime-approval", query: { myStatus: 3 } });
    },

    // 待处理 借调申请
    secondedApprovalBtn() {
      if (this.secondedNum != 0 || this.secondedNum != "") {
        this.$router.push({
          path: "/overtime-approval",
          query: { pendingStatus: 5 },
        });
      }
    },

    // 已处理借调申请
    secondedAlreadyBtn() {
      if (this.isLeader == false) {
        this.$message.warning("您不是领班");
      } else {
        this.$router.push({
          path: "/overtime-approval",
          query: { alreadyStatus: 5 },
        });
      }
    },

    // 借调申请
    secondedApplyBtn() {
      if (this.isLeader == false) {
        this.$message.warning("您不是领班");
      } else {
        this.$router.push({
          path: "/seconded-application",
          query: { workTeam: this.workTeam },
        });
      }
    },

    // 我的借调申请
    mySecondedBtn() {
      if (this.isLeader == false) {
        this.$message.warning("您不是领班");
      } else {
        this.$router.push({
          path: "/overtime-approval",
          query: { myStatus: 4 },
        });
      }
    },

    //  不是本企业员工
    conformNoticeBtn() {
      setTimeout(() => {
        WeixinJSBridge.call("closeWindow");
      }, 1500);
      this.dialogNoticeSmall = false;
    },
  },
  mounted() {
    this.$emit("refreshbizlines", "我的工作台", "returnNone");
    this.status = this.$route.query.status;
    this.openId = this.$route.query.openId;
    let openId = localStorage.getItem("openId");
    this.backStatus = this.$route.query.backStatus;
    if (this.status == 0) {
      if (openId == "" || openId == null) {
        this.$router.push({ name: "binding", query: { openId: this.openId } });
      } else {
        localStorage.setItem("openId", this.openId);
        this.checkEmployeeDetail();
        this.queryPhoneEmployeeDetail();
        this.initData();
      }
    } else if (this.status == 1) {
      this.noticeShow = "您不是本企业员工，如有问题，请联系管理员";
      this.dialogNoticeSmall = true;
      localStorage.clear();
    } else if (this.status == 2) {
      this.noticeShow = "本工号员工已离职，如有问题，请联系管理员";
      this.dialogNoticeSmall = true;
      localStorage.clear();
    }
    if (this.backStatus == 0) {
      this.checkEmployeeDetail();
      this.queryPhoneEmployeeDetail();
      this.initData();
    }
  },
};
</script>