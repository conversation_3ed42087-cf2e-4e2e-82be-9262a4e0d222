<template>
   <div class="restreminderBox">
        <div class="navListBetween">
            <div class="ashContent">{{department}}</div>
            <div class="ashContent">
                <span> {{dateShow}}</span>
                <span> {{weekShow}}</span>
            </div>
        </div>
        <div class="tableBox">
            <el-table
                :data="tableData" border style="width: 100%">
                <el-table-column prop="name" label="姓名"></el-table-column>
                <el-table-column prop="workTeam" label="班组"></el-table-column>
                <el-table-column prop="workType" label="班次"></el-table-column>
                <el-table-column prop="count" label="连续工作(天)"></el-table-column>
            </el-table>
        </div>
        <div class="bottomBtnBox">
            <el-button type="primary" @click="confirmBtn">确认</el-button>
        </div>
   </div>
  
</template>
<script>
export default {
    data(){
        
        return{
            department:'',
            weekShow:'',
            dateShow:'',
            tableData: []
        }
    },
    methods: {
        // H5根据员工号查询员工信息详情 (获取部门 车间 厂区)
        queryPhoneEmployeeDetail(){
            let data = {
                id: localStorage.getItem('openId'),	
                // id: 'NB004400',	
            }
            this.$http.wxqueryPhoneEmployeeDetail(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.department = res.data.data.department;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                if(errRes.code == 500){
                    this.$message.error(errRes.msg);
                }
            })
        },

        // 员工休息提醒
        queryCautionEmployee(){
            this.$http.wxqueryCautionEmployee(null,(res)=>{
                if(res.code == 'SUCCESS'){
                    if(res.status == 0){
                        this.dateShow  = res.data.date;
                        this.weekShow = res.data.week;
                        this.tableData = res.data.data;
                    }
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                if(errRes.code == 500){
                    this.$message.error(errRes.msg);
                }
                this.$message.error(errRes.message);
            })
        },

        confirmBtn(){
            setTimeout(() =>{
                WeixinJSBridge.call("closeWindow");
            },1500);

        }
    },
    mounted(){
        this.$emit('refreshbizlines','员工休息提醒','returnNone'); 
        this.queryPhoneEmployeeDetail();
        this.queryCautionEmployee();
    }
}
</script>