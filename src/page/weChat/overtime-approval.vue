<template>
  <div class="overtimeApprovalBox">
    <div
      class="overtimeApprovalDes"
      v-infinite-scroll="loadMore"
      infinite-scroll-disabled="busy"
      infinite-scroll-distance="10"
    >
      <div
        class="flex-between"
        v-show="pendingStatus == 1 && approvalLists.length > 0"
      >
        <el-select
          v-model="leaveTypeValue"
          placeholder="事假"
          @change="leaveTypeChange"
          clearable
        >
          <el-option
            v-for="item in leaveTypeOptions"
            :key="item.value"
            :label="item.leaveTypeName"
            :value="item.leaveTypeId"
          >
            <span style="float: left">{{ item.leaveTypeName }}</span>
            <span>（{{ item.leaveCount }}）</span>
          </el-option>
        </el-select>
        <el-select
          v-model="applyTime"
          placeholder="申请时间"
          @change="applyTimeChange"
          clearable
        >
          <el-option
            v-for="item in applyTimeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
      <!-- 已处理请假审批筛选条件 -->
      <div
        class="filter-container"
        v-show="alreadyStatus == 1"
      >
        <div class="filter-row">
          <el-input
            v-model="filterApplicantEmpId"
            placeholder="申请人工号"
            clearable
            class="filter-input"
            size="small"
          ></el-input>
          <el-button
            type="primary"
            size="small"
            @click="handleFilter"
            :loading="filterLoading"
            class="filter-button"
          >筛选</el-button>
        </div>
        <div class="filter-row">
          <el-select
            v-model="filterLeaveType"
            placeholder="假别"
            clearable
            class="filter-select"
            size="small"
          >
            <el-option
              v-for="item in leaveTypeFilterOptions"
              :key="item.value"
              :label="item.leaveTypeName"
              :value="item.leaveTypeId"
            >
            </el-option>
          </el-select>
          <el-button
            size="small"
            @click="handleResetFilter"
            class="filter-button"
          >重置</el-button>
        </div>
      </div>
      <div
        class="approvalList"
        v-for="(item, index) in approvalLists"
        :key="index"
        @click="toDetailsBtn(item.processinstanceid)"
      >
        <div class="listLeft">
          <div
            class="leftDes"
            v-show="item.proposerName != null && item.proposerName != ''"
          >
            <div class="proposerName">{{ item.proposerName }}的班组申请</div>
          </div>
          <div
            class="leftDes"
            v-show="
              myStatus == 1 ||
              pendingStatus == 1 ||
              alreadyStatus == 1 ||
              myStatus == 4 ||
              pendingStatus == 4 ||
              alreadyStatus == 4
            "
          >
            <div class="listTitle">申请人：</div>
            <div
              v-if="myStatus == 1 || pendingStatus == 1 || alreadyStatus == 1"
              class="listContent"
            >
              {{ item.applyUserName }}
            </div>
            <div
              v-if="myStatus == 4 || pendingStatus == 4 || alreadyStatus == 4"
              class="listContent"
            >
              {{ item.replenishUserName }}
            </div>
          </div>
          <div
            class="leftDes"
            v-show="
              myStatus == 1 ||
              pendingStatus == 1 ||
              alreadyStatus == 1 ||
              myStatus == 2 ||
              pendingStatus == 2 ||
              alreadyStatus == 2 ||
              pendingStatus == 3 ||
              alreadyStatus == 3
            "
          >
            <div class="listTitle">所在部门：</div>
            <div class="listContent">{{ item.department }}</div>
          </div>

          <div
            class="leftDes"
            v-show="myStatus == 1 || pendingStatus == 1 || alreadyStatus == 1"
          >
            <div class="listTitle">请假类型：</div>
            <div class="listContent">{{ item.leaveTypeName }}</div>
          </div>

          <div
            class="leftDes"
            v-show="
              myStatus == 2 ||
              pendingStatus == 2 ||
              alreadyStatus == 2 ||
              pendingStatus == 3
            "
          >
            <div class="listTitle">加班事由：</div>
            <div class="listContent">{{ item.reason }}</div>
          </div>

          <div
            class="leftDes"
            v-show="myStatus == 4 || pendingStatus == 5 || alreadyStatus == 5"
          >
            <div class="listTitle">借出班组：</div>
            <div class="listContent">{{ item.borrowWorkTeam }}</div>
          </div>
          <div
            class="leftDes"
            v-show="myStatus == 4 || pendingStatus == 5 || alreadyStatus == 5"
          >
            <div class="listTitle">领班：</div>
            <div class="listContent">{{ item.leaderName }}</div>
          </div>

          <div
            class="leftDes"
            v-show="myStatus != 3 && pendingStatus != 4 && alreadyStatus != 4"
          >
            <div class="listTitle">开始时间：</div>
            <div class="listContent">{{ item.startTime }}</div>
          </div>
          <div
            class="leftDes"
            v-show="myStatus != 3 && pendingStatus != 4 && alreadyStatus != 4"
          >
            <div class="listTitle">结束时间：</div>
            <div class="listContent">{{ item.endTime }}</div>
          </div>
          <div class="leftDes">
            <div
              class="listTitle"
              v-show="myStatus == 1 || pendingStatus == 1 || alreadyStatus == 1"
            >
              请假时长：
            </div>
            <div class="listContent">{{ item.leaveCount }}</div>
          </div>

          <div
            class="leftDes"
            v-show="myStatus == 3 || pendingStatus == 4 || alreadyStatus == 4"
          >
            <div class="listTitle">补卡日期：</div>
            <div class="listContent">{{ item.replenishTime }}</div>
          </div>
          <!-- <div
            class="leftDes"
            v-show="myStatus == 3 || pendingStatus == 4 || alreadyStatus == 4"
          >
            <div class="listTitle">补卡开始时间：</div>
            <div class="listContent">{{ item.startTime }}</div>
          </div>
          <div
            class="leftDes"
            v-show="myStatus == 3 || pendingStatus == 4 || alreadyStatus == 4"
          >
            <div class="listTitle">补卡结束时间：</div>
            <div class="listContent">{{ item.endTime }}</div>
          </div> -->
          <div class="applyTime">{{ item.applyDate }}</div>
        </div>
        <div class="listRight">
          <span class="approvalStatus">{{ item.statusName }}</span>
          <i class="el-icon-arrow-right"></i>
        </div>
      </div>
      <div class="approvalListsNothing" v-show="approvalLists.length <= 0">
        暂无数据！
      </div>
      <div class="loadNothing" v-show="endNothing === true">没有更多的数据</div>
      <!-- 操作 start -->
      <div class="bottomBtnBox">
        <el-button
          v-if="
            (pendingStatus == 1 ||
              pendingStatus == 2 ||
              pendingStatus == 4 ||
              (pendingStatus == 5 && checkEmployee != 'true')) &&
            approvalLists.length > 0
          "
          type="primary"
          @click="batchApprovalBtn"
          :disabled="isDisable"
          >批量审批</el-button
        >
      </div>
      <!-- 操作 end -->
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      approvalLists: [],
      myStatus: "", // 1、我的请假申请; 2、我的加班申请; 3、我的补卡申请
      pendingStatus: "", // 待处理 1、请假审批; 2、加班审批; 3、加班确认; 4、补卡审批
      alreadyStatus: "", // 已处理 1、请假审批; 2、加班审批; 3、加班确认; 4、补卡审批
      busy: true,
      currentPage: 1,
      pagesize: 5,
      total: 0,
      endNothing: false,
      leaveTypeValue: "",
      applyTime: "",
      leaveTypeOptions: [],
      applyTimeOptions: [
        {
          value: "1",
          label: "申请时间升序",
        },
        {
          value: "2",
          label: "请假时长降序",
        },
      ],
      isDisable: false,
      checkEmployee: "",
      // 已处理请假审批筛选字段
      filterApplicantEmpId: "",
      filterLeaveType: "",
      leaveTypeFilterOptions: [],
      filterLoading: false,
    };
  },
  methods: {
    leaveTypeChange() {
      if (this.pendingStatus == 1) {
        this.initPendingLeave();
      }
      if (this.myStatus == 1) {
        this.initMyLeave();
      }
    },
    applyTimeChange() {
      if (this.pendingStatus == 1) {
        this.initPendingLeave();
      }
      if (this.myStatus == 1) {
        this.initMyLeave();
      }
    },
    // 查询请假申请记录数量
    queryLeaveTypeCount() {
      this.$http.wxqueryLeaveTypeCount(
        null,
        (res) => {
          if (res.code == "SUCCESS") {
            this.leaveTypeOptions = res.data;
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 我的请假审批
    initMyLeave(flag) {
      let data = {
        offset: this.pagesize,
        rows: this.currentPage,
        orderType: this.applyTime,
        type: this.leaveTypeValue,
      };
      this.$http.wxqueryLeavePage(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            if (flag) {
              //如果flag为true则表示分页
              this.approvalLists = this.approvalLists.concat(res.data.data); //concat数组串联进行合并
              if (res.data.data.length <= 0) {
                //如果数据加载完 那么禁用滚动时间 this.busy设置为true
                this.busy = true;
                this.endNothing = true;
              } else {
                this.busy = false;
              }
            } else {
              //第一次进入页面 完全不需要数据拼接的
              this.approvalLists = res.data.data;
              if (this.approvalLists.length >= this.pagesize) {
                this.busy = false;
              } else {
                this.busy = true;
              }
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },
    //当属性滚动的时候  加载  滚动加载
    loadMore() {
      this.busy = true; //将无限滚动给禁用
      setTimeout(() => {
        //发送请求有时间间隔第一个滚动时间结束后才发送第二个请求
        this.currentPage++; //滚动之后加载第二页
        // 我的申请记录
        if (this.myStatus == 1) {
          this.initMyLeave(true);
        } else if (this.myStatus == 2) {
          this.initMyOverTime(true);
        } else if (this.myStatus == 3) {
          this.initCardApply(true);
        } else if (this.myStatus == 4) {
          this.initMySeconded(true);
        }
        // 待处理
        if (this.pendingStatus == 3) {
          this.initPendingConfirmate(true);
        } else if (this.pendingStatus == 5) {
          this.initPendingSeconded(true);
        }
        // 已处理
        if (this.alreadyStatus == 1) {
          this.initAlreadyLeave(true);
        } else if (this.alreadyStatus == 2) {
          this.initAlreadyOverTime(true);
        } else if (this.alreadyStatus == 3) {
          this.initAlreadyConfirmate(true);
        } else if (this.alreadyStatus == 4) {
          this.initAlreadyCard(true);
        } else if (this.alreadyStatus == 5) {
          this.initAlreadySeconded(true);
        }
      }, 500);
    },

    // 待处理 请假审批
    initPendingLeave() {
      let data = {
        orderType: this.applyTime,
        type: this.leaveTypeValue,
      };
      this.$http.wxqueryProcessList(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            this.approvalLists = res.data;
            if (res.data.length <= 0) {
              setTimeout(() => {
                this.$router.push({
                  path: "/my-workbench",
                  query: { backStatus: 0 },
                });
              });
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 已处理 请假审批
    initAlreadyLeave(flag) {
      let data = {
        offset: this.pagesize,
        rows: this.currentPage,
        applyUserEmpId: this.filterApplicantEmpId, // 申请人工号筛选
        leaveTypeId: this.filterLeaveType, // 假别筛选
      };
      this.$http.wxqueryCompleteList(
        data,
        (res) => {
          this.filterLoading = false; // 关闭loading状态
          if (res.code == "SUCCESS") {
            if (flag) {
              //如果flag为true则表示分页
              this.approvalLists = this.approvalLists.concat(res.data.data); //concat数组串联进行合并
              if (res.data.data.length <= 0) {
                //如果数据加载完 那么禁用滚动时间 this.busy设置为true
                this.busy = true;
                this.endNothing = true;
              } else {
                this.busy = false;
              }
            } else {
              //第一次进入页面 完全不需要数据拼接的
              this.approvalLists = res.data.data;
              if (this.approvalLists.length >= this.pagesize) {
                this.busy = false;
              } else {
                this.busy = true;
              }
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.filterLoading = false; // 关闭loading状态
          this.$message.error(errRes.message);
        }
      );
    },

    // 我的加班审批
    initMyOverTime(flag) {
      let data = {
        offset: this.pagesize,
        rows: this.currentPage,
      };
      this.$http.wxqueryMyOvertimePage(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            if (flag) {
              //如果flag为true则表示分页
              this.approvalLists = this.approvalLists.concat(res.data.data); //concat数组串联进行合并
              if (res.data.data.length <= 0) {
                //如果数据加载完 那么禁用滚动时间 this.busy设置为true
                this.busy = true;
                this.endNothing = true;
              } else {
                this.busy = false;
              }
            } else {
              //第一次进入页面 完全不需要数据拼接的
              this.approvalLists = res.data.data;
              if (this.approvalLists.length >= this.pagesize) {
                this.busy = false;
              } else {
                this.busy = true;
              }
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 待处理 加班审批
    initPendingOverTime() {
      this.$http.wxqueryPendingOvertime(
        null,
        (res) => {
          if (res.code == "SUCCESS") {
            this.approvalLists = res.data.overtimeApplyVos;
            if (res.data.overtimeApplyVos.length <= 0) {
              setTimeout(() => {
                this.$router.push({
                  path: "/my-workbench",
                  query: { backStatus: 0 },
                });
              });
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 已处理 加班审批
    initAlreadyOverTime(flag) {
      let data = {
        offset: this.pagesize,
        rows: this.currentPage,
      };
      this.$http.wxqueryCompleteOvertimePage(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            if (flag) {
              //如果flag为true则表示分页
              this.approvalLists = this.approvalLists.concat(res.data.data); //concat数组串联进行合并
              if (res.data.data.length <= 0) {
                //如果数据加载完 那么禁用滚动时间 this.busy设置为true
                this.busy = true;
                this.endNothing = true;
              } else {
                this.busy = false;
              }
            } else {
              //第一次进入页面 完全不需要数据拼接的
              this.approvalLists = res.data.data;
              if (this.approvalLists.length >= this.pagesize) {
                this.busy = false;
              } else {
                this.busy = true;
              }
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 待处理 加班确认
    initPendingConfirmate(flag) {
      let data = {
        offset: this.pagesize,
        rows: this.currentPage,
      };
      this.$http.wxqueryOvertimePage(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            if (flag) {
              //如果flag为true则表示分页
              this.approvalLists = this.approvalLists.concat(res.data.data); //concat数组串联进行合并
              if (res.data.data.length <= 0) {
                //如果数据加载完 那么禁用滚动时间 this.busy设置为true
                this.busy = true;
                this.endNothing = true;
              } else {
                this.busy = false;
              }
            } else {
              //第一次进入页面 完全不需要数据拼接的
              this.approvalLists = res.data.data;
              if (this.approvalLists.length >= this.pagesize) {
                this.busy = false;
              } else {
                this.busy = true;
              }
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 已处理 加班确认
    initAlreadyConfirmate(flag) {
      let data = {
        offset: this.pagesize,
        rows: this.currentPage,
      };
      this.$http.wxqueryOvertimeValidList(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            if (flag) {
              //如果flag为true则表示分页
              this.approvalLists = this.approvalLists.concat(res.data.data); //concat数组串联进行合并
              if (res.data.data.length <= 0) {
                //如果数据加载完 那么禁用滚动时间 this.busy设置为true
                this.busy = true;
                this.endNothing = true;
              } else {
                this.busy = false;
              }
            } else {
              //第一次进入页面 完全不需要数据拼接的
              this.approvalLists = res.data.data;
              if (this.approvalLists.length >= this.pagesize) {
                this.busy = false;
              } else {
                this.busy = true;
              }
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 审批 详情
    toDetailsBtn(id) {
      let newStr = id.split(",");
      let processInstanceIds = JSON.stringify(newStr);
      // myStatus 1、我的请假申请; 2、我的加班申请; 3、我的补卡申请; 4、我的借调申请
      if (this.myStatus == 1) {
        this.$router.push({
          path: "/leave-details",
          query: {
            myStatus: this.myStatus,
            processinstanceid: id,
            processInstanceIds: processInstanceIds,
          },
        });
      } else if (this.myStatus == 2) {
        this.$router.push({
          path: "/check-confirmation",
          query: {
            myStatus: this.myStatus,
            processinstanceid: id,
            processInstanceIds: processInstanceIds,
          },
        });
      } else if (this.myStatus == 3) {
        this.$router.push({
          path: "/card-details",
          query: {
            myStatus: this.myStatus,
            processinstanceid: id,
            processInstanceIds: processInstanceIds,
          },
        });
      } else if (this.myStatus == 4) {
        this.$router.push({
          path: "/seconded-confirmed",
          query: {
            myStatus: this.myStatus,
            processinstanceid: id,
            processInstanceIds: processInstanceIds,
          },
        });
      }
      // 待处理 pendingStatus 1、请假审批; 2、加班审批; 3、加班确认; 4、补卡审批; 5、借调审批
      if (this.pendingStatus == 1) {
        this.$router.push({
          path: "/leave-details",
          query: {
            pendingStatus: this.pendingStatus,
            processinstanceid: id,
            processInstanceIds: processInstanceIds,
          },
        });
      } else if (this.pendingStatus == 2) {
        this.$router.push({
          path: "/approver-confirmation",
          query: {
            pendingStatus: this.pendingStatus,
            processInstanceIds: processInstanceIds,
            processinstanceid: id,
          },
        });
      } else if (this.pendingStatus == 3) {
        this.$router.push({
          path: "/overtime-confirmation",
          query: {
            pendingStatus: this.pendingStatus,
            processinstanceid: id,
            processInstanceIds: processInstanceIds,
          },
        });
      } else if (this.pendingStatus == 4) {
        this.$router.push({
          path: "/card-details",
          query: {
            pendingStatus: this.pendingStatus,
            processinstanceid: id,
            processInstanceIds: processInstanceIds,
          },
        });
      } else if (this.pendingStatus == 5) {
        this.$router.push({
          path: "/seconded-confirmation",
          query: {
            pendingStatus: this.pendingStatus,
            processinstanceid: id,
            processInstanceIds: processInstanceIds,
          },
        });
      }
      // 已处理 alreadyStatus 1、请假审批; 2、加班审批; 3、加班确认; 4、补卡审批  5、借调审批
      if (this.alreadyStatus == 1) {
        this.$router.push({
          path: "/leave-details",
          query: {
            alreadyStatus: this.alreadyStatus,
            processinstanceid: id,
            processInstanceIds: processInstanceIds,
          },
        });
      } else if (this.alreadyStatus == 2) {
        this.$router.push({
          path: "/approver-confirmation",
          query: {
            alreadyStatus: this.alreadyStatus,
            processinstanceid: id,
            processInstanceIds: processInstanceIds,
          },
        });
      } else if (this.alreadyStatus == 3) {
        this.$router.push({
          path: "/overtime-confirmation",
          query: {
            alreadyStatus: this.alreadyStatus,
            processinstanceid: id,
            processInstanceIds: processInstanceIds,
          },
        });
      } else if (this.alreadyStatus == 4) {
        this.$router.push({
          path: "/card-details",
          query: {
            alreadyStatus: this.alreadyStatus,
            processinstanceid: id,
            processInstanceIds: processInstanceIds,
          },
        });
      } else if (this.alreadyStatus == 5) {
        this.$router.push({
          path: "/seconded-confirmed",
          query: {
            alreadyStatus: this.alreadyStatus,
            processinstanceid: id,
            processInstanceIds: processInstanceIds,
          },
        });
      }
    },

    // 我的补卡申请
    initCardApply(flag) {
      let data = {
        offset: this.pagesize,
        rows: this.currentPage,
      };
      this.$http.wxQueryReplenishPage(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            if (flag) {
              //如果flag为true则表示分页
              this.approvalLists = this.approvalLists.concat(res.data.data); //concat数组串联进行合并
              if (res.data.data.length <= 0) {
                //如果数据加载完 那么禁用滚动时间 this.busy设置为true
                this.busy = true;
                this.endNothing = true;
              } else {
                this.busy = false;
              }
            } else {
              //第一次进入页面 完全不需要数据拼接的
              this.approvalLists = res.data.data;
              if (this.approvalLists.length >= this.pagesize) {
                this.busy = false;
              } else {
                this.busy = true;
              }
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 待处理 补卡审批
    initPendingCard() {
      this.$http.wxQueryPendingList(
        null,
        (res) => {
          if (res.code == "SUCCESS") {
            this.approvalLists = res.data.data;
            if (res.data.data.length <= 0) {
              setTimeout(() => {
                this.$router.push({
                  path: "/my-workbench",
                  query: { backStatus: 0 },
                });
              });
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 已处理 补卡审批
    initAlreadyCard(flag) {
      let data = {
        offset: this.pagesize,
        rows: this.currentPage,
      };
      this.$http.wxQueryProcessedList(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            if (flag) {
              //如果flag为true则表示分页
              this.approvalLists = this.approvalLists.concat(res.data.data); //concat数组串联进行合并
              if (res.data.data.length <= 0) {
                //如果数据加载完 那么禁用滚动时间 this.busy设置为true
                this.busy = true;
                this.endNothing = true;
              } else {
                this.busy = false;
              }
            } else {
              //第一次进入页面 完全不需要数据拼接的
              this.approvalLists = res.data.data;
              if (this.approvalLists.length >= this.pagesize) {
                this.busy = false;
              } else {
                this.busy = true;
              }
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 我的借调申请
    initMySeconded(flag) {
      let data = {
        offset: this.pagesize,
        rows: this.currentPage,
      };
      this.$http.wxqueryBorrowPage(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            if (flag) {
              //如果flag为true则表示分页
              this.approvalLists = this.approvalLists.concat(res.data.data); //concat数组串联进行合并
              if (res.data.data.length <= 0) {
                //如果数据加载完 那么禁用滚动时间 this.busy设置为true
                this.busy = true;
                this.endNothing = true;
              } else {
                this.busy = false;
              }
            } else {
              //第一次进入页面 完全不需要数据拼接的
              this.approvalLists = res.data.data;
              if (this.approvalLists.length >= this.pagesize) {
                this.busy = false;
              } else {
                this.busy = true;
              }
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 待处理 借调审批
    initPendingSeconded(flag) {
      let data = {
        offset: this.pagesize,
        rows: this.currentPage,
      };
      this.$http.wxBorrowQueryPendingList(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            if (flag) {
              //如果flag为true则表示分页
              this.approvalLists = this.approvalLists.concat(res.data.data); //concat数组串联进行合并
              if (res.data.data.length <= 0) {
                //如果数据加载完 那么禁用滚动时间 this.busy设置为true
                this.busy = true;
                this.endNothing = true;
              } else {
                this.busy = false;
              }
            } else {
              //第一次进入页面 完全不需要数据拼接的
              this.approvalLists = res.data.data;
              if (res.data.data.length <= 0) {
                setTimeout(() => {
                  this.$router.push({
                    path: "/my-workbench",
                    query: { backStatus: 0 },
                  });
                });
              }
              if (this.approvalLists.length >= this.pagesize) {
                this.busy = false;
              } else {
                this.busy = true;
              }
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 已处理 借调审批
    initAlreadySeconded(flag) {
      let data = {
        offset: this.pagesize,
        rows: this.currentPage,
      };
      this.$http.wxqueryProcessedList(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            if (flag) {
              //如果flag为true则表示分页
              this.approvalLists = this.approvalLists.concat(res.data.data); //concat数组串联进行合并
              if (res.data.data.length <= 0) {
                //如果数据加载完 那么禁用滚动时间 this.busy设置为true
                this.busy = true;
                this.endNothing = true;
              } else {
                this.busy = false;
              }
            } else {
              //第一次进入页面 完全不需要数据拼接的
              this.approvalLists = res.data.data;
              if (this.approvalLists.length >= this.pagesize) {
                this.busy = false;
              } else {
                this.busy = true;
              }
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 批量审批
    batchApprovalBtn() {
      if (this.myStatus) {
        this.$router.push({
          path: "/approval-batch",
          query: { myStatus: this.myStatus },
        });
      }
      if (this.pendingStatus) {
        this.$router.push({
          path: "/approval-batch",
          query: { pendingStatus: this.pendingStatus },
        });
      }
      if (this.alreadyStatus) {
        this.$router.push({
          path: "/approval-batch",
          query: { alreadyStatus: this.alreadyStatus },
        });
      }
    },

    // 执行筛选
    handleFilter() {
      this.filterLoading = true;
      this.currentPage = 1;
      this.endNothing = false;
      this.approvalLists = []; // 清空当前列表
      this.initAlreadyLeave();
    },

    // 重置筛选条件
    handleResetFilter() {
      this.filterApplicantEmpId = "";
      this.filterLeaveType = "";
      this.currentPage = 1;
      this.endNothing = false;
      this.approvalLists = []; // 清空当前列表
      this.initAlreadyLeave();
    },

    // 获取假别选项
    getLeaveTypeFilterOptions() {
      this.$http.wxqueryLeaveTypeAll(
        null,
        (res) => {
          if (res.code == "SUCCESS") {
            if (res.status == 0) {
              this.leaveTypeFilterOptions = res.data;
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message ? errRes.message : errRes.msg);
        }
      );

    },
  },
  mounted() {
    this.checkEmployee = localStorage.getItem("checkEmployee");
    this.myStatus = this.$route.query.myStatus; // myStatus 1、我的请假申请; 2、我的加班申请; 3、我的补卡申请; 4、我的借调申请
    this.pendingStatus = this.$route.query.pendingStatus; // 待处理 pendingStatus 1、请假审批; 2、加班审批; 3、加班确认; 4、补卡审批; 5、借调审批
    this.alreadyStatus = this.$route.query.alreadyStatus; // 已处理 alreadyStatus 1、请假审批; 2、加班审批; 3、加班确认; 4、补卡审批; 5、借调审批
    if (this.myStatus == 1) {
      this.initMyLeave();
      this.$emit("refreshbizlines", "我的请假申请");
    } else if (this.myStatus == 2) {
      this.initMyOverTime();
      this.$emit("refreshbizlines", "我的加班申请");
    } else if (this.myStatus == 3) {
      this.initCardApply();
      this.$emit("refreshbizlines", "我的补卡申请");
    } else if (this.myStatus == 4) {
      this.initMySeconded();
      this.$emit("refreshbizlines", "我的借调申请");
    }
    if (this.pendingStatus == 1) {
      this.initPendingLeave();
      this.queryLeaveTypeCount();
      this.$emit("refreshbizlines", "请假审批");
    } else if (this.pendingStatus == 2) {
      this.initPendingOverTime();
      this.$emit("refreshbizlines", "加班审批");
    } else if (this.pendingStatus == 3) {
      this.initPendingConfirmate();
      this.$emit("refreshbizlines", "加班确认");
    } else if (this.pendingStatus == 4) {
      this.initPendingCard();
      this.$emit("refreshbizlines", "补卡审批");
    } else if (this.pendingStatus == 5) {
      this.initPendingSeconded();
      this.$emit("refreshbizlines", "借调审批");
    }
    if (this.alreadyStatus == 1) {
      this.initAlreadyLeave();
      // 初始化假别筛选选项
      this.getLeaveTypeFilterOptions();
      this.$emit("refreshbizlines", "请假审批");
    } else if (this.alreadyStatus == 2) {
      this.initAlreadyOverTime();
      this.$emit("refreshbizlines", "加班审批");
    } else if (this.alreadyStatus == 3) {
      this.initAlreadyConfirmate();
      this.$emit("refreshbizlines", "加班确认");
    } else if (this.alreadyStatus == 4) {
      this.initAlreadyCard();
      this.$emit("refreshbizlines", "补卡审批");
    } else if (this.alreadyStatus == 5) {
      this.initAlreadySeconded();
      this.$emit("refreshbizlines", "借调审批");
    }

    if (
      this.myStatus == undefined &&
      this.pendingStatus == undefined &&
      this.alreadyStatus == undefined
    ) {
      let newStr = this.$route.query.processinstanceid.split(",");
      this.processInstanceIds = newStr;
      this.initPendingOverTime();
      this.$emit("refreshbizlines", "加班审批");
      this.pendingStatus = 2;
    }
  },
};
</script>

<style scoped>
.filter-container {
  padding: 10px;
  background-color: #f8f9fa;
  margin-bottom: 8px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.filter-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  margin-bottom: 8px;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-input {
  flex: 1;
  max-width: 200px;
}

.filter-select {
  flex: 1;
  max-width: 200px;
}

.filter-button {
  min-width: 70px;
  flex-shrink: 0;
}
</style>
