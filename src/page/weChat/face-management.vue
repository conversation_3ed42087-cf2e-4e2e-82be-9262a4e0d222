<template>
    <div class="faceManagementBox">
        <div class="upImgBox">
            <img :src="upImg">
        </div>
        <div class="noticeBox">
            <!-- 上传照片 提示 start -->
            <div class="upSuccessNotice" v-show="upSuccess">
                <div>您已成功提交人脸照片</div>
                <div>请前往考勤机做人脸照片验证</div>
            </div>
            <!-- 上传照片 提示 end -->
            
            <!-- 验证成功 提示 start -->
            <div class="upSuccessNotice" v-show="verifySuccess">
                <div>人脸照片已验证成功</div>
                <div> 3个月内无法更换照片，</div>
                <div>如需更换请联系系统管理员</div>
            </div>
            <!-- 验证成功 提示 end -->
            
            <!-- 验证失败 提示 start -->
            <div class="upSuccessNotice" v-show="verifyFail">
                <div>您已成功提交人脸照片</div>
                <div> 请前往考勤机验证，成功打卡后点击下方验证按钮</div>
            </div>
            <!-- 验证失败 提示 end -->
        </div>
        <div class="btnBox">
            <!-- 上传照片 start -->
            <div class="faceBtn" v-show="upBtn">
                <el-button type="primary" @click="updateImg">重新拍摄</el-button>
                <el-button type="primary" @click="submitBtn">提交</el-button>
            </div>
            <!-- 上传照片 end -->

            <!-- 更新照片 start -->
            <div class="updateBtn" v-show="updateShowBtn">
                <el-button type="primary" @click="updateBtn">更新照片</el-button>
            </div>
            <!-- 更新照片 end -->

            <!-- 验证 start -->
            <div class="faceBtn" v-show="updateVerifyBtn">
                <el-button type="primary" @click="reentryBtn">重新录入</el-button>
                <el-button type="primary" @click="verificationBtn">验证</el-button>
            </div>
            <!-- 验证 end -->
        </div>

        <!-- 验证提示 start -->
        <el-dialog
            title="提示"
            :visible.sync="dialogNotice"
            :close-on-click-modal = false
            :show-close="false"
            class="dialogNotice dialogNoticeSmall">
            <div class="noticeShow">
                <div>未查询到成功打卡记录,请确认已在考勤机上成功打卡并稍后再试</div>
            </div>
            <span slot="footer" class="dialog-footer dialog-footerCenter">
                <el-button type="text" class="verifyNoticeBtn" @click="verifyNoticeBtn">确定</el-button>
            </span>
        </el-dialog>
        <!-- 验证提示 end -->
    </div>
</template>
<script>
import wx from 'weixin-js-sdk';
export default {
    data(){
        return{
            upImg:'../static/images/icon_user.png',
            upSuccess:false, //上传成功提示
            upBtn:false, //上传操作
            verifySuccess:false, //验证成功提示
            verifyBtn:false, // 验证成功操作
            updateShowBtn:false, // 更新操作
            verifyFail:false,   // 验证失败提示
            dialogNotice:false, // 验证失败dialog
            updateVerifyBtn:false, // 重新验证操作
            empId:'',
            imgUrl:'', 
            localIds:'',
            valid:'' // 验证结果
        }
    },
    methods: {
        initData(){
            // valid = 5  验证结果
            if(this.valid == 5){
                this.getInformation()
                this.getVerificationResults()
            }else{
                this.upBtn = true;
            }
        },

        // 获取信息
        getInformation(){
            let data = {
                id: localStorage.getItem('openId'),	
                // id: 'NB004400',	
            }
            this.$http.wxqueryPhoneEmployeeDetail(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    if(res.status == 0){
                        this.upImg = res.data.data.faceUrl;
                    }
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                if(errRes.code == 500){
                    this.$message.error(errRes.msg);
                }
                this.$message.error(errRes.message);
            })
        },

        // 验证结果
        getVerificationResults(){
            this.$http.wxqueryAttendanceValid(null,(res)=>{
                if(res.code == 'SUCCESS'){
                    if(res.status == 0){
                        this.verifySuccess = true;
                    }
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                if(errRes.code == 500){
                    this.$message.error(errRes.msg);
                }
                this.$message.error(errRes.message);
                this.dialogNotice = true;
            })
        },

        // 上传照片 提交
        submitBtn() {
            var that = this
            if (that.upImg == '') {
                that.$message({
                    type: 'warning',
                    message: '请重新拍摄照片或上传照片'
                });  
            } else {
                let data = {
                    empId:that.empId,
                    faceUrl:that.upImg,
                }
                that.$http.wxupdateEmployeeFaceImage(data,(res)=>{
                    if(res.code == 'SUCCESS'){
                        if(res.status == 0){
                            that.$message.success(res.message);
                            that.upSuccess = true;
                            that.upBtn = false;
                            setTimeout(() =>{
                                WeixinJSBridge.call("closeWindow");
                            },1500);
                        }
                    }else{
                        that.$message.error(res.message);
                    }
                },(errRes)=>{
                    
                    if(errRes.code == 500){
                        that.$message.error(errRes.msg);
                    }
                    that.$message.error(errRes.message); 
                    setTimeout(() =>{
                        WeixinJSBridge.call("closeWindow");
                    },1500); 
                })
            }
        },
        
        // 点击重新拍摄
        updateImg(){
            var that = this;
            wx.chooseImage({
                count: 1,
                sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
                sourceType: ['camera'], // 可以指定来源是相册还是相机，默认二者都有 album 从相册选图，camera 使用相机，默认二者都有
                success: function (res) {
                    that.localIds  = res.localIds[0]// 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片
                    that.uploadImg();
                }
            });
        },

        // 重新拍摄上传
        uploadImg(){
            var that = this;
            wx.uploadImage({
                localId: that.localIds, // 需要上传的图片的本地ID，由chooseImage接口获得
                isShowProgressTips: 1, // 默认为1，显示进度提示
                success: function (res) {
                    var serverId = res.serverId; // 返回图片的服务器端ID
                    let data = {
                        mediaId: serverId
                    }
                    that.$http.wxuploadMedia(data, (res) => {
                        if (res.code == 'SUCCESS') {
                            that.upImg = res.data.path
                            that.$message.success(res.message);
                        } else {
                            that.$message.error(res.message);
                        }
                        
                    }, (errRes) => {
                        that.$message.error(errRes.message)
                    })
                    .then((data) => {

                    })
                    .catch((error) => {
                    })
                }
            });
        },

        // 更新照片
        updateBtn(){
            this.submitBtn()
        },

        // 验证提示确定
        verifyNoticeBtn(){
            setTimeout(() =>{
                WeixinJSBridge.call("closeWindow");
            },2000);
        },

        reentryBtn(){

        },

        // 验证结果失败 重新验证
        verificationBtn(){
           
        },
    },
    mounted(){
        this.$emit('refreshbizlines','人脸管理');  
        this.empId = this.$route.query.empId;
        this.upImg = this.$route.query.imgUrl;
        this.valid = this.$route.query.valid;
        this.initData();
    }
}
</script>