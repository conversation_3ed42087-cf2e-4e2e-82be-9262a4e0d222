<template>
  <div class="overtimeConfirmationBox backcolorBox bottomBox">
    <div class="overtimeConfirmationLists">
      <div class="list">
        <div>部门</div>
        <div>{{ department }}</div>
      </div>
      <div class="list">
        <div>加班事由</div>
        <div>{{ reason }}</div>
      </div>
      <div class="list">
        <div>加班类型</div>
        <div>{{ overtimeType }}</div>
      </div>

      <!-- 加班员工 start -->
      <div class="addLists">
        <div class="lstTop">
          <div class="listTopLeft">
            <div class="timeShow">{{ startTime }}</div>
            -
            <div class="timeShow">{{ endTime }}</div>
          </div>
        </div>
        <div class="overTimeEmployees">
          <label for="">加班员工</label>
          <div class="edit">
            <!-- {{employees}} -->
            <span
              v-for="(em, emindex) in employeesLists"
              :key="emindex"
              :class="[
                em.status == '2' ? 'greenMark' : '',
                em.status == '3' ? 'redMark' : '',
                em.status == '1' ? 'grayMark' : '',
              ]"
            >
              {{ em.overtimeUser + em.workType }}
            </span>
          </div>
        </div>
      </div>
      <!-- 加班员工 end -->

      <!-- 操作记录 start -->
      <div class="operateRecord" v-if="operateRecord.length > 0">
        <div>操作记录</div>
        <div class="recordList">
          <div
            class="recordDes"
            v-for="(re, reindex) in operateRecord"
            :key="reindex"
          >
            <div>{{ re.approveUserName + re.approveStatusName }}审批</div>
            <div>{{ re.approveTime }}</div>
          </div>
        </div>
      </div>
      <!-- 操作记录 end -->
    </div>

    <!-- 员工确认状态 start -->
    <div class="overTimeEmployeeColor">
      <div class="greenMark"><span class="greenBgMark"></span>已确认</div>
      <div class="redMark"><span class="redBgMark"></span>拒绝</div>
      <div class="grayMark"><span class="grayBgMark"></span>未答复</div>
    </div>
    <!-- 员工确认状态 end -->

    <!-- 审批人 操作 start -->
    <div class="bottomBtnBox" v-show="statusShow == 1 || statusShow == 2 || statusShow == 3">
      <el-button @click="rejectBtn">驳回</el-button>
      <el-button type="primary" @click="adoptBtn">通过</el-button>
      <!-- <el-button type="primary" @click="confirmBtn" v-show="statusShow >= 3"
        >确认</el-button
      > -->
    </div>
    <!-- 审批人 操作 end -->

    <!-- 驳回原因 start -->
    <el-dialog
      title="驳回原因"
      :visible.sync="dialogRemark"
      :close-on-click-modal="false"
      :show-close="false"
      class="dialogRemark"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        class="demo-ruleForm"
      >
        <el-form-item prop="remark" class="remark">
          <el-input
            type="textarea"
            :rows="3"
            v-model="ruleForm.remark"
            auto-complete="off"
            placeholder="输入内容"
            v-reset-page
          >
          </el-input>
        </el-form-item>

        <el-form-item class="footBtn">
          <el-button type="text" @click="cancelBtn('ruleForm')"
            >取 消</el-button
          >
          <el-button type="text" @click="submitForm('ruleForm')"
            >确认驳回</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 驳回原因 end -->
  </div>
</template>
<script>
export default {
  data() {
    var checkRemark = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("输入内容!"));
      } else {
        callback();
      }
    };
    return {
      isLeader: true,
      department: "", // 部门
      reason: "", // 原因
      multiple: "", // 倍
      startTime: "", // 开始时间
      endTime: "", // 结束时间
      overtimeType: "", // 加班类型
      employees: "", // 加班人员
      processinstanceid: "",
      processInstanceIds: [], // 审批人
      statusShow: "",
      dialogRemark: false,
      operateRecord: [],
      employeesLists: [],
      ruleForm: {
        remark: "",
      },
      rules: {
        remark: [{ validator: checkRemark, trigger: "blur" }],
      },
    };
  },
  methods: {
    // 审批人数据渲染
    initApproverData() {
      let data = {
        processinstanceid: this.processInstanceIds,
        status: 1,
      };
      this.$http.wxqueryOvertimeDetail(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            if (res.status == 0) {
              var dataShow = res.data.apply;
              var approveArray = res.data.approveArray;
              this.department = dataShow.department;
              this.reason = dataShow.reason;
              this.overtimeType = dataShow.overtimeTypeName;
              this.multiple = dataShow.multiple;
              this.startTime = dataShow.startTime;
              this.endTime = dataShow.endTime;
              this.operateRecord = approveArray;
              this.employees = dataShow.operator;
              this.employeesLists = res.data.applyList;
              this.statusShow = dataShow.status; //大状态 1：待确认 2：待提交 3：待审批 4：审批通过 5：撤回 6：驳回 小状态 1：未答复 2：已确认 3：已拒绝 4：领班确认 5：领班取消 6：经理通过 7：经理驳回 8：超时自动取消
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          if (errRes.code == 500) {
            this.$message.error(errRes.msg);
          }
          this.$message.error(errRes.message);
        }
      );
    },

    // 审批驳回
    rejectBtn() {
      this.dialogRemark = true;
    },

    // 拒绝 驳回 取消
    cancelBtn(formName) {
      this.dialogRemark = false;
      this.$refs[formName].resetFields();
    },

    // 确认 拒绝  驳回
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = {
            processInstanceIds: this.processInstanceIds,
            remark: this.ruleForm.remark,
            status: 2, // 1：同意 2：拒绝,默认同意
          };
          this.$http.wxapproveOvertimeApply(
            data,
            (res) => {
              if (res.code == "SUCCESS") {
                if (res.status == 0) {
                  this.$message.success(res.message);
                  setTimeout(() => {
                    this.$router.push({
                      path: "/overtime-approval",
                      query: { pendingStatus: 2 },
                    });
                  }, 1000);
                  this.dialogRemark = false;
                  this.$refs[formName].resetFields();
                }
              } else {
                this.$message.error(res.message);
              }
            },
            (errRes) => {
              if (errRes.code == 500) {
                this.$message.error(errRes.msg);
              }
              this.$message.error(errRes.message);
            }
          );
        } else {
          return false;
        }
      });
    },

    //审批通过
    adoptBtn() {
      let data = {
        processInstanceIds: this.processInstanceIds,
        status: 1, // 是否同意 1：同意 2：拒绝,默认同意
      };
      this.$http.wxapproveOvertimeApply(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            this.$message.success(res.message);
            setTimeout(() => {
              this.$router.push({
                path: "/overtime-approval",
                query: { pendingStatus: 2 },
              });
            }, 1000);
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    confirmBtn() {
      this.$router.push({
        path: "/overtime-approval",
        query: {
          myStatus: 2,
        },
      });
    },
  },
  mounted() {
    this.myStatus = this.$route.query.myStatus; // myStatus 1、我的请假申请; 2、我的加班申请; 3、我的补卡申请; 4、我的借调申请
    this.pendingStatus = this.$route.query.pendingStatus; // 待处理 pendingStatus 1、请假审批; 2、加班审批; 3、加班确认; 4、补卡审批; 5、借调审批
    this.alreadyStatus = this.$route.query.alreadyStatus; // 已处理 alreadyStatus 1、请假审批; 2、加班审批; 3、加班确认; 4、补卡审批; 5、借调审批
    this.$emit("refreshbizlines", "加班审批");
    this.processinstanceid = this.$route.query.processinstanceid;
    this.processInstanceIds = JSON.parse(this.$route.query.processInstanceIds);
    this.initApproverData();
  },
};
</script>
