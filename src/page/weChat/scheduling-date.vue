 <template>
       <div class="schedulingBox">
           <div class="navScheduling">
               <div class="schedulingNav" @click="personnelBtn">按人员排班</div>
               <div class="schedulingNavActive" @click="dateBtn">按日期排班</div>
           </div>
           <div class="calendarBox calendarDefinedBox">
                <!-- 里面写eleCalendar组件-->
                <ele-calendar  
                    :data="datedef"
                    @date-change="dateChange"
                    @pick="pickDate"
                    value-format="yyyy-MM-dd">
                </ele-calendar>
            </div>
       </div>
</template>
<script>
import eleCalendar from 'ele-calendar'
import 'ele-calendar/dist/vue-calendar.css' 
export default {
    data(){
        return{
           datedef:[],
        }
    },
    components: {
        eleCalendar
    },
    methods: {
        // 按照人员排班 nav
        personnelBtn(){
            this.$router.push({ path:'/scheduling'})
        },

        // 按照日期排班 nav
        dateBtn(){
            this.$router.push({ path:'/scheduling-date'})
        },
        
        // 查询 年 月
        dateChange(date){

        },

        // 具体一天 
        pickDate(date){
            this.$router.push({ path:'/scheduling-personnel',query:{date}})
        },
    },
    mounted(){
        this.$emit('refreshbizlines','排班'); 
    }
}
</script>