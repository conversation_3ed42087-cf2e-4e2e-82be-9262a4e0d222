<template>
  <div class="overtimeApplicationBox backcolorBox">
    <div class="overtimeApplicationDes">
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        class="demo-ruleForm"
      >
        <el-form-item class="formItem defFormItem" label="厂区">
          <div class="department">{{ ruleForm.plant }}</div>
        </el-form-item>
        <el-form-item class="formItem defFormItem" label="部门">
          <div class="department">{{ ruleForm.department }}</div>
        </el-form-item>
        <el-form-item class="formItem defFormItem" label="车间">
          <div class="department">{{ ruleForm.workshop }}</div>
        </el-form-item>
        <el-form-item
          prop="reason"
          class="formItem lineFormItem"
          label="加班事由"
        >
          <el-input
            type="textarea"
            v-model="ruleForm.reason"
            placeholder="请输入加班事由"
            v-reset-page
          ></el-input>
        </el-form-item>
        <el-form-item label="加班类型" class="formItem lineFormItem">
          <el-radio-group v-model="ruleForm.overtimeType">
            <el-radio
              v-for="item in overtimeTypeOptions"
              :key="item.value"
              :label="item.overtimeTypeId"
            >
              {{
                item.overtimeTypeName
              }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          prop="relinquishTime"
          class="formItem lineFormItem"
          label="自动撤回时间（min）"
        >
          <el-input
            type="text"
            v-model="ruleForm.relinquishTime"
            placeholder="超过此时间未全部确认，即自动撤回"
            @blur="blurText($event)"
            min="0"
            v-reset-page
          ></el-input>
        </el-form-item>
        <!-- 添加的时间段 start -->
        <el-form-item class="formItem addFormItem">
          <div
            class="addLists"
            v-for="(item, index) in ruleForm.addOverTime"
            :key="index"
            :class="getAddOverTime(item, index)"
          >
            <div class="lstTop">
              <div class="iconShow">
                <!-- <el-button @click="editTime(item,index)"><i class="el-icon-edit"></i></el-button> -->
                <el-button @click="deleteTime(item, index)"
                  ><i class="el-icon-delete"></i
                ></el-button>
              </div>

              <div class="overTimeEmployees">
                <label for="">加班日期</label>
                <div class="edit">
                  <el-date-picker
                    v-model="item.overtimeDate"
                    type="date"
                    placeholder="请选择加班日期"
                    value-format="yyyy-MM-dd"
                    format="yyyy-MM-dd"
                    :clearable="false"
                    @change="selectDate(index,item.overtimeDate)"
                  >
                  </el-date-picker>
                </div>
              </div>

              <div class="overTimeEmployees">
                <label for="">班次</label>
                <div class="edit">
                  <el-select
                    v-model="item.workType"
                    filterable
                    placeholder="请选择班次"
                    @change="selectShift(index, item.workType)"
                  >
                    <el-option
                      v-for="item in shiftOptions"
                      :key="item.name"
                      :label="item.name"
                      :value="item.name"
                    >
                    </el-option>
                  </el-select>
                  <i class="el-icon-arrow-right"></i>
                </div>
              </div>
            </div>
            <div class="listTopLeft">
              <el-input
                type="text"
                v-model="item.startTime"
                placeholder="请选择开始时间"
                @focus="showStartCalendarDialog(index, item.startTime)"
                readonly="readonly"
              >
              </el-input>
              -
              <el-input
                type="text"
                v-model="item.endTime"
                placeholder="请选择结束时间"
                @focus="showEndCalendarDialog(index, item.endTime)"
                readonly="readonly"
              >
              </el-input>
            </div>

            <div class="overTimeEmployees">
              <label for="">加班时长</label>
              <div class="edit">{{ item.timeCount }}</div>
            </div>
            <div class="overTimeEmployees" @click="selectEmployees(index)">
              <label for="">加班员工</label>
              <div class="edit">
                <el-input
                  type="text"
                  v-model="item.employees"
                  placeholder="请选择"
                  v-reset-page
                ></el-input>
                <i class="el-icon-arrow-right"></i>
              </div>
            </div>
          </div>
        </el-form-item>
        <!-- 添加的时间段 end -->

        <!-- 时间段选择 start -->
        <div class="timeFormItem">
          <vue-hash-calendar
            :defaultDatetime="defaultDatetime"
            ref="picker"
            model="dialog"
            :scroll-change-date="true"
            :visible.sync="isShowAddCalendar"
            :is-show-week-view="false"
            format="YY-MM-DD hh:mm:ss"
            week-start="sunday"
            picker-type="datetime"
            :show-today-button="true"
            :disabled-week-view="false"
            @confirm="dateAddConfirm"
            @click="dateAddClick"
            @change="dateAddChange"
            :minuteStep="30"
          >
          </vue-hash-calendar>
        </div>
        <!-- 时间段选择 end -->
        <el-form-item class="formItem bottomBtnBox">
          <el-button @click="setDate()">添加时间段</el-button>
          <el-button
            type="primary"
            @click="submitForm('ruleForm')"
            :disabled="disabled"
            >发送给员工</el-button
          >
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
import dayjs from "dayjs";
export default {
  data() {
    return {
      employees: "", // 加班员工
      employeesIdArr: "",
      date: "",
      disabled: false,
      ruleForm: {
        department: "", // 部门
        reason: "", // 加班事由
        overtimeType: "", // 加班类型
        relinquishTime: "", // 撤回时间
        addOverTime: [],
        plant: "",
        workshop: "",
      },
      addOverTime: [],
      employeesOptions: [],
      overtimeTypeOptions: [],
      employeesCheckedArr: [],
      overtimeHoursList:[],
      editIndex: "",
      rules: {},
      timeStatus: "", // 0 添加时间 1 开始时间 2 结束时间
      isShowAddCalendar: false,
      startIndex: "",
      endIndex: "",
      shiftOptions: [],
      indexShow: "",
      defaultDatetime: undefined,
    };
  },
  methods: {
    // 加班类别
    queryOvertimeType() {
      this.$http.wxqueryOvertimeType(
        null,
        (res) => {
          if (res.code == "SUCCESS") {
            this.overtimeTypeOptions = res.data;
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 数字值验证
    blurText(e) {
      let boolean = new RegExp("^[1-9][0-9]*$").test(e.target.value);
      if (!boolean) {
        this.$message.warning("请输入数字值");
        e.target.value = "";
      }
    },

    // 添加时间段
    setDate() {
      this.timeStatus = 0;
      // this.isShowAddCalendar = true;
      this.ruleForm.addOverTime.push({
        startTime: "",
        endTime: "",
        workType: "",
        timeCount: "",
        overtimeDate: ""
      });
      this.addOverTime.push({
        startTime: "",
        endTime: "",
        workType: "",
        timeCount: "",
        overtimeDate: ""
      });
    },
    getAddOverTime(item, idex) {
      var startTimeDes = new Date(item.startTime).getTime();
      var endTimeDes = new Date(item.endTime).getTime();
      if (endTimeDes <= startTimeDes) {
        return "redBorder";
      }
    },
    // 显示日历 开始时间
    showStartCalendarDialog(index, defaultDatetime) {
      this.indexShow = index;
      this.timeStatus = 1;
      this.startIndex = index;
      this.isShowAddCalendar = true;
      if (defaultDatetime) {
        var time = defaultDatetime;
        time = time.replace(/-/g, ":").replace(" ", ":");
        time = time.split(":");
        this.defaultDatetime = new Date(
          time[0],
          time[1] - 1,
          time[2],
          time[3],
          time[4],
          time[5]
        );
      }
    },

    // 显示日历 结束时间
    showEndCalendarDialog(index, defaultDatetime) {
      this.indexShow = index;
      this.timeStatus = 2;
      this.endIndex = index;
      this.isShowAddCalendar = true;
      if (defaultDatetime) {
        var time = defaultDatetime;
        time = time.replace(/-/g, ":").replace(" ", ":");
        time = time.split(":");
        this.defaultDatetime = new Date(
          time[0],
          time[1] - 1,
          time[2],
          time[3],
          time[4],
          time[5]
        );
      }
    },

    // 日期改变触发
    dateAddChange(date) {},

    // 点击确认按钮触发 -- 添加
    dateAddConfirm(date) {
      var that = this;
      if (that.timeStatus == 0) {
        that.ruleForm.addOverTime.push({
          startTime: date,
          endTime: "",
          workType: "",
          timeCount: "",
        });
        that.addOverTime.push({
          startTime: date,
          endTime: "",
          workType: "",
          timeCount: "",
        });
      } else if (that.timeStatus == 1) {
        // 开始
        var startTimeOrArr = that.ruleForm.addOverTime[that.startIndex]; // 原始
        var startTimeSubArr = that.addOverTime[that.startIndex]; // 提交
        startTimeOrArr.startTime = date;
        that.$set(that.ruleForm.addOverTime, that.startIndex, startTimeOrArr);
        startTimeSubArr.startTime = date;
        that.$set(that.addOverTime, that.startIndex, startTimeSubArr);
        
        // 如果加班日期为空，则自动设置为开始时间的日期
        if (!startTimeOrArr.overtimeDate) {
          var startDate = dayjs(date).format('YYYY-MM-DD');
          startTimeOrArr.overtimeDate = startDate;
          that.$set(that.ruleForm.addOverTime, that.startIndex, startTimeOrArr);
          startTimeSubArr.overtimeDate = startDate;
          that.$set(that.addOverTime, that.startIndex, startTimeSubArr);
        }
        
        if (endTimeShow != "") {
          var startTimeShow = that.ruleForm.addOverTime[
            that.startIndex
          ].startTime.slice(0, 16);
          var endTimeShow = that.ruleForm.addOverTime[
            that.startIndex
          ].endTime.slice(0, 16);
          var startTimeDes = new Date(startTimeShow).getTime();
          var endTimeDes = new Date(endTimeShow).getTime();
          // if (startTimeDes >= endTimeDes) {
          //   startTimeOrArr.startTime = "";
          //   that.$set(
          //     that.ruleForm.addOverTime,
          //     that.startIndex,
          //     startTimeOrArr
          //   );
          //   startTimeSubArr.startTime = "";
          //   that.$set(that.addOverTime, that.startIndex, startTimeSubArr);
          //   startTimeOrArr.endTime = "";
          //   that.$set(
          //     that.ruleForm.addOverTime,
          //     that.startIndex,
          //     startTimeOrArr
          //   );
          //   startTimeSubArr.endTime = "";
          //   that.$set(that.addOverTime, that.startIndex, startTimeSubArr);
          //   this.$message.warning("开始时间不得大于结束时间");
          // }
          var workType = this.ruleForm.addOverTime[that.startIndex].workType;
          var startTime = this.ruleForm.addOverTime[that.startIndex].startTime;
          var endTime = this.ruleForm.addOverTime[that.startIndex].endTime;
          if (workType != "" && startTime != "" && endTime != "") {
            this.getOvertimeTime(that.startIndex);
          }
        }
      } else if (that.timeStatus == 2) {
        // 结束
        var endTimeOrArr = that.ruleForm.addOverTime[that.endIndex]; // 原始
        var endTimeSubArr = that.addOverTime[that.endIndex]; // 提交
        endTimeOrArr.endTime = date;
        that.$set(that.ruleForm.addOverTime, that.endIndex, endTimeOrArr);
        endTimeSubArr.endTime = date;
        that.$set(that.addOverTime, that.endIndex, endTimeSubArr);
        if (endTimeShow != "") {
          var startTimeShow = endTimeOrArr.startTime.slice(0, 16);
          var endTimeShow = endTimeOrArr.endTime.slice(0, 16);
          var startTimeDes = new Date(startTimeShow).getTime();
          var endTimeDes = new Date(endTimeShow).getTime();
          // if (endTimeDes <= startTimeDes) {
          //   endTimeOrArr.endTime = "";
          //   that.$set(that.ruleForm.addOverTime, that.endIndex, endTimeOrArr);
          //   endTimeSubArr.endTime = "";
          //   that.$set(that.addOverTime, that.endIndex, endTimeSubArr);
          //   endTimeOrArr.startTime = "";
          //   that.$set(that.ruleForm.addOverTime, that.endIndex, endTimeOrArr);
          //   endTimeSubArr.startTime = "";
          //   that.$set(that.addOverTime, that.endIndex, endTimeSubArr);
          //   this.$message.warning("结束时间不得小于开始时间");
          // }
          var workType = this.ruleForm.addOverTime[that.endIndex].workType;
          var startTime = this.ruleForm.addOverTime[that.endIndex].startTime;
          var endTime = this.ruleForm.addOverTime[that.endIndex].endTime;
          if (workType != "" && startTime != "" && endTime != "") {
            this.getOvertimeTime(that.endIndex);
          }
        }
      }
    },

    // 点击日期时按钮触发 -- 添加
    dateAddClick(date) {},

    // 禁用的日期
    disabledDate(date) {
      // 开始时间小于结束时间
      if (this.timeStatus == 1) {
        let endTime;
        if (this.ruleForm.addOverTime.length > 0) {
          if (this.startIndex == "") {
            this.startIndex = 0;
            let endTimeIndex = this.ruleForm.addOverTime[this.startIndex]
              .endTime;
            if (endTimeIndex != undefined) {
              endTime = endTimeIndex.slice(0, 16);
              let timestamp = date.getTime();
              if (timestamp > new Date(endTime).getTime()) {
                return true;
              }
              return false;
            }
          } else {
            let endTimeIndex = this.ruleForm.addOverTime[this.startIndex]
              .endTime;
            if (endTimeIndex != undefined) {
              endTime = endTimeIndex.slice(0, 16);
              let timestamp = date.getTime();
              if (timestamp > new Date(endTime).getTime()) {
                return true;
              }
              return false;
            }
          }
        }
      } else if (this.timeStatus == 2) {
        // 结束时间大于开始时间
        let startTime;
        if (this.ruleForm.addOverTime.length > 0) {
          if (this.endIndex == "") {
            this.endIndex = 0;
            let startTimeIndex = this.ruleForm.addOverTime[this.endIndex]
              .startTime;
            if (startTimeIndex != undefined) {
              startTime = startTimeIndex.slice(0, 16);
              let timestamp = date.getTime();
              if (timestamp < new Date(startTime).getTime() - 86400000) {
                return true;
              }
              return false;
            }
          } else {
            let startTimeIndex = this.ruleForm.addOverTime[this.endIndex]
              .startTime;
            if (startTimeIndex != undefined) {
              startTime = startTimeIndex.slice(0, 16);
              let timestamp = date.getTime();
              if (timestamp < new Date(startTime).getTime() - 86400000) {
                return true;
              }
              return false;
            }
          }
        }
      }
    },

    // 查询加班时长
    getOvertimeTime(index) {
      let data = {
        startTime: this.ruleForm.addOverTime[index].startTime,
        endTime: this.ruleForm.addOverTime[index].endTime,
        workType: this.ruleForm.addOverTime[index].workType,
      };
      this.$http.calculateOvertimeTime(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            this.ruleForm.addOverTime[index].timeCount = res.data;
            this.addOverTime[index].timeCount = res.data;
          } else {
            this.$message.error(res.message);
            this.ruleForm.addOverTime[index].timeCount = "";
            this.addOverTime[index].timeCount = "";
            // this.ruleForm.addOverTime[index].startTime = "";
            // this.addOverTime[index].startTime = "";
            // this.ruleForm.addOverTime[index].endTime = "";
            // this.addOverTime[index].endTime = "";
          }
        },
        (errRes) => {
          this.ruleForm.addOverTime[index].timeCount = "";
          this.addOverTime[index].timeCount = "";
          this.$message.error(errRes.message ? errRes.message : errRes.msg);

          // this.ruleForm.addOverTime[index].startTime = "";
          // this.addOverTime[index].startTime = "";
          // this.ruleForm.addOverTime[index].endTime = "";
          // this.addOverTime[index].endTime = "";
        }
      );
    },

    // 查询所有班次信息
    queryPhoneWorkTypeAll() {
      this.$http.wxqueryPhoneWorkTypeAll(
        null,
        (res) => {
          if (res.code == "SUCCESS") {
            this.shiftOptions = res.data.data;
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 选择加班日期
    selectDate(index,val) {
      var that = this;
      var item = that.ruleForm.addOverTime[index]; // 原始
      item.overtimeDate = val;
      that.$set(that.addOverTime, index, item);
    },


    // 选择班次
    selectShift(index, val) {
      var startTime = this.ruleForm.addOverTime[index].startTime;
      var endTime = this.ruleForm.addOverTime[index].endTime;
      if (
        startTime != "" &&
        startTime != undefined &&
        endTime != "" &&
        endTime != undefined
      ) {
        this.getOvertimeTime(index);
        this.editIndex = index;
        this.ruleForm.addOverTime[this.editIndex].workType = val;
        this.$set(
          this.ruleForm.addOverTime,
          this.editIndex,
          this.ruleForm.addOverTime[this.editIndex]
        );
        this.addOverTime[this.editIndex].workType = val;
        this.$set(
          this.addOverTime,
          this.editIndex,
          this.addOverTime[this.editIndex]
        );
      } else {
        this.$message.error("完善时间");
        this.ruleForm.addOverTime[this.editIndex].workType = "";
      }
    },

    // 选择员工
    selectEmployees(index) {
      this.editIndex = index;
      this.$router.push({
        path: "/overtime-employees",
        query: {
          addOverTime: this.ruleForm.addOverTime,
          startTime: this.startTime,
          endTime: this.endTime,
          reason: this.ruleForm.reason,
          overtimeType: this.ruleForm.overtimeType,
          relinquishTime: this.ruleForm.relinquishTime,
          addOverTimeAll: this.addOverTime,
          editIndex: this.editIndex,
          department: this.ruleForm.department,
          plant: this.ruleForm.plant,
          workshop: this.ruleForm.workshop,
        },
      });
    },

    // 删除 添加时间段
    deleteTime(item, index) {
      if (index !== -1) {
        this.ruleForm.addOverTime.splice(index, 1);
      }
      if (index !== -1) {
        this.addOverTime.splice(index, 1);
      }
    },

    // 查询当前主管所有下级员工
    queryChildUser() {
      var that = this;
      let data = {
        scheduleDate: "",
      };
      that.$http.wxqueryChildUser(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            if (res.data.length > 0) {
              that.employeesOptions = res.data;
              that.getEmployees();
            }
          } else {
            that.$message.error(res.message);
          }
        },
        (errRes) => {
          that.$message.error(errRes.message);
        }
      );
    },

    // 员工
    getEmployees() {
      let employeesNameArr = [];
      let employeesIdArr = [];
      let that = this;
      that.employeesCheckedArr.forEach((element) => {
        if (that.employeesOptions.length > 0) {
          let obj = that.employeesOptions.find((item) => {
            return item.empId === element;
          });
          if (obj) {
            employeesNameArr.push(obj.name);
            employeesIdArr.push(obj.empId);
          }
        }
      });
      this.employeesIdArr = employeesIdArr.join(",");
      this.employees = employeesNameArr.join(",");
      if (this.ruleForm.addOverTime.length > 0) {
        this.ruleForm.addOverTime[this.editIndex].employees = this.employees;
        this.$set(
          this.ruleForm.addOverTime,
          this.editIndex,
          this.ruleForm.addOverTime[this.editIndex]
        );
      }
      if (this.addOverTime.length > 0) {
        this.addOverTime[this.editIndex].operator = this.employeesIdArr;
        this.$set(
          this.addOverTime,
          this.editIndex,
          this.addOverTime[this.editIndex]
        );
      }
    },

    // 发送给员工
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (this.addOverTime.length <= 0) {
          this.$message.error("请添加时间段");
          return
        }
        for (let i = 0; i < this.addOverTime.length; i++) {
          var startTime = this.addOverTime[i].startTime;
          var endTime = this.addOverTime[i].endTime;
          if (new Date(startTime).getTime() >= new Date(endTime).getTime()) {
            return this.$message.error("开始时间不能晚于结束时间");
          }
          if (this.addOverTime[i].timeCount == "") {
            return this.$message.error("加班时长不可为空");
          }
        }
        if (valid) {
          this.disabled = true;
          let data = {
            reason: this.ruleForm.reason,
            timeoutCount: this.ruleForm.relinquishTime,
            typeId: this.ruleForm.overtimeType,
            overtimeArr: this.addOverTime,
          };
          this.$http.wxaddOvertimeApply(
            data,
            (res) => {
              if (res.code == "SUCCESS") {
                if (res.status == 0) {
                  this.$message.success(res.message);
                  setTimeout(() => {
                    this.$router.push({
                      path: "/my-workbench",
                      query: { backStatus: 0 },
                    });
                  }, 1000);
                }
              } else {
                this.$message.error(res.message);
              }
            },
            (errRes) => {
              if (errRes.code == 500) {
                this.$message.error(errRes.msg);
              }
              this.disabled = false;
              this.$message.error(errRes.message);
            }
          );
        } else {
          return false;
        }
      });
    },
  },
  mounted() {
    this.$emit("refreshbizlines", "加班申请");
    this.employeesCheckedArr = this.$route.query.employeesCheckedArr; // 加班员工
    this.startTime = this.$route.query.startTime; // 编辑开始时间
    this.endTime = this.$route.query.endTime; // 编辑结束时间
    this.ruleForm.reason = this.$route.query.reason; // 加班事由
    this.ruleForm.overtimeType = this.$route.query.overtimeType; // 加班类型
    this.ruleForm.relinquishTime = this.$route.query.relinquishTime; // 撤回时间
    this.editIndex = this.$route.query.editIndex; // 员工编辑
    this.ruleForm.department = this.$route.query.department;
    this.ruleForm.plant = this.$route.query.plant;
    this.ruleForm.workshop = this.$route.query.workshop;
    this.overtimeHoursList = this.$route.query.overtimeHoursList;
    if (this.$route.query.editIndex == undefined) {
      this.editIndex = 0;
    } else {
      this.editIndex = this.$route.query.editIndex;
    }
    if (this.$route.query.employeesCheckedArr == undefined) {
      this.employeesCheckedArr = [];
    } else {
      this.queryChildUser();
      this.employeesCheckedArr = this.$route.query.employeesCheckedArr;
    }
    if (this.$route.query.addOverTime == undefined) {
      this.ruleForm.addOverTime = [];
    } else {
      this.ruleForm.addOverTime = this.$route.query.addOverTime;
    }
    if (this.$route.query.addOverTimeAll == undefined) {
      this.addOverTime = [];
    } else {
      this.addOverTime = this.$route.query.addOverTimeAll;
    }
    this.queryOvertimeType();
    this.queryPhoneWorkTypeAll();
  },
};
</script>
