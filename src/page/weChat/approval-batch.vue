
<template>
  <div class="approverApprovalBox approverBatchBox">
    <div
      class="approverApprovalDes"
      v-infinite-scroll="loadMore"
      infinite-scroll-disabled="busy"
      infinite-scroll-distance="10"
    >
      <div
        class="flex-between"
        v-show="
          (myStatus == 1 || pendingStatus == 1 || alreadyStatus == 1) &&
          approvalLists.length > 0
        "
      >
        <el-select
          v-model="leaveTypeValue"
          placeholder="事假"
          @change="leaveTypeChange"
          clearable
        >
          <el-option
            v-for="item in leaveTypeOptions"
            :key="item.value"
            :label="item.leaveTypeName"
            :value="item.leaveTypeId"
          >
            <span style="float: left">{{ item.leaveTypeName }}</span>
            <span>（{{ item.leaveCount }}）</span>
          </el-option>
        </el-select>
        <el-select
          v-model="applyTime"
          placeholder="申请时间"
          @change="applyTimeChange"
          clearable
        >
          <el-option
            v-for="item in applyTimeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
      <div class="batchBox">
        <el-checkbox
          class="checkAll"
          :indeterminate="isIndeterminate"
          v-model="checkAll"
          @change="handleCheckAllChange"
          >全选</el-checkbox
        >
        <el-checkbox-group
          v-model="approvalChecked"
          @change="handleCheckedChange"
        >
          <el-checkbox
            v-for="(item, index) in approvalLists"
            :label="item.processinstanceid"
            :key="index"
          >
            <!-- <div class="approvalList">
              <div class="listLeft">
                <div
                  class="leftDes"
                  v-show="item.proposerName != null && item.proposerName != ''"
                >
                  <div class="proposerName">
                    {{ item.proposerName }}的加班申请
                  </div>
                </div>
                <div class="leftDes">
                  <div>所在部门：</div>
                  <div>{{ item.department }}</div>
                </div>
                <div class="leftDes">
                  <div>加班事由：</div>
                  <div>{{ item.reason }}</div>
                </div>
                <div class="leftDes">
                  <div>开始时间：</div>
                  <div>{{ item.startTime }}</div>
                </div>
                <div class="leftDes">
                  <div>结束时间：</div>
                  <div>{{ item.endTime }}</div>
                </div>
                <div class="applyTime">{{ item.applyDate }}</div>
              </div>
              <div class="listRight">
                <span class="approvalStatus">{{ item.statusName }}</span>
                <i class="el-icon-arrow-right"></i>
              </div>
            </div> -->
            <div class="approvalList">
              <div class="listLeft">
                <div
                  class="leftDes"
                  v-show="item.proposerName != null && item.proposerName != ''"
                >
                  <div class="proposerName">
                    {{ item.proposerName }}的班组申请
                  </div>
                </div>
                <div
                  class="leftDes"
                  v-show="
                    myStatus == 1 ||
                    pendingStatus == 1 ||
                    alreadyStatus == 1 ||
                    myStatus == 4 ||
                    pendingStatus == 4 ||
                    alreadyStatus == 4
                  "
                >
                  <div class="listTitle">申请人：</div>
                  <div
                    v-if="
                      myStatus == 1 || pendingStatus == 1 || alreadyStatus == 1
                    "
                    class="listContent"
                  >
                    {{ item.applyUserName }}
                  </div>
                  <div
                    v-show="
                      myStatus == 4 || pendingStatus == 4 || alreadyStatus == 4
                    "
                    class="listContent"
                  >
                    {{ item.replenishUserName }}
                  </div>
                </div>
                <div
                  class="leftDes"
                  v-show="
                    myStatus == 1 ||
                    pendingStatus == 1 ||
                    alreadyStatus == 1 ||
                    myStatus == 2 ||
                    pendingStatus == 2 ||
                    alreadyStatus == 2 ||
                    pendingStatus == 3 ||
                    alreadyStatus == 3
                  "
                >
                  <div class="listTitle">所在部门：</div>
                  <div class="listContent">{{ item.department }}</div>
                </div>

                <div
                  class="leftDes"
                  v-show="
                    myStatus == 1 || pendingStatus == 1 || alreadyStatus == 1
                  "
                >
                  <div class="listTitle">请假类型：</div>
                  <div class="listContent">{{ item.leaveTypeName }}</div>
                </div>

                <div
                  class="leftDes"
                  v-show="
                    myStatus == 2 ||
                    pendingStatus == 2 ||
                    alreadyStatus == 2 ||
                    pendingStatus == 3
                  "
                >
                  <div class="listTitle">加班事由：</div>
                  <div class="listContent">{{ item.reason }}</div>
                </div>

                <div
                  class="leftDes"
                  v-show="
                    myStatus == 4 || pendingStatus == 5 || alreadyStatus == 5
                  "
                >
                  <div class="listTitle">借出班组：</div>
                  <div class="listContent">{{ item.borrowWorkTeam }}</div>
                </div>
                <div
                  class="leftDes"
                  v-show="
                    myStatus == 4 || pendingStatus == 5 || alreadyStatus == 5
                  "
                >
                  <div class="listTitle">领班：</div>
                  <div class="listContent">{{ item.leaderName }}</div>
                </div>

                <div
                  class="leftDes"
                  v-show="
                    myStatus != 3 && pendingStatus != 4 && alreadyStatus != 4
                  "
                >
                  <div class="listTitle">开始时间：</div>
                  <div class="listContent">{{ item.startTime }}</div>
                </div>
                <div
                  class="leftDes"
                  v-show="
                    myStatus != 3 && pendingStatus != 4 && alreadyStatus != 4
                  "
                >
                  <div class="listTitle">结束时间：</div>
                  <div class="listContent">{{ item.endTime }}</div>
                </div>
                <div class="leftDes">
                  <div
                    class="listTitle"
                    v-show="
                      myStatus == 1 || pendingStatus == 1 || alreadyStatus == 1
                    "
                  >
                    请假时长：
                  </div>
                  <div class="listContent">{{ item.leaveCount }}</div>
                </div>

                <div
                  class="leftDes"
                  v-show="
                    myStatus == 3 || pendingStatus == 4 || alreadyStatus == 4
                  "
                >
                  <div class="listTitle">补卡日期：</div>
                  <div class="listContent">{{ item.replenishTime }}</div>
                </div>
                <!-- <div
            class="leftDes"
            v-show="myStatus == 3 || pendingStatus == 4 || alreadyStatus == 4"
          >
            <div class="listTitle">补卡开始时间：</div>
            <div class="listContent">{{ item.startTime }}</div>
          </div>
          <div
            class="leftDes"
            v-show="myStatus == 3 || pendingStatus == 4 || alreadyStatus == 4"
          >
            <div class="listTitle">补卡结束时间：</div>
            <div class="listContent">{{ item.endTime }}</div>
          </div> -->
                <div class="applyTime">{{ item.applyDate }}</div>
              </div>
              <div class="listRight">
                <span class="approvalStatus">{{ item.statusName }}</span>
                <i class="el-icon-arrow-right"></i>
              </div>
            </div>
          </el-checkbox>
        </el-checkbox-group>
      </div>
    </div>

    <!-- 操作 start -->
    <div class="bottomBtnBox">
      <el-button v-if="pendingStatus == 4" @click="rejectBtn">拒绝</el-button>
      <el-button v-else @click="rejectBtn">驳回</el-button>
      <el-button type="primary" @click="adoptBtn">通过</el-button>
    </div>
    <!-- 操作 end -->

    <!-- 驳回原因 start -->
    <el-dialog
      :title="pendingStatus == 4 ? '拒绝原因' : '驳回原因'"
      :visible.sync="dialogRemark"
      :close-on-click-modal="false"
      :show-close="false"
      class="dialogRemark"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        class="demo-ruleForm"
      >
        <el-form-item prop="remark" class="remark">
          <el-input
            type="textarea"
            :rows="3"
            v-model="ruleForm.remark"
            auto-complete="off"
            placeholder="输入内容"
            v-reset-page
          >
          </el-input>
        </el-form-item>

        <el-form-item class="footBtn">
          <el-button type="text" @click="cancelBtn('ruleForm')"
            >取 消</el-button
          >

          <el-button
            v-if="pendingStatus == 4"
            type="text"
            @click="submitForm('ruleForm')"
            >确认拒绝</el-button
          >
          <el-button v-else type="text" @click="submitForm('ruleForm')"
            >确认驳回</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 驳回原因 end -->
  </div>
</template>
<script>
export default {
  data() {
    var checkRemark = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("输入内容!"));
      } else {
        callback();
      }
    };
    return {
      approvalLists: [],
      approvalChecked: [],
      approvalAllChecked: [],
      dialogRemark: false,
      ruleForm: {
        remark: "",
      },
      rules: {
        remark: [{ validator: checkRemark, trigger: "blur" }],
      },
      isIndeterminate: true,
      checkAll: "",
      myStatus: "",
      pendingStatus: "",
      alreadyStatus: "",
      dialogEmployee: false,
      employeesOptions: [],
      ruleFormEmployees: {
        employeeList: [],
      },
      busy: true,
      currentPage: 1,
      pagesize: 5,
      total: 0,
      endNothing: false,
      leaveTypeValue: "",
      applyTime: "",
      leaveTypeOptions: [],
      applyTimeOptions: [
        {
          value: "1",
          label: "申请时间升序",
        },
        {
          value: "2",
          label: "请假时长降序",
        },
      ],
    };
  },
  methods: {
    leaveTypeChange() {
      this.initPendingLeave();
    },
    applyTimeChange() {
      this.initPendingLeave();
    },
    // 查询请假申请记录数量
    queryLeaveTypeCount() {
      this.$http.wxqueryLeaveTypeCount(
        null,
        (res) => {
          if (res.code == "SUCCESS") {
            this.leaveTypeOptions = res.data;
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },
    // 查询借调申请明细
    queryBorrowDetail() {
      let data = {
        processinstanceid: this.processinstanceid,
      };
      this.$http.wxqueryBorrowDetail(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            for (let i = 0; i < this.ruleForm.personNum; i++) {
              this.ruleFormEmployees.employeeList.push({
                employees: "",
              });
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },
    // 查询当前主管所有下级员工
    queryChildUser() {
      let data = {
        scheduleDate: "",
      };
      this.$http.wxqueryChildUser(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            this.employeesOptions = res.data;
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },
    // 待处理 请假审批
    initPendingLeave() {
      let data = {
        orderType: this.applyTime,
        type: this.leaveTypeValue,
      };
      this.$http.wxqueryProcessList(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            this.approvalLists = res.data;
            this.approvalAllChecked = this.approvalLists.map(
              (item) => item.processinstanceid
            );
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 待处理加班审批
    initPendingOverTime() {
      this.$http.wxqueryPendingOvertime(
        null,
        (res) => {
          if (res.code == "SUCCESS") {
            this.approvalLists = res.data.overtimeApplyVos;
            this.approvalAllChecked = this.approvalLists.map(
              (item) => item.processinstanceid
            );
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // // 待处理 加班确认
    // initPendingConfirmate(flag) {
    //   let data = {
    //     offset: this.pagesize,
    //     rows: this.currentPage,
    //   };
    //   this.$http.wxqueryOvertimePage(
    //     data,
    //     (res) => {
    //       if (res.code == "SUCCESS") {
    //         this.approvalLists = this.approvalLists.concat(res.data.data); //concat数组串联进行合并
    //         this.approvalAllChecked = this.approvalLists.map(
    //           (item) => item.processinstanceid
    //         );
    //         console.log(this.approvalAllChecked);
    //         if (flag) {
    //           //如果flag为true则表示分页
    //           this.approvalLists = this.approvalLists.concat(res.data.data); //concat数组串联进行合并
    //           this.approvalAllChecked = this.approvalLists.map(
    //             (item) => item.processinstanceid
    //           );

    //           if (res.data.data.length <= 0) {
    //             //如果数据加载完 那么禁用滚动时间 this.busy设置为true
    //             this.busy = true;
    //             this.endNothing = true;
    //           } else {
    //             this.busy = false;
    //           }
    //         } else {
    //           //第一次进入页面 完全不需要数据拼接的
    //           this.approvalLists = res.data.data;
    //           if (this.approvalLists.length >= this.pagesize) {
    //             this.busy = false;
    //           } else {
    //             this.busy = true;
    //           }
    //         }
    //       } else {
    //         this.$message.error(res.message);
    //       }
    //     },
    //     (errRes) => {
    //       this.$message.error(errRes.message);
    //     }
    //   );
    // },
    // 待处理 补卡审批
    initPendingCard() {
      this.$http.wxQueryPendingList(
        null,
        (res) => {
          if (res.code == "SUCCESS") {
            this.approvalLists = res.data.data;
            this.approvalAllChecked = this.approvalLists.map(
              (item) => item.processinstanceid
            );
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    //当属性滚动的时候  加载  滚动加载
    loadMore() {
      this.busy = true; //将无限滚动给禁用
      setTimeout(() => {
        //发送请求有时间间隔第一个滚动时间结束后才发送第二个请求
        this.currentPage++; //滚动之后加载第二页
        // 待处理
        if (this.pendingStatus == 3) {
          this.initPendingConfirmate(true);
        } else if (this.pendingStatus == 5) {
          this.initPendingSeconded(true);
        }
      }, 500);
    },

    // 待处理 借调审批
    initPendingSeconded(flag) {
      let data = {
        offset: this.pagesize,
        rows: this.currentPage,
      };
      this.$http.wxBorrowQueryPendingList(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            if (flag) {
              //如果flag为true则表示分页
              this.approvalLists = this.approvalLists.concat(res.data.data); //concat数组串联进行合并
              this.approvalAllChecked = this.approvalLists.map(
                (item) => item.processinstanceid
              );
              if (res.data.data.length <= 0) {
                //如果数据加载完 那么禁用滚动时间 this.busy设置为true
                this.busy = true;
                this.endNothing = true;
              } else {
                this.busy = false;
              }
            } else {
              //第一次进入页面 完全不需要数据拼接的
              this.approvalLists = res.data.data;
              if (this.approvalLists.length >= this.pagesize) {
                this.busy = false;
              } else {
                this.busy = true;
              }
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },
    // 全选
    handleCheckAllChange(val) {
      this.approvalChecked = val ? this.approvalAllChecked : [];
      this.isIndeterminate = false;
    },
    // 批量选择
    handleCheckedChange(value) {},

    // 审批驳回
    rejectBtn() {
      this.dialogRemark = true;
    },

    // 取消驳回
    cancelBtn(formName) {
      this.dialogRemark = false;
      this.$refs[formName].resetFields();
    },

    // 确认驳回
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.pendingStatus == 1) {
            let data = {
              processinstanceid: this.approvalChecked,
              approveStatus: 2, // 审批状态结果 1：同意 2：驳回
              approveType: 1, // 审批流程类型 1：请假流程 2：加班流程
              remark: this.ruleForm.remark,
            };
            this.$http.wxapproveProcess(
              data,
              (res) => {
                if (res.code == "SUCCESS") {
                  this.$message.success(res.message);
                  setTimeout(() => {
                    this.$router.push({
                      path: "/overtime-approval",
                      query: { pendingStatus: 1 },
                    });
                  }, 1000);
                  this.dialogRemark = false;
                  this.$refs[formName].resetFields();
                } else {
                  this.$message.error(res.message);
                }
              },
              (errRes) => {
                this.$message.error(errRes.message);
              }
            );
          } else if (this.pendingStatus == 2) {
            let data = {
              processInstanceIds: this.approvalChecked,
              status: 2, // 是否同意 1：同意 2：拒绝,默认同意
              remark: this.ruleForm.remark,
            };
            this.$http.wxapproveOvertimeApply(
              data,
              (res) => {
                if (res.code == "SUCCESS") {
                  this.$message.success(res.message);
                  setTimeout(() => {
                    this.$router.push({
                      path: "/overtime-approval",
                      query: { pendingStatus: 2 },
                    });
                  }, 1000);
                  this.dialogRemark = false;
                  this.$refs[formName].resetFields();
                } else {
                  this.$message.error(res.message);
                }
              },
              (errRes) => {
                this.$message.error(errRes.message);
              }
            );
          } else if (this.pendingStatus == 3) {
            let data = {
              processinstanceid: this.approvalChecked,
              status: 2, // 是否同意 1：同意 2：拒绝,默认同意
              remark: this.ruleForm.remark,
            };
            this.$http.wxemployeeApprove(
              data,
              (res) => {
                if (res.code == "SUCCESS") {
                  if (res.status == 0) {
                    this.$message.success(res.message);
                    setTimeout(() => {
                      this.$router.push({
                        path: "/overtime-approval",
                        query: { pendingStatus: 3 },
                      });
                    }, 1000);
                    this.dialogRemark = false;
                    this.$refs[formName].resetFields();
                  }
                } else {
                  this.$message.error(res.message);
                }
              },
              (errRes) => {
                if (errRes.code == 500) {
                  this.$message.error(errRes.msg);
                }
                this.$message.error(errRes.message);
              }
            );
          } else if (this.pendingStatus == 4) {
            let data = {
              processinstanceid: this.approvalChecked,
              status: 2, // 1：同意 2：拒绝
              rejectReason: this.ruleForm.remark,
            };
            this.$http.wxApproveReplenish(
              data,
              (res) => {
                if (res.code == "SUCCESS") {
                  this.dialogRemark = false;
                  this.$message.success(res.message);
                  this.$router.push({
                    path: "/overtime-approval",
                    query: { pendingStatus: 4 },
                  });
                } else {
                  this.$message.error(res.message);
                }
              },
              (errRes) => {
                this.$message.error(errRes.message);
              }
            );
          } else if (this.pendingStatus == 5) {
            let data = {
              processinstanceid: this.approvalChecked,
              rejectReason: this.ruleForm.remark,
              status: 2, // 1：同意 2：拒绝,默认同意
            };
            this.$http.wxapproveBorrow(
              data,
              (res) => {
                if (res.code == "SUCCESS") {
                  if (res.status == 0) {
                    this.$message.success(res.message);
                    setTimeout(() => {
                      this.$router.push({
                        path: "/overtime-approval",
                        query: { pendingStatus: 5 },
                      });
                    }, 1000);
                    this.dialogRemark = false;
                    this.$refs[formName].resetFields();
                  }
                } else {
                  this.$message.error(res.message);
                }
              },
              (errRes) => {
                if (errRes.code == 500) {
                  this.$message.error(errRes.msg);
                }
                this.$message.error(errRes.message);
              }
            );
          }
        } else {
          return false;
        }
      });
    },

    //审批通过
    adoptBtn() {
      if (this.pendingStatus == 1) {
        let data = {
          processinstanceid: this.approvalChecked,
          approveStatus: 1, // 审批状态结果 1：同意 2：驳回
          approveType: 1, // 审批流程类型 1：请假流程 2：加班流程
        };
        this.$http.wxapproveProcess(
          data,
          (res) => {
            if (res.code == "SUCCESS") {
              this.$message.success(res.message);
              setTimeout(() => {
                this.$router.push({
                  path: "/overtime-approval",
                  query: { pendingStatus: 1 },
                });
              }, 1000);
            } else {
              this.$message.error(res.message);
            }
          },
          (errRes) => {
            this.$message.error(errRes.message);
          }
        );
      } else if (this.pendingStatus == 2) {
        let data = {
          processInstanceIds: this.approvalChecked,
          status: 1, // 是否同意 1：同意 2：拒绝,默认同意
        };
        this.$http.wxapproveOvertimeApply(
          data,
          (res) => {
            if (res.code == "SUCCESS") {
              this.$message.success(res.message);
              setTimeout(() => {
                this.$router.push({
                  path: "/overtime-approval",
                  query: { pendingStatus: 2 },
                });
              }, 1000);
            } else {
              this.$message.error(res.message);
            }
          },
          (errRes) => {
            this.$message.error(errRes.message);
          }
        );
      } else if (this.pendingStatus == 3) {
        let data = {
          processinstanceid: this.approvalChecked,
          status: 1, // 是否同意 1：同意 2：拒绝,默认同意
        };
        this.$http.wxemployeeApprove(
          data,
          (res) => {
            if (res.code == "SUCCESS") {
              this.$message.success(res.message);
              setTimeout(() => {
                this.$router.push({
                  path: "/overtime-approval",
                  query: { pendingStatus: 3 },
                });
              }, 1000);
            } else {
              this.$message.error(res.message);
            }
          },
          (errRes) => {
            this.$message.error(errRes.message);
          }
        );
      } else if (this.pendingStatus == 4) {
        let data = {
          processinstanceid: this.approvalChecked,
          status: 1, // 是否同意 1：同意 2：拒绝,默认同意
        };
        this.$http.wxApproveReplenish(
          data,
          (res) => {
            if (res.code == "SUCCESS") {
              this.$message.success(res.message);
              setTimeout(() => {
                this.$router.push({
                  path: "/overtime-approval",
                  query: { pendingStatus: 4 },
                });
              }, 1000);
            } else {
              this.$message.error(res.message);
            }
          },
          (errRes) => {
            this.$message.error(errRes.message);
          }
        );
      } else if (this.pendingStatus == 5) {
        let data = {
          processinstanceid: this.approvalChecked,
          status: 1,
        };
        this.$http.wxapproveBorrow(
          data,
          (res) => {
            if (res.code == "SUCCESS") {
              this.$message.success(res.message);
              setTimeout(() => {
                this.$router.push({
                  path: "/overtime-approval",
                  query: { pendingStatus: 5 },
                });
              }, 1000);
            } else {
              this.$message.error(res.message);
            }
          },
          (errRes) => {
            this.$message.error(errRes.message);
          }
        );
      }
    },

    // 取消
    cancelBtnEmployees(formName) {
      this.dialogRemark = false;
      this.$refs[formName].resetFields();
    },
  },
  mounted() {
    this.myStatus = this.$route.query.myStatus; // myStatus 1、我的请假申请; 2、我的加班申请; 3、我的补卡申请; 4、我的借调申请
    this.pendingStatus = this.$route.query.pendingStatus; // 待处理 pendingStatus 1、请假审批; 2、加班审批; 3、加班确认; 4、补卡审批; 5、借调审批
    this.alreadyStatus = this.$route.query.alreadyStatus; // 已处理 alreadyStatus 1、请假审批; 2、加班审批; 3、加班确认; 4、补卡审批; 5、借调审批
    if (this.pendingStatus == 1) {
      this.initPendingLeave();
      this.queryLeaveTypeCount();
      this.$emit("refreshbizlines", "请假审批");
    } else if (this.pendingStatus == 2) {
      this.initPendingOverTime();
      this.$emit("refreshbizlines", "加班审批");
    } else if (this.pendingStatus == 3) {
      this.initPendingConfirmate();
      this.$emit("refreshbizlines", "加班确认");
    } else if (this.pendingStatus == 4) {
      this.initPendingCard();
      this.$emit("refreshbizlines", "补卡审批");
    } else if (this.pendingStatus == 5) {
      this.initPendingSeconded();
      this.$emit("refreshbizlines", "借调审批");
    }
  },
};
</script>