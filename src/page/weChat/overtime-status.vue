<template>
  <div class="overtimeEmployeesBox">
    <!-- 领班确认加班员工 start -->
    <div class="foremanAllLists">
      <el-collapse v-model="activeName" @change="handleChange" accordion>
        <div>
          <el-collapse-item
            :title="
              confirmLists[0].statusName + '(' + confirmLists.length + ')'
            "
            name="1"
            :class="[confirmLists.length > 0 ? 'greenMark' : '', 'foremanList']"
            v-if="confirmLists.length > 0"
          >
            <div @click="foremanChange(confirmLists)">
              <span v-for="(item, index) in confirmLists" :key="index">
                {{ item.overtimeUser }}
              </span>
            </div>
          </el-collapse-item>

          <el-collapse-item
            :title="
              noResponseLists[0].statusName + '(' + noResponseLists.length + ')'
            "
            name="2"
            :class="[
              noResponseLists.length > 0 ? 'grayMark' : '',
              'foremanList',
            ]"
            v-if="noResponseLists.length > 0"
          >
            <div @click="foremanChange(noResponseLists)">
              <span v-for="(item, index) in noResponseLists" :key="index">
                {{ item.overtimeUser }}
              </span>
            </div>
          </el-collapse-item>

          <el-collapse-item
            :title="rejectLists[0].statusName + '(' + rejectLists.length + ')'"
            name="3"
            :class="[rejectLists.length > 0 ? 'redMark' : '', 'foremanList']"
            v-if="rejectLists.length > 0"
          >
            <div @click="foremanChange(rejectLists)">
              <span v-for="(item, index) in rejectLists" :key="index">
                {{ item.overtimeUser }}
              </span>
            </div>
          </el-collapse-item>
        </div>
      </el-collapse>
    </div>
    <!-- 领班确认加班员工 end -->

    <div class="bottomBtnBox">
      <el-button type="primary" @click="foremanConfirm">确认</el-button>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      processinstanceid: "",
      foremanLists: [], // 领班查看加班人员
      processInstanceIds: [],
      activeName: "",
      confirmLists: [],
      noResponseLists: [],
      rejectLists: [],
    };
  },
  methods: {
    handleChange() {},
    getOvertimeUserAllByProcess() {
      let data = {
        processinstanceid: this.processInstanceIds,
      };
      this.$http.queryOvertimeUserAllByProcess(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            this.confirmLists = res.data.confirm;
            this.noResponseLists = res.data.noResponse;
            this.rejectLists = res.data.reject;
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },
    // 领班查看员工确认状态
    queryOvertimeApplyType() {
      let data = {
        processinstanceid: this.processInstanceIds,
      };
      this.$http.wxqueryOvertimeApplyType(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            this.foremanLists = res.data.data;
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 领班确认
    foremanConfirm() {
      let processInstanceIds = JSON.stringify(this.processInstanceIds);
      this.$router.push({
        path: "/check-confirmation",
        query: {
          processinstanceid: this.processinstanceid,
          processInstanceIds: processInstanceIds,
        },
      });
    },
    foremanChange(list) {
      let listAll = JSON.stringify(list);
      console.log(listAll,'listAll')
      let processInstanceIds = JSON.stringify(this.processInstanceIds);
      this.$router.push({
        path: "/overtimestatus-confirmation",
        query: {
          listAll: listAll,
          processinstanceid: this.processinstanceid,
          processInstanceIds: processInstanceIds,
        },
      });
    },
  },
  mounted() {
    this.$emit("refreshbizlines", "加班确认状态");
    this.processinstanceid = this.$route.query.processinstanceid;
    this.processInstanceIds = JSON.parse(this.$route.query.processInstanceIds);
    // this.queryOvertimeApplyType();
    this.getOvertimeUserAllByProcess();
  },
};
</script>