<template>
    <div class="secondedApplicationBox">
        <el-form  :model="ruleForm" :rules="rules" ref="ruleForm" class="demo-ruleForm">
            <el-form-item class="formItem">
                <div>借入班组：{{ruleForm.workTeam}}</div>
            </el-form-item>
            <el-form-item prop="team" class="formItem defFormItem" label="借出班组">
                <el-select v-model="ruleForm.team" filterable placeholder="请选择" @change="selectTeam(ruleForm.team)" v-reset-page>
                    <el-option
                        v-for="item in teamOptions"
                        :key="item.id"
                        :label="item.workTeam"
                        :value="item.workTeam+'&'+item.name+'&&'+item.empId" >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item class="formItem" v-show="ruleForm.leaveNotice !='' && ruleForm.leaveNotice != 'null' ">
                <div class="leaveNotice">领班：{{ruleForm.leaveNotice}}</div>
            </el-form-item>
            <el-form-item prop="workType" class="formItem defFormItem" label="班次:">
                <el-select v-model="ruleForm.workType" placeholder="请选择班次" v-reset-page>
                    <el-option
                        v-for="item in shiftOptions"
                        :key="item.name"
                        :label="item.name"
                        :value="item.name" >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item prop="startTime" class="formItem timeFormItem" label="开始时间">
                <el-input type="text" 
                    v-model="ruleForm.startTime" 
                    placeholder="请选择开始时间"
                    @focus="startShow()"
                    readonly="readonly">
                </el-input>
                <i class="el-icon-date"></i>
            </el-form-item>
            <el-form-item prop="endTime" class="formItem timeFormItem" label="结束时间">
                <el-input type="text" 
                    v-model="ruleForm.endTime" 
                    placeholder="请选择开始时间"
                    @focus="endShow()"
                    readonly="readonly">
                </el-input>
                <i class="el-icon-date"></i>
            </el-form-item>

            <!-- 时间段选择 start -->
            <div class="timeFormItem">
                <vue-hash-calendar
                    ref="picker"
                    model="dialog"
                    :scroll-change-date="true"
                    :visible.sync="isShowAddCalendar"
                    :is-show-week-view="false"
                    format="YY-MM-DD hh:mm:ss"
                    week-start="sunday"
                    picker-type="datetime"
                    :show-today-button="true"
                    :disabled-week-view="false"
                    @confirm="dateAddConfirm"
                    @click="dateAddClick"
                    @change="dateAddChange"
                    :disabled-date="disabledDate"
                    :minuteStep='30'>
                </vue-hash-calendar>
            </div>
            <!-- 时间段选择 end -->

            <el-form-item prop="personNum" class="formItem defFormItem" label="借调人数">
                <el-input type="text" v-model="ruleForm.personNum" placeholder="请输入" @blur="blurText($event)" min='1' max="50" v-reset-page></el-input>
            </el-form-item>

            <!-- 操作 -->
            <el-form-item class="formItem bottomBtnBox">
                <el-button type="primary" @click="submitForm('ruleForm')" :disabled="disabled">提交借调申请</el-button>
            </el-form-item>
            <!-- 操作 -->
        </el-form>
    </div>
</template>
<script>
export default {
    data(){
        return{
            ruleForm:{
                team:'',
                startTime:'',
                endTime:'',
                leaveNotice:'',
                personNum:'',
                leaderId:'',
                workTeam:'',
                workType:'',
            },
            disabled:false,
            teamOptions:[],
            rules: {
                
            },
            timeStatus:'',// 0 开始时间 1 结束时间
            isShowAddCalendar:false,
            shiftOptions:[],        
        }
    },
    methods: {

        // 查询所有的领班
        queryLeaderAll(){
            this.$http.wxqueryLeaderAll(null,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.teamOptions = res.data.data;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },

        // 查询所有班次信息
        queryPhoneWorkTypeAll(){
            this.$http.wxqueryPhoneWorkTypeAll(null,(res)=>{
                if(res.code == 'SUCCESS'){
                this.shiftOptions = res.data.data
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },
        
        // 选择 借调班组 
        selectTeam(value){
            this.ruleForm.leaveNotice = value.split('&')[1];
            this.ruleForm.leaderId = value.split('&&')[1];
        },

        // 数字值验证
        blurText(e){
            let boolean = new RegExp("^[1-9][0-9]*$").test(e.target.value);
            if(!boolean){
                this.$message.warning("请输入正确人数");
                e.target.value = '';
            }
        },

        // 显示日历 开始时间
        startShow(){
            this.timeStatus = 0;
            this.isShowAddCalendar = true;
        },
        
        // 显示日历 结束时间
        endShow(){
            this.timeStatus = 1;
            this.isShowAddCalendar = true;
        },

        // 日期改变触发
        dateAddChange(date) {
        },

        // 点击确认按钮触发 -- 添加
        dateAddConfirm(date) {
            // if(this.timeStatus == 0){
            //     this.ruleForm.startTime = date;
            // }else if(this.timeStatus == 1){
            //     this.ruleForm.endTime = date;
            // }
            if(this.timeStatus == 0){
                this.ruleForm.startTime = date;
                var startTimeShow = (this.ruleForm.startTime).slice(0,16);
                var endTimeShow = (this.ruleForm.endTime).slice(0,16)
                var startTimeDes = new Date(startTimeShow).getTime();
                var endTimeDes = new Date(endTimeShow).getTime();
                if(startTimeDes >= endTimeDes){
                    this.ruleForm.startTime = '';
                    this.ruleForm.endTime = ''
                    this.$message.warning("开始时间不得大于结束时间");
                }
            }else if(this.timeStatus == 1){
                this.ruleForm.endTime = date;
                var startTimeShow = (this.ruleForm.startTime).slice(0,16);
                var endTimeShow = (this.ruleForm.endTime).slice(0,16)
                var startTimeDes = new Date(startTimeShow).getTime();
                var endTimeDes = new Date(endTimeShow).getTime();
                if(endTimeDes <= startTimeDes){
                    this.ruleForm.startTime = '';
                    this.ruleForm.endTime = ''
                    this.$message.warning("结束时间不得小于开始时间");
                }
            }
        },

        // 点击日期时按钮触发 -- 添加
        dateAddClick(date) {
        },

        // 禁用的日期
        disabledDate(date) {
            // 开始时间小于结束时间
            if(this.timeStatus == 0){
                let endTime
                if(this.ruleForm.endTime != ''){
                    let endTimeIndex = this.ruleForm.endTime;
                    if(endTimeIndex != undefined){
                        endTime = endTimeIndex.slice(0,16);
                        let timestamp = date.getTime();
                        if (timestamp > new Date(endTime).getTime()) {
                            return true
                        }
                        return false
                    }
                }
            }else if(this.timeStatus == 1){ // 结束时间大于开始时间
                let startTime
                if(this.ruleForm.startTime !=''){
                    let startTimeIndex = this.ruleForm.startTime;
                    if(startTimeIndex != undefined){
                        startTime = startTimeIndex.slice(0,16);
                        let timestamp = date.getTime();
                        if ( timestamp <  (new Date(startTime).getTime()) - 86400000) {
                            return true
                        }
                        return false
                    }
                }
            }
            
        },
        
        // 提交请假申请
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let boolean = new RegExp("^[1-9][0-9]*$").test(this.ruleForm.personNum);
                    if(!boolean){
                        this.$message.warning("请输入正确人数");
                        this.ruleForm.personNum = '';
                        return
                    }
                    let data = {
                       borrowWorkTeam:this.ruleForm.team.split('&')[0],
                       endTime:this.ruleForm.endTime,
                       leader:this.ruleForm.leaderId,
                       passivityCount:this.ruleForm.personNum,
                       startTime:this.ruleForm.startTime,
                       workType:this.ruleForm.workType,
                    }
                    this.disabled = true;
                    this.$http.wxsubmitBorrow(data,(res)=>{
                        if(res.code == 'SUCCESS'){
                            if(res.status == 0){
                                this.$message.success(res.message);
                                setTimeout(() =>{
                                    this.$router.push({ path:'/my-workbench',query:{backStatus:0}})
                                },1000);
                            }
                        }else{
                            this.$message.error(res.message);
                        }
                    },(errRes)=>{
                        if(errRes.code == 500){
                            this.$message.error(errRes.msg);
                        }
                        this.disabled = false;
                        this.$message.error(errRes.message);
                    })  
                } else {
                    return false;
                }
            });
        },
    },
    mounted(){
        this.queryLeaderAll();
        this.queryPhoneWorkTypeAll();
        this.$emit('refreshbizlines','借调申请'); 
        if(this.$route.query.workTeam == undefined){
            this.ruleForm.workTeam  = ''
        }else{
            this.ruleForm.workTeam = this.$route.query.workTeam
        }
    }
}
</script>