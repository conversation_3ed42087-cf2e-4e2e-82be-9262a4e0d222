<template>
  	<div class="modalBox">
        <div class="content">
            <div class="returnLine">
                <div v-bind:class="[defClass, activeClass]" @click="returnBtn">
                    <img :src="img"  alt="">
                </div>
                <div class="headerTitle">
                    <div>{{headerTitle}}</div>
                </div>
            </div>
            <!-- 内容 start -->
            <transition>
                <!-- v-on 来做个监测的函数来检测 -->
                <router-view  v-on:refreshbizlines="initTitle"  v-if="status == 0"></router-view>
            </transition>
            <!-- 内容 end -->
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            status:0,
            headerTitle:'',
            img:'../static/images/weChat/left.png',
            defClass:'return',
            activeClass:'',
        }
    },
    methods: {
        // 返回上一页
        returnBtn(){
            this.$router.go(-1); 
        },

        initTitle(title,activeClass){
            this.headerTitle = title;
            this.activeClass = activeClass;
        },

    },
	mounted(){
       
    },  
  }
</script>