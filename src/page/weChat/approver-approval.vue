
<template>
    <div class="approverApprovalBox">
        <div class="approverApprovalDes">
            <div class="approvalList" v-for="(item, index) in approvalLists" :key="index" @click="toDetailsBtn(item.processinstanceid)">
                <div class="listLeft">
                    <div class="leftDes" v-show="item.proposerName != null && item.proposerName != ''">
                        <div class="proposerName">{{item.proposerName}}的加班申请</div>
                    </div>
                    <div class="leftDes">
                        <div>所在部门：</div>
                        <div>{{item.department}}</div>
                    </div>
                    <div class="leftDes" >
                        <div>加班事由：</div>
                        <div>{{item.reason}}</div>
                    </div>
                    <div class="leftDes" >
                        <div>开始时间：</div>
                        <div>{{item.startTime}}</div>
                    </div>
                    <div class="leftDes">
                        <div>结束时间：</div>
                        <div>{{item.endTime}}</div>
                    </div>
                    <div class="applyTime">{{item.applyDate}}</div>
                </div>
                <div class="listRight">
                    <span class="approvalStatus">{{item.statusName}}</span>
                    <i class="el-icon-arrow-right"></i>
                </div>
            </div>
        </div>

        <!-- 操作 start -->
        <div class="bottomBtnBox">
            <el-button type="primary" @click="batchApprovalBtn" :disabled='isDisable'>批量审批</el-button>
        </div>
        <!-- 操作 end -->
    </div>
</template>
<script>
export default {
    data(){
        return{
            approvalLists:[],
            isDisable:'',
        }
    },
    methods: {
        // 待处理加班审批
        initPendingOvertime(){
            this.$http.wxqueryPendingOvertime(null,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.approvalLists = res.data.overtimeApplyVos;
                    if(this.approvalLists.length > 0){
                        this.isDisable = false;
                    }else{
                        this.isDisable = true;
                    }
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },
        
        // 审批人确认
        toDetailsBtn(id){
            this.$router.push({ path:'/approver-confirmation',query:{processInstanceIds:[id],processinstanceid:id}})
        },

        // 批量审批
        batchApprovalBtn(){
            this.$router.push({ path:'/approval-batch'})
        },
    },
    mounted(){
        this.$emit('refreshbizlines','加班审批');  
        this.initPendingOvertime()
    }
}
</script>