<template>
  <div class="overtimeEmployeesBox">
    <!-- 领班确认加班员工 start -->
    <div class="foremanLists">
      <div
        class="foremanList"
        v-for="(item, index) in listAll"
        :key="index"
      >
        <div class="foremanListLeft">
          <div class="overtimeUser">
            {{ item.overtimeUser
            }}<i class="borrowMark" v-show="item.borrow != null">{{
              item.borrow
            }}</i>
          </div>
          <div class="updateTime" v-show="item.status == 2">
            {{ item.updateTime }}
          </div>
          <div class="redMark" v-show="item.remark != null">
            拒绝原因：{{ item.remark }}
          </div>
        </div>
        <!-- 状态 1：未答复 2：已确认 3：已拒绝  -->
        <div
          :class="[
            item.status == 2 ? 'greenMark' : '',
            item.status == 3 ? 'redMark' : '',
            item.status == 1 ? 'grayMark' : '',
          ]"
        >
          {{ item.statusName }}
        </div>
      </div>
    </div>
    <!-- 领班确认加班员工 end -->

    <div class="bottomBtnBox">
      <el-button type="primary" @click="foremanConfirm">确认</el-button>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      listAll: [],
      processinstanceid: "",
      processInstanceIds: [],
    };
  },
  methods: {
    // 领班确认
    foremanConfirm() {
      let processInstanceIds = JSON.stringify(this.processInstanceIds);
      this.$router.push({
        path: "/overtime-status",
        query: {
          processinstanceid: this.processinstanceid,
          processInstanceIds: processInstanceIds,
        },
      });
    },
  },
  mounted() {
    this.$emit("refreshbizlines", "加班确认状态");
    this.listAll = JSON.parse(this.$route.query.listAll);
    this.processinstanceid = this.$route.query.processinstanceid;
    this.processInstanceIds = JSON.parse(this.$route.query.processInstanceIds);
  },
};
</script>