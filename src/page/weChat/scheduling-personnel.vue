 <template>
  <!-- 选择人员 -->
  <div class="schedulingBox">
    <div class="navScheduling">
      <div class="schedulingNav" @click="personnelBtn">按人员排班</div>
      <div class="schedulingNavActive" @click="dateBtn">按日期排班</div>
    </div>
    <div class="schedulingEmployeesBox">
      <div class="block">
        <el-date-picker
          v-model="selectDate"
          type="date"
          placeholder="选择月"
          :clearable="false"
          @change="selectTime"
          value-format="yyyy-MM-dd"
          disabled
        >
        </el-date-picker>
      </div>
      <div class="checkEmployees">
        <el-input
          placeholder="请输入工号或者姓名"
          v-model="empId"
          clearable
          @input="queryChildUser"
        >
        </el-input>
      </div>
      <div class="employeesLists">
        <div
          class="employeesList"
          v-if="employeesLists.length <= 0"
          style="justify-content: center"
        >
          <div>暂无数据！</div>
        </div>
        <div
          class="employeesList"
          v-else
          @click="checkScheduling(item.empId, item.workType)"
          v-for="(item, index) in employeesLists"
          :key="index"
        >
          <div>{{ item.name }}</div>
          <div>{{ item.workType }}</div>
        </div>
      </div>
    </div>

    <!-- 选择班次 start -->
    <el-dialog
      title="选择班次"
      :visible.sync="dialogRemark"
      :close-on-click-modal="false"
      :show-close="false"
      class="dialogRemark"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        class="demo-ruleForm"
      >
        <el-form-item prop="team" class="remark" label="班次:">
          <el-select v-model="ruleForm.team" placeholder="请选择班次">
            <el-option
              v-for="item in teamOptions"
              :key="item.name"
              :label="item.name"
              :value="item.name"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item class="footBtn">
          <el-button type="text" @click="cancelBtn('ruleForm')"
            >取 消</el-button
          >
          <el-button type="text" @click="submitForm('ruleForm')"
            >确 认</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 选择班次 end -->
  </div>
</template>
<script>
export default {
  data() {
    var checkTeam = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请选择班次!"));
      } else {
        callback();
      }
    };
    return {
      selectDate: "",
      employeesLists: [],
      dialogRemark: false,
      teamOptions: [],
      empId: "",
      ruleForm: {
        team: "",
      },
      rules: {
        team: [{ validator: checkTeam, trigger: "blur" }],
      },
      employeesOption: [],
      empId: "",
    };
  },
  methods: {
    // 按照人员排班 nav
    personnelBtn() {
      this.$router.push({ path: "/scheduling" });
    },

    // 按照日期排班 nav
    dateBtn() {
      this.$router.push({ path: "/scheduling-date" });
    },

    // 查询考勤
    selectTime(value) {
      this.selectDate = value;
      this.queryChildUser();
    },

    // 查询当前主管所有下级员工
    queryChildUser() {
      let data = {
        scheduleDate: this.selectDate,
        empId: this.empId,
      };
      this.$http.wxqueryChildUser(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            this.employeesLists = res.data;
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 查询当前主管]下级员工
    getChildUser(empId) {
      if (empId !== "") {
        let data = {
          empId: empId,
        };
        this.$http.wxqueryChildUser(
          data,
          (res) => {
            if (res.code == "SUCCESS") {
              this.employeesOption = res.data;
            } else {
              this.employeesOption = [];
            }
          },
          (errRes) => {
            this.employeesOption = [];
          }
        );
      } else {
        this.options = [];
      }
    },

    // 查询所有班次信息
    queryPhoneWorkTypeAll() {
      this.$http.wxqueryPhoneWorkTypeAll(
        null,
        (res) => {
          if (res.code == "SUCCESS") {
            this.teamOptions = res.data.data;
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 选择人员排班
    checkScheduling(id, team) {
      if (team == null) {
        this.$message.error("还未排班不能修改");
      } else {
        this.dialogRemark = true;
        this.empId = id;
        this.ruleForm.team = team;
      }
    },

    // 选择班次 取消
    cancelBtn(formName) {
      this.dialogRemark = false;
      this.$refs[formName].resetFields();
    },

    // 选择班次  确认
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = {
            empId: this.empId,
            scheduleDate: this.selectDate,
            workType: this.ruleForm.team,
          };
          this.$http.wxupdateUserSchedule(
            data,
            (res) => {
              if (res.code == "SUCCESS") {
                if (res.status == 0) {
                  this.$message.success(res.message);
                  this.dialogRemark = false;
                  this.$refs[formName].resetFields();
                  this.queryChildUser();
                }
              } else {
                this.$message.error(res.message);
              }
            },
            (errRes) => {
              this.$message.error(errRes.message);
            }
          );
        } else {
          return false;
        }
      });
    },
  },
  mounted() {
    this.$emit("refreshbizlines", "排班");
    this.selectDate = this.$route.query.date;
    this.queryChildUser();
    this.queryPhoneWorkTypeAll();
  },
};
</script>