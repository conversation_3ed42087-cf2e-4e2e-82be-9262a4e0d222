<template>
    <div class="secondedApplicationBox">
        <el-form  :model="ruleForm" :rules="rules" ref="ruleForm" class="demo-ruleForm">
            <el-form-item prop="team" class="formItem defFormItem" label="借入班组">
                <div>{{ruleForm.workTeam}}</div>
            </el-form-item>
            <el-form-item prop="team" class="formItem defFormItem" label="借出班组">
                <div>{{ruleForm.team}}</div>
            </el-form-item>
            <el-form-item class="formItem" v-show="ruleForm.leaveNotice !='' && ruleForm.leaveNotice != 'null' && ruleForm.leaveNotice != null">
                 <div class="leaveNotice">领班：{{ruleForm.leaveNotice}}</div>
            </el-form-item>
            <el-form-item class="formItem">
                <div class="leaveNotice" >班次：{{ruleForm.workType}}</div>
            </el-form-item>
            <el-form-item prop="startTime" class="formItem defFormItem styleForm" label="开始时间">
                <el-date-picker
                    v-model="ruleForm.startTime"
                    type="datetime"
                    placeholder="请选择"
                    :clearable = false
                    value-format="yyyy-MM-dd HH:mm:ss"
                    disabled>
                </el-date-picker>
                </el-form-item>
                <el-form-item prop="endTime" class="formItem defFormItem styleForm" label="结束时间">
                <el-date-picker
                    v-model="ruleForm.endTime"
                    type="datetime"
                    placeholder="请选择"
                    :clearable = false
                    value-format="yyyy-MM-dd HH:mm:ss"
                    disabled>
                </el-date-picker>
                </el-form-item>
            <el-form-item prop="personNum" class="formItem defFormItem" label="借调人数">
                <div>{{ruleForm.personNum}}</div>
            </el-form-item>
            <el-form-item prop="status" class="formItem defFormItem" label="状态">
                <div >{{ruleForm.status}}</div>
            </el-form-item>
            <el-form-item v-show="ruleForm.secondedPeople != null" prop="personNum" class="formItem defFormItem" label="借调人员">
                <div>{{ruleForm.secondedPeople}}</div>
            </el-form-item>
            <div class="formItem proposerShow">
                <div class="applyList">
                    <div>{{ruleForm.applyDate}}</div>
                    <div>申请人:</div>
                    <div>{{ruleForm.proposer}}</div>
                </div>
                <div class="approveArrayList" v-for="(item,index) in ruleForm.approveArray" :key="index" v-show="ruleForm.approveArray.length > 0">
                    <div class="approveArray">
                        <div>{{item.approveTime}}</div>
                        <div>{{item.approveUserName}}</div>
                        <div>{{item.approveStatusName}}借调</div>
                    </div>
                    <div class="approveArray approveReject" v-show="item.remark != null">
                        <div>拒绝原因：</div>
                        <div>{{item.remark}}</div>
                    </div>
                </div>
            </div>
        </el-form>
    </div>
</template>
<script>
export default {
    data(){
        return{
            ruleForm:{
                team:'',
                startTime:'',
                endTime:'',
                leaveNotice:'',
                personNum:'',
                secondedPeople:'',
                approveArray:[],
                workTeam:'',
                workType:'',
            },
            processinstanceid:'',
            rules: {
                
            },
        }
    },
    methods: {
       // 查询借调申请明细
        queryBorrowDetail(){
            let data = {
                processinstanceid:this.processinstanceid
            }
            this.$http.wxqueryBorrowDetail(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.ruleForm.team = res.data.data.borrowWorkTeam;
                    this.ruleForm.startTime = res.data.data.startTime;
                    this.ruleForm.endTime = res.data.data.endTime;
                    this.ruleForm.leaveNotice = res.data.data.leaderName;
                    this.ruleForm.personNum = res.data.data.passivityCount;
                    this.ruleForm.status = res.data.data.statusName;
                    this.ruleForm.proposer = res.data.data.applyUserName;
                    this.ruleForm.applyDate = res.data.data.applyDate;
                    this.ruleForm.secondedPeople = res.data.data.borrowUserName;
                    this.ruleForm.approveArray = res.data.approveArray;
                    this.ruleForm.workTeam = res.data.data.workTeam;
                    this.ruleForm.workType = res.data.data.workType;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },
    },
    mounted(){
        this.$emit('refreshbizlines','借调申请');  
        this.processinstanceid = this.$route.query.processinstanceid; 
        this.queryBorrowDetail();
    }
}
</script>