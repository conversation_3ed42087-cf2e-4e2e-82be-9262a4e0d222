<template>
  <div class="applyCardBox">
    <el-form :model="ruleForm" class="demo-ruleForm">
      <el-form-item label="申请人" class="formItem styleForm">
        {{ ruleForm.replenishUserName }}
      </el-form-item>
      <el-form-item label="补卡日期" class="formItem styleForm">
        <el-date-picker v-model="ruleForm.replenishTime" type="dateTime" format="YY-MM-DD hh:mm:ss"
          value-format="YY-MM-DD hh:mm:ss" disabled :clearable="false">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="班次日期" class="formItem styleForm">
        <el-date-picker v-model="ruleForm.workTypeDate" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" disabled
          :clearable="false">
        </el-date-picker>
      </el-form-item>
      <!-- <el-form-item label="开始时间" prop="startTime" class="formItem styleForm">
                <el-time-picker
                    v-model="ruleForm.startTime"
                    format="HH:mm"
                    value-format="HH:mm"
                    disabled
                    :clearable = false>
                </el-time-picker>
            </el-form-item>
            <el-form-item label="结束时间" prop="endTime" class="formItem styleForm">
                <el-time-picker
                    v-model="ruleForm.endTime"
                    format="HH:mm"
                    value-format="HH:mm"
                    disabled
                    :clearable = false>
                </el-time-picker>
            </el-form-item> -->
      <el-form-item label="出入" class="formItem styleForm">
        {{ ruleForm.replenishStatusName }}
      </el-form-item>
      <el-form-item label="补卡原因" class="formItem styleForm">
        {{ ruleForm.typeName }}
      </el-form-item>
      <el-form-item label="备注" class="formItem defFormItem styleForm">
        {{ ruleForm.reason }}
      </el-form-item>
      <el-form-item label="状态" class="formItem styleForm">
        {{ ruleForm.statusName }}
      </el-form-item>
      <div class="formItem proposerShow processBox" label="">
        <!-- <div class="applyList">
                    <div>{{ruleForm.applyDate}}</div>
                    <div>申请人:</div>
                    <div>{{ruleForm.proposer}}</div>
                </div> -->

        <div class="approvalShowDes" v-show="imgArray.length > 0">
          <div class="titleImg">图片</div>
          <div class="approvalImgUrl">
            <div class="imgList emo-image__preview" v-for="(item, index) in imgArray" :key="index">
              <!-- <img :src="item" alt=""> -->
              <el-image :src="item" :preview-src-list="imgArray"> </el-image>
            </div>
          </div>
        </div>
        <div class="approveArrayList" v-for="(item, index) in approveArray" :key="index">
          <div class="approveArray">
            <div>{{ item.approveTime }}</div>
            <div>{{ item.approveUserName }}</div>
            <div>{{ item.approveStatusName }}补卡</div>
          </div>
          <div class="approveArray approveReject" v-show="item.remark != null">
            <div>拒绝原因：</div>
            <div>{{ item.remark }}</div>
          </div>
        </div>
      </div>
      <el-form-item class="formItem bottomBtnBox" v-show="ruleForm.status == 1">
        <el-button class="refuseBtn" type="primary" @click="refuseFrom()">拒绝</el-button>
        <el-button type="primary" @click="submitForm()">确认</el-button>
      </el-form-item>
    </el-form>

    <!-- 拒绝原因dialog start -->
    <el-dialog class="dialogRemark" title="填写拒绝原因" :visible.sync="dialogRefuse" :close-on-click-modal="false"
      :show-close="false">
      <el-form :model="ruleRefuseForm" :rules="rules" ref="ruleRefuseForm" class="demo-ruleForm">
        <el-form-item prop="remark" class="remark">
          <el-input v-model="ruleRefuseForm.remark" type="textarea" :rows="3" auto-complete="off" placeholder="输入内容"
            v-reset-page>
          </el-input>
        </el-form-item>
        <el-form-item class="footBtn">
          <el-button type="text" @click="cancelBtn('ruleRefuseForm')">取 消</el-button>
          <el-button type="text" @click="submitBtn('ruleRefuseForm')">确认拒绝</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 拒绝原因dialog end -->
  </div>
</template>
<script>
export default {
  data() {
    var checkRemark = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("输入内容!"));
      } else {
        callback();
      }
    };
    return {
      ruleForm: {
        replenishUser: "",
        replenishDate: "",
        replenishTime: "",
        workTypeDate: "",
        startTime: "",
        endTime: "",
        reason: "",
        status: "", //1：待审批 2：已审批 3：被拒绝
        statusName: "",
        applyType: "",
        typeName: "",
        replenishStatusName: "",
      },
      approveArray: "",
      processinstanceid: "",
      myStatus: "", // 1、我的请假申请; 2、我的加班申请; 3、我的补卡申请
      pendingStatus: "", // 待处理 1、请假审批; 2、加班审批; 3、加班确认; 4、补卡审批
      alreadyStatus: "", // 已处理 1、请假审批; 2、加班审批; 3、加班确认; 4、补卡审批
      // 拒绝原因dialog
      dialogRefuse: false,
      ruleRefuseForm: {
        remark: "",
      },
      rules: {
        remark: [{ validator: checkRemark, trigger: "blur" }],
      },
      processInstanceIds: [],
      imgArray: []
    };
  },
  methods: {
    // 查看补卡申请详细
    initCard() {
      let data = {
        processinstanceid: this.processinstanceid,
      };
      this.$http.wxQueryReplenishDetail(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            this.ruleForm = res.data.data;
            if (res.data.data.attaUrl) {
              this.imgArray = res.data.data.attaUrl.split(',')
            }
            this.ruleForm.applyType = parseInt(res.data.data.type);
            this.approveArray = res.data.approveArray;
            if (this.myStatus == 3) {
              this.ruleForm.status = false;
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 拒绝原因
    refuseFrom() {
      this.dialogRefuse = true;
    },

    // 确认
    submitForm() {
      let data = {
        processinstanceid: this.processInstanceIds,
        status: 1, // 1：同意 2：拒绝
        rejectReason: this.ruleRefuseForm.remark,
      };
      this.$http.wxApproveReplenish(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            this.$message.success(res.message);
            this.$router.push({
              path: "/overtime-approval",
              query: { pendingStatus: 4 },
            });
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 确认拒绝
    submitBtn(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = {
            processinstanceid: this.processInstanceIds,
            status: 2, // 1：同意 2：拒绝
            rejectReason: this.ruleRefuseForm.remark,
          };
          this.$http.wxApproveReplenish(
            data,
            (res) => {
              if (res.code == "SUCCESS") {
                this.dialogRefuse = false;
                this.$message.success(res.message);
                this.$router.push({
                  path: "/overtime-approval",
                  query: { pendingStatus: 4 },
                });
              } else {
                this.$message.error(res.message);
              }
            },
            (errRes) => {
              this.$message.error(errRes.message);
            }
          );
        } else {
          return false;
        }
      });
    },

    // 取消拒绝
    cancelBtn(formName) {
      this.dialogRefuse = false;
      this.$refs[formName].resetFields();
    },
  },
  mounted() {
    this.$emit("refreshbizlines", "补卡申请");
    if (this.$route.query.processinstanceid) {
      this.processinstanceid = this.$route.query.processinstanceid;
      let newStr = this.$route.query.processinstanceid.split(",");
      this.processInstanceIds = newStr;
    }
    if (this.$route.query.processInstanceIds) {
      this.processInstanceIds = JSON.parse(
        this.$route.query.processInstanceIds
      );
    }
    this.myStatus = this.$route.query.myStatus; // myStatus 1、我的请假申请; 2、我的加班申请; 3、我的补卡申请
    this.pendingStatus = this.$route.query.pendingStatus; // 待处理 pendingStatus 1、请假审批; 2、加班审批; 3、加班确认; 4、补卡审批
    this.alreadyStatus = this.$route.query.alreadyStatus; // 已处理 alreadyStatus 1、请假审批; 2、加班审批; 3、加班确认; 4、补卡审批
    this.initCard();
    if (
      this.myStatus == undefined &&
      this.pendingStatus == undefined &&
      this.alreadyStatus == undefined
    ) {
      this.pendingStatus = 4;
    }
  },
};
</script>