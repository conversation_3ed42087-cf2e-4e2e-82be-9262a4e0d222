<template>
  <div class="overtimeEmployeesBox">
    <!-- 加班员工 start-->

    <div class="employeesLists">
      <div class="checkEmployees">
        <el-input
          placeholder="请输入工号或者姓名"
          v-model="empId"
          clearable
          @input="queryChildUser"
        >
        </el-input>
      </div>
      <div v-if="employeesLists.length > 0">
        <el-checkbox
          class="checkAll"
          :indeterminate="isIndeterminate"
          v-model="checkAll"
          @change="handleCheckAllChange"
          >全选</el-checkbox
        >
        <el-checkbox-group
          class="employeesList"
          v-model="employees"
          @change="handleCheckedEmployeesChange"
        >
          <el-checkbox
            v-for="(item, index) in employeesLists"
            :label="item.empId"
            :key="index"
            >{{ item.name }}
            <i class="borrowMark" v-show="item.borrow != null">{{
              item.borrow
            }}</i>
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <div v-else class="approvalListsNothing">暂无人员！</div>
    </div>
    <!-- 加班员工 end -->

    <div class="bottomBtnBox">
      <el-button type="primary" @click="employeesConfirm">确认</el-button>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      checkList: [],
      checkAll: false,
      employeesLists: [],
      addOverTime: [],
      addOverTimeAll: [],
      startTime: "",
      endTime: "",
      reason: "",
      overtimeType: "",
      relinquishTime: "",
      employees: [], // 选中人员
      employeesCheckedArr: [], // 全部人员
      addShow: "",
      isIndeterminate: true, //全选状态
      editIndex: "",
      editStatus: "",
      department: "",
      plant: "",
      workshop: "",
      employeesOption: [],
      empId: "",
    };
  },
  methods: {
    // 查询当前主管所有下级员工
    queryChildUser() {
      let data = {
        scheduleDate: "",
        empId: this.empId,
      };
      this.$http.wxqueryChildUser(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            this.employeesLists = res.data;
            for (let i = 0; i < this.employeesLists.length; i++) {
              this.employeesCheckedArr.push(this.employeesLists[i].empId);
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },
    // 查询当前主管]下级员工
    getChildUser(empId) {
      if (empId !== "") {
        let data = {
          empId: empId,
        };
        this.$http.wxqueryChildUser(
          data,
          (res) => {
            if (res.code == "SUCCESS") {
              this.employeesOption = res.data;
            } else {
              this.employeesOption = [];
            }
          },
          (errRes) => {
            this.employeesOption = [];
          }
        );
      } else {
        this.options = [];
      }
    },

    // 员工确认
    employeesConfirm() {
      this.$router.push({
        path: "/overtime-application",
        query: {
          employeesCheckedArr: this.employees,
          addOverTime: this.addOverTime,
          reason: this.reason,
          overtimeType: this.overtimeType,
          relinquishTime: this.relinquishTime,
          addOverTimeAll: this.addOverTimeAll,
          editIndex: this.editIndex,
          department: this.department,
          plant: this.plant,
          workshop: this.workshop,
        },
      });
    },

    // 去重
    unique(arr) {
      const res = new Map();
      return arr.filter((arr) => !res.has(arr) && res.set(arr, 1));
    },

    // 全选
    handleCheckAllChange(val) {
      this.employeesCheckedArr = this.unique(this.employeesCheckedArr);
      this.isIndeterminate = false;
      if (val) {
        this.employees = this.employeesCheckedArr;
      } else {
        this.employees = [];
      }
    },

    // 选中
    handleCheckedEmployeesChange(value) {
      this.employeesCheckedArr = this.unique(this.employeesCheckedArr);
      this.employees = value;
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.employeesCheckedArr.length;
      this.isIndeterminate =
        checkedCount >= 0 && checkedCount < this.employeesCheckedArr.length;
    },
  },
  mounted() {
    this.$emit("refreshbizlines", "选择加班员工", "returnNone");
    this.employeesShow = this.$route.query.employeesShow;
    this.addOverTime = this.$route.query.addOverTime;
    this.addOverTimeAll = this.$route.query.addOverTimeAll;
    this.reason = this.$route.query.reason;
    this.overtimeType = this.$route.query.overtimeType;
    this.relinquishTime = this.$route.query.relinquishTime;
    this.editIndex = this.$route.query.editIndex;
    this.department = this.$route.query.department;
    this.plant = this.$route.query.plant;
    this.workshop = this.$route.query.workshop;
    if (this.addOverTimeAll.length > 0) {
      if (this.addOverTimeAll[this.editIndex].operator != undefined) {
        this.employees = this.addOverTimeAll[this.editIndex].operator.split(
          ","
        );
      }
    }
    this.queryChildUser();
  },
};
</script>