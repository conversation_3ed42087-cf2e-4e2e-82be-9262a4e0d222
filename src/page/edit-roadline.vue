<template>
    <div class="editBox">
       <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>考勤设置</el-breadcrumb-item>
            <el-breadcrumb-item :to="{ path: '/bus-management' }">线路管理</el-breadcrumb-item>
            <el-breadcrumb-item>编辑线路</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-button type="primary" @click="addBtn">新建站点</el-button>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableContent">
            <table>
                <thead>
                    <tr>
                        <th>站点名称</th>
                        <th>所属线路</th>
                        <th>所属厂区</th>
                        <th>创建人</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="tableData.length <= 0">
                        <td colspan="6">暂无数据!</td>
                    </tr>
                    <tr v-for="(item,index) in tableData" :key='index' v-else>
                        <td>{{item.name}}</td>
                        <td>{{item.lineName}}</td>
                        <td>{{item.plant}}</td>
                        <td>{{item.createName}}</td>
                        <td>{{item.createdTime}}</td>
                        <td>
                            <el-button type="text" @click="handleEdit(item.id)">编辑</el-button>
                            <el-button type="text" @click="handleDelete(item.id)">删除</el-button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- table end -->

        <!-- 分页 start -->
        <div class="paginationBox">
            <el-pagination v-show="total!=0" background layout="total, prev, pager, next, sizes, jumper" :total="total" :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData" @current-change="initData"></el-pagination>
        </div>
        <!-- 分页 end -->

        <!-- 新建站点dialog start -->
        <el-dialog
            :title="dialogTitle == 0 ? '新建站点' : '编辑站点'"
            :visible.sync="dialogAdd"
            width="60%"
            :close-on-click-modal='false'
            @close="resetForm('ruleAddForm')"
            >
            <el-form :model="ruleAddForm" :rules="rulesAdd" ref="ruleAddForm" label-width="120px" class="demo-ruleForm">
                <el-form-item label="所属厂区：" prop="factory">
                    <el-select v-model="ruleAddForm.factory" placeholder="请选择厂区" disabled style="width:100%;">
                        <el-option
                            v-for="item in factoryOption"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="线路名称：" prop="road">
                    <el-input v-model="ruleAddForm.road" placeholder="请输入线路" disabled></el-input>
                    <!-- <el-select v-model="ruleAddForm.road" placeholder="请选择线路" disabled>
                        <el-option
                            v-for="item in roadOption"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id">
                        </el-option>
                    </el-select> -->
                </el-form-item>
                <el-form-item label="站点名称：" prop="name">
                    <el-input v-model="ruleAddForm.name" placeholder="请输入站点名称"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleAddForm')">确定</el-button>
                    <el-button @click="resetForm('ruleAddForm')">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 新建站点dialog end -->
    </div>
</template>
<script>
export default {
    data(){
        return{
            // 表格
            tableData:[],
            // 分页
            currentPage:1,
            pageSize:10,
            total:0,
            dialogAdd:false,//新建站点dialog
            // 新建站点Form
            ruleAddForm:{
                factory:'',
                road:'',
                name:''
            },
            rulesAdd:{
                // factory: [
                //     { required: true, message: '请选择厂区', trigger: 'change' }
                // ],
                // road: [
                //     { required: true, message: '请选择线路', trigger: 'change' }
                // ],
                name: [
                    { required: true, message: '请输入站点名称', trigger: 'blur' }
                ]
            },
            factoryOption:[],
            // roadOption:[],
            dialogTitle:'',
            lineId:'',//线路id
            factory:'',//所属厂区
            lineName:'',//线路名称
            id:''//站点id
        }
    },
    methods:{
        // 渲染数据 分页
        initData(){
            let data = {
                lineId:this.lineId,
                // name:'',
                offset:this.pageSize,
                rows:this.currentPage,
            }
            this.$http.queryStationPage(data,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    this.tableData = res.data.data;
                    this.total = res.data.count;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // 新建
        addBtn() {
            this.dialogAdd = true;
            this.dialogTitle = 0;
            this.ruleAddForm.factory = this.factory;
            this.ruleAddForm.road = this.lineName;
            this.ruleAddForm.name = '';
        },

        // table编辑
        handleEdit(id) {
            let data = {
                id:id
            }
            this.$http.queryStationDetail(data,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    this.dialogAdd = true;
                    this.dialogTitle = 1;
                    this.ruleAddForm.factory = res.data.plant;
                    this.ruleAddForm.road = res.data.lineName;
                    this.ruleAddForm.name = res.data.name;
                    this.id = id;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // 添加/编辑站点确定
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if(this.dialogTitle == 0){
                        let data = {
                            lineId:this.lineId,
                            name: this.ruleAddForm.name
                        }
                        this.$http.addStation(data,(res)=>{
                            
                            if(res.code == 'SUCCESS'){
                                this.dialogAdd = false;
                                this.$refs[formName].resetFields();
                                this.initData()
                                this.$message.success(res.message);
                            }else{
                                this.$message.error(res.message);
                            }
                        },(errRes)=>{
                            
                            this.$message.error(errRes.message);
                        })
                    }else{
                        let data = {
                            lineId:this.lineId,
                            id:this.id,
                            name: this.ruleAddForm.name
                        }
                        this.$http.updateStation(data,(res)=>{
                            
                            if(res.code == 'SUCCESS'){
                                this.dialogAdd = false;
                                this.$refs[formName].resetFields();
                                this.initData()
                                this.$message.success(res.message);
                            }else{
                                this.$message.error(res.message);
                            }
                        },(errRes)=>{
                            
                            this.$message.error(errRes.message);
                        })
                    }
                } else {
                    return false;
                }
            });
        },

        // 取消
        resetForm(formName){
            this.$refs[formName].resetFields();
            this.dialogAdd = false;
        },

        // table删除
        handleDelete(id) {
            this.$confirm('确认删除？','提示',{
                type: 'warning',
            }).then(() => {
                let data = {
                    id:id
                }
                this.$http.deleteStation(data,(res)=>{
                    
                    if(res.code == 'SUCCESS'){
                        this.initData();
                        this.$message.success(res.message);
                    }else{
                        this.$message.error(res.message);
                    }
                },(errRes)=>{
                    
                    this.$message.error(errRes.message);
                })
            }).catch(() => {

            });
        },

        // 厂区
        factoryList(){
            this.$http.queryPlantList(null,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    this.factoryOption = res.data;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        }
    },
    mounted(){
        this.lineId = this.$route.query.id;
        this.factory = this.$route.query.factory;
        this.lineName = this.$route.query.lineName;
        this.initData();
        this.factoryList();
    }
}
</script>
