<template>
    <div class="reportBox">
       <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>报告数据查询</el-breadcrumb-item>
            <el-breadcrumb-item>考勤异常报告</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
                <el-form-item label="日期：" prop="dataTime">
                    <el-date-picker
                        v-model="formInline.dataTime"
                        type="daterange"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd"
                        range-separator="~"
                        @change="dataChange"
                        :clearable = false>
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="周：" class="weekday">
                    <el-form-item prop="startWeek">
                        <el-date-picker
                            v-model="formInline.startWeek"
                            type="week"
                            format="yyyy . WK WW"
                            value-format="yyyy-MM-dd"
                            :picker-options="pickerOptionsStart"
                            placeholder="开始周"
                            @change="startChange"
                            :clearable = false>
                        </el-date-picker>
                    </el-form-item>
                    <span>~</span>
                    <el-form-item prop="endWeek" class="endWeek">
                        <el-date-picker
                            v-model="formInline.endWeek"
                            type="week"
                            format="yyyy . WK WW"
                            value-format="yyyy-MM-dd"
                            :picker-options="pickerOptionsEnd"
                            placeholder="结束周"
                            @change="endChange"
                            :clearable = false>
                        </el-date-picker>
                    </el-form-item>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="initData(currentPage = 1)">查询</el-button>
                    <el-button @click="resetForm('formInline')">重置</el-button>
                    <el-button type="primary" @click="exportBtn">导出</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableContent">
            <table>
                <thead>
                    <tr>
                        <th colspan="5">
                            <span>WK{{startHead}} - WK{{endHead}}</span>
                            <span>{{startData}} - {{endData}}</span>
                        </th>
                    </tr>
                    <tr>
                        <th>workshop</th>
                        <th>异常人数</th>
                        <th>占总人数比例</th>
                        <th>异常数量</th>
                        <th>占总异常数量比例</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="tableData.length <= 0">
                        <td colspan="5">暂无数据!</td>
                    </tr>
                    <tr v-for="(item,index) in tableData" :key='index' v-else>
                        <td>{{item.workshop}}</td>
                        <td>{{item.number}}</td>
                        <td>{{item.numberRatio.toFixed(2)}}%</td>
                        <td>{{item.count}}</td>
                        <td>{{item.countRatio.toFixed(2)}}%</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- table end -->

        <!-- 分页 start -->
        <div class="paginationBox">
            <el-pagination v-show="total!=0" background layout="total, prev, pager, next, sizes, jumper" :total="total" :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData" @current-change="initData"></el-pagination>
        </div>
        <!-- 分页 end -->
    </div>
</template>
<script>
var axios = require('axios')// 引用axios
import utils from '../utils/index.js'//引用常用工具文件
export default {
    data(){
        return{
            // 头部查询
            formInline: {
                dataTime:[],
                startWeek:'',
                endWeek:''
            },
            pickerOptionsStart: { //disabledDate 文档上：设置禁用状态，参数为当前日期，要求返回 Boolean
                disabledDate: time => {
                    let endWeekVal = this.formInline.endWeek;
                    if (endWeekVal) {
                        return time.getTime() > new Date(endWeekVal).getTime();
                    }  
                }
            },
            pickerOptionsEnd: {
                disabledDate: time => { //disabledDate 文档上：设置禁用状态，参数为当前日期，要求返回 Boolean
                    let beginWeekVal = this.formInline.startWeek;
                    if (beginWeekVal) {
                        return time.getTime() < new Date(beginWeekVal).getTime();
                    }
                },
            },
            // 表格
            tableData:[],
            // 分页
            currentPage:1,
            pageSize:10,
            total:0,
            startHead:'',
            endHead:'',
            startData:'',
            endData:'',

            q:'',// 开始
            p:''//结束
        }
    },
    methods:{
        // 重置
        resetForm(formName) {
            this.$refs[formName].resetFields();
            this.currentPage = 1;
            this.initData();
        },
        
        // 渲染数据 分页
        initData(){
            let data = {
                endQueryDate:this.formInline.dataTime[1],
                offset:this.pageSize,
                rows:this.currentPage,
                startQueryDate:this.formInline.dataTime[0],
            }
            this.$http.queryAttendanceReportPage(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.tableData = res.data.data;
                    this.total = res.data.count;
                    if(this.formInline.dataTime.length > 0){
                        this.startData = this.formInline.dataTime[0];
                        this.endData = this.formInline.dataTime[1];
                        var reg = /^(\d{4})-(\d{1,2})-(\d{1,2})$/;
                        var newStart = this.startData.match(reg);
                        var year  = RegExp.$1;
                        var month  = RegExp.$2;
                        var day  = RegExp.$3;
                        var newEnd = this.endData.match(reg);
                        var year1  = RegExp.$1;
                        var month1  = RegExp.$2;
                        var day1  = RegExp.$3;
                        this.startHead = this.getYearWeek(year, month, day)
                        this.endHead = this.getYearWeek(year1, month1, day1)
                    }
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },

        //判断当前日期为当年第几周
        getYearWeek(a, b, c) {
            // 当前日期的年、月、日
            // date1是当前日期
            // date2是当年第一天
            // d是当前日期是今年第多少天
            // 用d + 当前年的第一天的周差距的和在除以7就是本年第几周
            var date1 = new Date(a, parseInt(b) - 1, c), date2 = new Date(a, 0, 1),
            d = Math.round((date1.valueOf() - date2.valueOf()) / 86400000);
            return Math.ceil((d + ((date2.getDay() + 1) - 1)) / 7);
        },

        // 导出
        exportBtn(){
            let data = {
                endQueryDate:this.formInline.dataTime[1],
                offset:this.pageSize,
                rows:this.currentPage,
                startQueryDate:this.formInline.dataTime[0],
            }
            axios({
                url: 'api/admin/attendanceAnalysisController/exportAttendanceReport',
                method: "POST",
                data:data,
                responseType:"blob",
                headers:{
                    // 'Content-Type':'application/json;charset=UTF-8',
                    'Authorization' : localStorage.getItem('Auth-Token')
                },
            }).then(function (res) {
                if(!res){
                    return
                }
                utils.downloadExcel(res.data,'考勤异常报告','xlsx')//调用公共方法
            })
        },

        dataChange(){
            this.formInline.startWeek = this.formInline.dataTime[0];
            this.formInline.endWeek = this.formInline.dataTime[1];

        },
        startChange(){
            this.q = this.formInline.startWeek;
        },
        endChange(){
            this.p = this.formInline.endWeek;
            this.$set(this.formInline,'dataTime',[this.q,this.p])
        }
    },
    mounted(){
        this.initData();
    }
}
</script>
