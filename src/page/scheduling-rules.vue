<template>
	<div class="ruleBox">
		<!-- 面包屑 start -->
		<el-breadcrumb separator="/">
			<el-breadcrumb-item>考勤设置</el-breadcrumb-item>
			<el-breadcrumb-item :to="{ path: '/workforce-management' }">排班管理</el-breadcrumb-item>
			<el-breadcrumb-item>排班规则</el-breadcrumb-item>
		</el-breadcrumb>
		<!-- 面包屑 end -->

		<!-- search start -->
		<div class="searchFrom">
			<el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
				
				<el-form-item label="班组名称：" prop="name">
					<el-select v-model="formInline.name" filterable allow-create placeholder="请输入班组名称">
						<el-option v-for="item in nameOption" :key="item.id" :label="item.name" :value="item.name">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" @click="initData(currentPage = 1)">查询</el-button>
				</el-form-item>
				<el-form-item>
					<el-button  type="primary" @click="addBtn">新建</el-button>
				</el-form-item>
				
			</el-form>
		</div>
		<!-- search end -->

		<!-- table start -->
		<div class="tableContent">
			<table>
				<thead>
					<tr>
						<th>序号</th>
						<!-- <th>plant</th> -->
						<!-- <th>workshop</th> -->
						<th>班组</th>
						<th>起始日期</th>
						<th>排班包含周期数</th>
						<th>周期1</th>
						<th>周期2</th>
						<th>周期3</th>
						<th>周期4</th>
						<th>周期5</th>
						<th>操作</th>
					</tr>
				</thead>
				<tbody>
					<tr v-if="tableData.length <= 0">
						<td colspan="10">暂无数据!</td>
					</tr>
					<tr v-for="(item, index) in tableData" :key='index' v-else>
						<td>{{ index + 1 }}</td>
						<!-- <td>{{item.plant}}</td>
						<td>{{item.workshop}}</td> -->
						<td>{{ item.workTeam }}</td>
						<td>{{ item.startDate }}</td>
						<td>{{ item.cycle }}</td>
						<td>{{ item.workTypeOne }}</td>
						<td>{{ item.workTypeTwo }}</td>
						<td>{{ item.workTypeThree }}</td>
						<td>{{ item.workTypeFour }}</td>
						<td>{{ item.workTypeFive }}</td>
						<td>
							<el-button type="text" @click="handleEdit(item)">编辑</el-button>
							<el-button type="text" @click="handleDelete(item.scheduleRuleId)">删除</el-button>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
		<!-- table end -->

		<!-- 分页 start -->
		<div class="paginationBox">
			<el-pagination v-show="total != 0" background layout="total, prev, pager, next, sizes, jumper" :total="total"
				:current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData"
				@current-change="initData"></el-pagination>
		</div>
		<!-- 分页 end -->

		<!-- 新建dialog start -->
		<el-dialog :title="dialogTitle == 0 ? '新建' : '编辑'" :visible.sync="dialogAdd" width="80%" :close-on-click-modal='false'
			@close="resetForm('ruleAddForm')">
			<el-form :model="ruleAddForm" :rules="rulesAdd" ref="ruleAddForm" label-width="200px" class="demo-ruleForm">
				<el-form-item label="班组：" prop="team">
					<!-- <el-autocomplete
						v-model="ruleAddForm.team"
						placeholder="请选择班组"
						:fetch-suggestions="querySearchT"
						:trigger-on-focus="false"
						@select="handleSelectT"
						></el-autocomplete> -->
					<el-select v-model="ruleAddForm.team" value-key="id" placeholder="请选择班组" @change="handleChange"
						style="width:100%;" :disabled="isForbid" filterable>
						<el-option v-for="item in teamOption" :key="item.id" :label="item.workTeam" :value="item"
							:disabled="item.disabled">
						</el-option>
						<!-- :value="item.workTeam+'$'+item.workshop+'|'+item.plant" -->
					</el-select>
				</el-form-item>
				<el-form-item label="起始日期：" prop="startDate">
					<el-date-picker v-model="ruleAddForm.startDate" type="date" placeholder="请选择起始日期" value-format="yyyy-MM-dd"
						:picker-options="pickerOptionsStart" :clearable=false style="width:100%;">
					</el-date-picker>
				</el-form-item>
				<el-form-item label="排班包含周期数：" prop="cycleWeeks">
					<el-input v-model="ruleAddForm.cycleWeeks" placeholder="请输入排班包含周期数" @input="inputSet($event)"></el-input>
				</el-form-item>

				<div v-if="ruleAddForm.cycleWeeks">
					<el-form-item v-for="(item, index) in ruleAddForm.weekLists" :key='index' :label="'周期 ' + (index + 1) + '：'"
						:prop="'weekLists.' + index + '.shift'" :rules="{
							required: true, message: '请选择班次', trigger: 'change'
						}">
						<!-- <el-autocomplete
						v-model="item.shift"
						placeholder="请选择班次"
						:fetch-suggestions="querySearchS"
						:trigger-on-focus="false"
						@select="handleSelectS"
						style="width:100%;"
						></el-autocomplete> -->
						<el-select v-model="item.shift" filterable placeholder="请选择班次" style="width:100%;">
							<el-option v-for="item in shiftOptions" :key="item" :label="item" :value="item">
							</el-option>
						</el-select>
					</el-form-item>
				</div>

				<el-form-item>
					<el-button type="primary" @click="submitForm('ruleAddForm')">确定</el-button>
					<el-button @click="resetForm('ruleAddForm')">取消</el-button>
				</el-form-item>
			</el-form>
		</el-dialog>
		<!-- 新建dialog end -->
	</div>
</template>
<script>
export default {
	data() {
		// 班组
		var checkTeam = (rule, value, callback) => {
			if (value == '') {
				callback(new Error('请选择班组!'))
			} else {
				callback()
			}
		};
		// 起始日期
		var checkStart = (rule, value, callback) => {
			if (value == '') {
				callback(new Error('请选择起始日期!'))
			} else {
				callback()
			}
		};
		// 排班包含周期数
		var checkWeek = (rule, value, callback) => {
			if (value == '') {
				callback(new Error('请输入排班包含周期数!'))
			} else {
				callback()
			}
		};
		return {
			formInline: {
				name: ''
			},
			nowDate: '',
			pickerOptionsStart: { //disabledDate 文档上：设置禁用状态，参数为当前日期，要求返回 Boolean
				disabledDate: time => {
					let newDate = '';
					// if(this.ruleAddForm.startDate != '' && this.ruleAddForm.startDate != undefined && this.ruleAddForm.startDate != null){
					//     newDate = this.ruleAddForm.startDate;
					// }else{
					newDate = this.nowDate;
					// }
					let dataArr = newDate.split('-');
					let fDate = dataArr[0] + '-' + dataArr[1] + '-15';
					// let lastDate = new Date(dataArr[0],dataArr[1],0);
					// let lastDay = new Date(lastDate.getTime()).getDate();
					// let lDate = dataArr[0] + '-' + dataArr[1] + '-' + lastDay;
					if (fDate) {
						return time.getTime() > new Date(fDate).getTime();
					}
				}
			},
			// 表格
			tableData: [],
			// 分页
			currentPage: 1,
			pageSize: 10,
			total: 0,
			dialogAdd: false,//新建dialog
			// 新建Form
			ruleAddForm: {
				team: '',
				startDate: '',
				cycleWeeks: '',

				weekLists: [],
			},
			rulesAdd: {
				team: [
					{ validator: checkTeam, trigger: 'blur' }
				],
				startDate: [
					{ validator: checkStart, trigger: 'change' }
				],
				cycleWeeks: [
					{ validator: checkWeek, trigger: 'blur' }
				],
			},
			teamOption: [],
			// restaurantsT: [],//班组模糊查询列表
			// restaurantsS: [],//班次模糊查询列表
			shiftOptions: [],
			dialogTitle: '',
			id: '',
			plant: '',
			workshop: '',
			workTeam: '',
			isForbid: false,
			changeArr: [],
			nameOption:[]
		}
	},
	methods: {
		// 渲染数据 分页
		initData() {
			let data = {
				offset: this.pageSize,
				rows: this.currentPage,
				workTeam: this.formInline.name,
			}
			this.$http.queryScheduleRulePage(data, (res) => {

				if (res.code == 'SUCCESS') {
					this.tableData = res.data.data;
					this.total = res.data.count;

					for (let i = 0; i < this.tableData.length; i++) {
						this.changeArr.push(this.tableData[i].workTeam)
					}

					this.teamList();
				} else {
					this.$message.error(res.message);
				}
			}, (errRes) => {

				this.$message.error(errRes.message);
			})
		},

		// 新建
		addBtn() {
			this.dialogAdd = true;
			this.dialogTitle = 0;
			this.isForbid = false;
			this.ruleAddForm = {
				team: '',
				// startDate:'',
				cycleWeeks: '',

				weekLists: [],
			}
		},

		// table编辑
		handleEdit(item) {
			this.dialogAdd = true;
			this.dialogTitle = 1;
			this.isForbid = true;
			this.ruleAddForm.weekLists = [];
			this.ruleAddForm.team = item.workTeam;
			this.ruleAddForm.startDate = item.startDate;
			this.ruleAddForm.cycleWeeks = item.cycle;
			for (let i = 0; i < item.cycle; i++) {
				this.ruleAddForm.weekLists.push({ shift: '' })
			}
			if (item.workTypeOne != null) {
				this.ruleAddForm.weekLists[0].shift = item.workTypeOne;
			}
			if (item.workTypeTwo != null) {
				this.ruleAddForm.weekLists[1].shift = item.workTypeTwo;
			}
			if (item.workTypeThree != null) {
				this.ruleAddForm.weekLists[2].shift = item.workTypeThree;
			}
			if (item.workTypeFour != null) {
				this.ruleAddForm.weekLists[3].shift = item.workTypeFour;
			}
			if (item.workTypeFive != null) {
				this.ruleAddForm.weekLists[4].shift = item.workTypeFive;
			}

			this.id = item.scheduleRuleId;
			this.plant = item.plant;
			this.workshop = item.workshop;
			this.workTeam = item.workTeam;
			this.shiftList();
		},

		// 添加/编辑确定
		submitForm(formName) {
			this.$refs[formName].validate((valid) => {
				if (valid) {
					if (this.dialogTitle == 0) {
						let data = {
							cycle: this.ruleAddForm.cycleWeeks,
							plant: this.plant,
							startDate: this.ruleAddForm.startDate,
							workTeam: this.workTeam,
							workTypeFive: this.ruleAddForm.weekLists[4] != undefined ? this.ruleAddForm.weekLists[4].shift : '',
							workTypeFour: this.ruleAddForm.weekLists[3] != undefined ? this.ruleAddForm.weekLists[3].shift : '',
							workTypeOne: this.ruleAddForm.weekLists[0] != undefined ? this.ruleAddForm.weekLists[0].shift : '',
							workTypeThree: this.ruleAddForm.weekLists[2] != undefined ? this.ruleAddForm.weekLists[2].shift : '',
							workTypeTwo: this.ruleAddForm.weekLists[1] != undefined ? this.ruleAddForm.weekLists[1].shift : '',
							workshop: this.workshop
						}
						this.$http.addScheduleRule(data, (res) => {

							if (res.code == 'SUCCESS') {
								this.dialogAdd = false;
								this.$refs[formName].resetFields();
								this.initData()
								this.$message.success(res.message);
							} else {
								this.$message.error(res.message);
							}
						}, (errRes) => {

							this.$message.error(errRes.message);
						})
					} else {
						let data = {
							scheduleRuleId: this.id,
							cycle: this.ruleAddForm.cycleWeeks,
							plant: this.plant,
							startDate: this.ruleAddForm.startDate,
							workTeam: this.workTeam,
							workTypeFive: this.ruleAddForm.weekLists[4] != undefined ? this.ruleAddForm.weekLists[4].shift : '',
							workTypeFour: this.ruleAddForm.weekLists[3] != undefined ? this.ruleAddForm.weekLists[3].shift : '',
							workTypeOne: this.ruleAddForm.weekLists[0] != undefined ? this.ruleAddForm.weekLists[0].shift : '',
							workTypeThree: this.ruleAddForm.weekLists[2] != undefined ? this.ruleAddForm.weekLists[2].shift : '',
							workTypeTwo: this.ruleAddForm.weekLists[1] != undefined ? this.ruleAddForm.weekLists[1].shift : '',
							workshop: this.workshop
						}
						this.$http.updateScheduleRule(data, (res) => {

							if (res.code == 'SUCCESS') {
								this.dialogAdd = false;
								this.$refs[formName].resetFields();
								this.initData()
								this.$message.success(res.message);
							} else {
								this.$message.error(res.message);
							}
						}, (errRes) => {

							this.$message.error(errRes.message);
						})
					}
				} else {
					return false;
				}
			});
		},

		// 取消
		resetForm(formName) {
			this.$refs[formName].resetFields();
			this.dialogAdd = false;
		},

		// table删除
		handleDelete(id) {
			this.$confirm('确认删除？', '提示', {
				type: 'warning',
			}).then(() => {
				let data = {
					scheduleRuleId: id
				}
				this.$http.deleteScheduleRule(data, (res) => {

					if (res.code == 'SUCCESS') {
						this.initData();
						this.$message.success(res.message);
					} else {
						this.$message.error(res.message);
					}
				}, (errRes) => {

					this.$message.error(errRes.message);
				})
			}).catch(() => {

			});
		},

		// 班组 下拉选
		teamList() {
			this.$http.queryWorkGroupAll(null, (res) => {
				if (res.code == 'SUCCESS') {
					// this.restaurantsT = res.data.data;  
					this.teamOption = res.data.data;
					this.nameOption = res.data.data;
					for (let i = 0; i < this.teamOption.length; i++) {
						this.teamOption[i]["disabled"] = false;
						for (let j = 0; j < this.changeArr.length; j++) {
							if (this.changeArr[j] == this.teamOption[i].workTeam) {
								this.teamOption[i].disabled = true;
							}
						}
					}
				} else {
					this.$message.error(res.message);
				}
			}, (errRes) => {

				this.$message.error(errRes.message);
			})
		},

		// // 班组
		// // 返回输入建议的方法，仅当你的输入建议数据 resolve 时，通过调用 callback(data:[]) 来返回它
		// querySearchT(queryString, cb) {
		//     var restaurantsT = this.restaurantsT;
		//     // 因为autocomplete只识别value字段并在下拉列中显示，添加value字段
		//     for(let i=0; i<restaurantsT.length; i++){
		//         restaurantsT[i]["value"] = restaurantsT[i].workTeam;
		//     }
		//     var results = queryString ? restaurantsT.filter(this.createFilter(queryString)) : restaurantsT;
		//     // 调用 callback 返回建议列表的数据
		//     cb(results);
		// },

		// // 在 Input 值改变时触发
		// handleSelectT(item) {
		//     console.log(item)
		//     this.plant = item.plant;
		//     this.workshop = item.workshop;
		// },

		// 班次 下拉选
		shiftList() {
			let data = {
				plant: this.plant,
				workshop: this.workshop,
				name: this.workTeam
			}
			this.$http.queryWorkGroupDetailByName(data, (res) => {

				if (res.code == 'SUCCESS') {
					this.shiftOptions = res.data.workTypeNameArray;
				} else {
					this.$message.error(res.message);
				}
			}, (errRes) => {

				this.$message.error(errRes.message);
			})
		},

		// // 班次
		// // 返回输入建议的方法，仅当你的输入建议数据 resolve 时，通过调用 callback(data:[]) 来返回它
		// querySearchS(queryString, cb) {
		//     var restaurantsS = this.restaurantsS;
		//     // 因为autocomplete只识别value字段并在下拉列中显示，添加value字段
		//     for(let i=0; i<restaurantsS.length; i++){
		//         restaurantsS[i]["value"] = restaurantsS[i].name;
		//     }
		//     var results = queryString ? restaurantsS.filter(this.createFilter(queryString)) : restaurantsS;
		//     // 调用 callback 返回建议列表的数据
		//     cb(results);
		// },
		// createFilter(queryString) {
		//     return (restaurant) => {
		//         return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
		//     };
		// },
		// // 在 Input 值改变时触发
		// handleSelectS(item) {
		//     console.log(item);
		// },

		// 周期设置
		inputSet(e) {
			this.ruleAddForm.weekLists = [];
			let boolean = new RegExp("^[1-9][0-9]*$").test(e);
			if (!boolean) {
				this.$message.warning("请输入数字值");
				this.ruleAddForm.cycleWeeks = '';
			} else {
				if (e > 5) {
					this.$message.warning("周期不得超过5");
					this.ruleAddForm.cycleWeeks = '';
				} else {
					for (let i = 0; i < e; i++) {
						this.ruleAddForm.weekLists.push({ shift: '' })
					}
				}
			}
		},

		// 班组切换
		handleChange(value) {
			this.workTeam = value.workTeam;
			this.workshop = value.workshop;
			this.plant = value.plant;
			// var valOne = value.split("$")[1];
			// this.workTeam = value.split("$")[0];
			// this.workshop = valOne.split("|")[0];
			// this.plant = valOne.split("|")[1];
			this.shiftList();
		},
	},
	mounted() {
		this.initData();

		// this.shiftList();
		var date = new Date();
		var year = date.getFullYear();
		var month = date.getMonth() + 1;
		var day = date.getDate();
		this.nowDate = year + '-' + month + '-' + day;
	}
}
</script>
