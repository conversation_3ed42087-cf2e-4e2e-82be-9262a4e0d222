<template>
    <div class="machineBox">
        <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>考勤机管理</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
                <el-form-item label="考勤机名称：" prop="name">
                    <el-input v-model="formInline.name" placeholder="请输入考勤机名称"></el-input>
                </el-form-item>
                <el-form-item label="MAC地址：" prop="address">
                    <el-input v-model="formInline.address" placeholder="请输入MAC地址"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="initData(currentPage = 1)">查询</el-button>
                    <el-button type="primary" @click="addBtn">新建</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableContent">
            <table>
                <thead>
                    <tr>
                        <th>考勤机名称</th>
                        <th>厂区</th>
                        <th>考勤机位置</th>
                        <th>MAC地址</th>
                        <th>IP地址</th>
                        <th>序列号</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="tableData.length <= 0">
                        <td colspan="6">暂无数据!</td>
                    </tr>
                    <tr v-for="(item,index) in tableData" :key='index' v-else>
                        <td>{{item.machineName}}</td>
                        <td>{{item.plant}}</td>
                        <td>{{item.machineLocation}}</td>
                        <td>{{item.machineMac}}</td>
                        <td>{{item.machineIp}}</td>
                        <td>{{item.machineSn}}</td>
                        <td>
                            <el-button type="text" @click="handleEdit(item)">编辑</el-button>
                            <el-button type="text" @click="handleDelete(item.machineId)">删除</el-button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- table end -->

        <!-- 分页 start -->
        <div class="paginationBox">
            <el-pagination v-show="total!=0" background layout="total, prev, pager, next, sizes, jumper" :total="total" :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData" @current-change="initData"></el-pagination>
        </div>
        <!-- 分页 end -->

        <!-- 新建考勤机dialog start -->
        <el-dialog
            :title="dialogTitle == 0 ? '新建考勤机' : '编辑考勤机'"
            :visible.sync="dialogAdd"
            width="60%"
            :close-on-click-modal='false'
            @close="resetForm('ruleAddForm')"
            v-loading="loading"
            >
            <el-form :model="ruleAddForm" :rules="rulesAdd" ref="ruleAddForm" label-width="120px" class="demo-ruleForm">
                <el-form-item label="考勤机名称：" prop="name">
                    <el-input v-model="ruleAddForm.name" placeholder="请输入考勤机名称"></el-input>
                </el-form-item>
                <el-form-item label="厂区：" prop="factory">
                    <el-select v-model="ruleAddForm.factory" placeholder="请选择厂区" style="width:100%;">
                        <el-option
                            v-for="item in factoryOption"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="考勤机位置：" prop="position">
                    <el-input v-model="ruleAddForm.position" placeholder="请输入考勤机位置"></el-input>
                </el-form-item>
                <el-form-item label="MAC地址：" prop="address">
                    <el-input v-model="ruleAddForm.address" placeholder="请输入MAC地址"></el-input>
                </el-form-item>
                <el-form-item label="设备IP地址：" prop="machineIp">
                    <el-input v-model="ruleAddForm.machineIp" placeholder="请输入设备IP地址"></el-input>
                </el-form-item>
                <el-form-item label="设备序列号：" prop="machineSn">
                    <el-input v-model="ruleAddForm.machineSn" placeholder="请输入设备序列号"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleAddForm')">确定</el-button>
                    <el-button @click="resetForm('ruleAddForm')">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 新建考勤机dialog end -->
    </div>
</template>
<script>
export default {
    data(){
        // 考勤机名称验证
        var checkName = (rule,value,callback) => {
            if(value == ''){
                callback(new Error('请输入考勤机名称!'))
            }else{
                callback()
            }
        };
        // 厂区验证
        var checkFactory = (rule,value,callback) => {
            if(value == ''){
                callback(new Error('请选择厂区!'))
            }else{
                callback()
            }
        };
        // 考勤机位置验证
        var checkPosition = (rule,value,callback) => {
            if(value == ''){
                callback(new Error('请输入考勤机位置!'))
            }else{
                callback()
            }
        };
        // // MAC地址验证
        // var checkAddress = (rule,value,callback) => {
        //     if(value == ''){
        //         callback(new Error('请输入MAC地址!'))
        //     }else{
        //         callback()
        //     }
        // };
        // // 设备IP地址验证
        // var checkIp = (rule,value,callback) => {
        //     if(value == ''){
        //         callback(new Error('请输入设备IP地址!'))
        //     }else{
        //         callback()
        //     }
        // };
        // 设备序列号验证
        var checkSn = (rule,value,callback) => {
            if(value == ''){
                callback(new Error('请输入设备序列号!'))
            }else{
                callback()
            }
        };
        return{
            // 头部查询
            formInline: {
                name:'',
                address:''
            },
            // 表格
            tableData:[],
            // 分页
            currentPage:1,
            pageSize:10,
            total:0,
            dialogAdd:false,//新建考勤机dialog
            // 新建考勤机Form
            ruleAddForm:{
                name:'',
                factory:'',
                position:'',
                address:'',
                machineIp:'',
                machineSn:''
            },
            rulesAdd:{
                name:[
                    { validator: checkName, trigger: 'blur' }
                ],
                factory:[
                    { validator: checkFactory, trigger: 'change' }
                ],
                position:[
                    { validator: checkPosition, trigger: 'blur' }
                ],
                // address:[
                //     { validator: checkAddress, trigger: 'blur' }
                // ],
                // machineIp:[
                //     { validator: checkIp, trigger: 'blur' }
                // ],
                machineSn:[
                    { validator: checkSn, trigger: 'blur' }
                ]
            },
            dialogTitle:'',
            machineId:'',//考勤机id
            factoryOption:[],
            loading: false,
        }
    },
    methods:{
        // 渲染数据 分页
        initData(){
            let data = {
                machineMac: this.formInline.address,
                machineName: this.formInline.name,
                offset:this.pageSize,
                rows:this.currentPage,
            }
            this.$http.queryMachinePage(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.tableData = res.data.data;
                    this.total = res.data.count;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },

        // 新建
        addBtn() {
            this.dialogAdd = true;
            this.dialogTitle = 0;
            this.ruleAddForm = {
                name:'',
                factory:'',
                position:'',
                address:'',
                machineIp:'',
                machineSn:''
            }
        },

        // table编辑
        handleEdit(item) {
            this.dialogAdd = true;
            this.dialogTitle = 1;
            this.ruleAddForm.name = item.machineName;
            this.ruleAddForm.factory = item.plant;
            this.ruleAddForm.position = item.machineLocation;
            this.ruleAddForm.address = item.machineMac;
            this.ruleAddForm.machineIp = item.machineIp;
            this.ruleAddForm.machineSn = item.machineSn;
            this.machineId = item.machineId;
        },

        // 添加/编辑考勤机确定
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.loading = true;
                    if(this.dialogTitle == 0){
                        let data = {
                            machineIp: this.ruleAddForm.machineIp,
                            plant:this.ruleAddForm.factory,
                            machineLocation: this.ruleAddForm.position,
                            machineMac: this.ruleAddForm.address,
                            machineName: this.ruleAddForm.name,
                            machineSn: this.ruleAddForm.machineSn
                        }
                        this.$http.addMachine(data,(res)=>{
                            if(res.code == 'SUCCESS'){
                                this.loading = false;
                                this.dialogAdd = false;
                                this.$refs[formName].resetFields();
                                this.initData()
                                this.$message.success(res.message);
                            }else{
                                this.loading = false;
                                this.$message.error(res.message);
                            }
                        },(errRes)=>{
                            this.loading = false;
                            this.$message.error(errRes.message);
                        })
                    }else{
                        let data = {
                            machineId: this.machineId,
                            machineLocation: this.ruleAddForm.position,
                            plant:this.ruleAddForm.factory,
                            machineMac: this.ruleAddForm.address,
                            machineName: this.ruleAddForm.name,
                            machineIp: this.ruleAddForm.machineIp,
                            machineSn: this.ruleAddForm.machineSn
                        }
                        this.$http.updateMachine(data,(res)=>{
                            if(res.code == 'SUCCESS'){
                                this.loading = false;
                                this.dialogAdd = false;
                                this.$refs[formName].resetFields();
                                this.initData()
                                this.$message.success(res.message);
                            }else{
                                this.loading = false;
                                this.$message.error(res.message);
                            }
                        },(errRes)=>{
                            this.loading = false;
                            this.$message.error(errRes.message);
                        })
                    }
                } else {
                    return false;
                }
            });
        },

        // 取消
        resetForm(formName){
            this.$refs[formName].resetFields();
            this.dialogAdd = false;
        },

        // table删除
        handleDelete(id) {
            this.$confirm('确认删除？','提示',{
                type: 'warning',
            }).then(() => {
                let data = {
                    machineId:id
                }
                this.$http.deleteMachine(data,(res)=>{
                    
                    if(res.code == 'SUCCESS'){
                        this.initData();
                        this.$message.success(res.message);
                    }else{
                        this.$message.error(res.message);
                    }
                },(errRes)=>{
                    
                    this.$message.error(errRes.message);
                })
            }).catch(() => {

            });
        },

        // 厂区
        factoryList(){
            this.$http.queryPlantList(null,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    this.factoryOption = res.data;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        }
    },
    mounted(){
        this.initData();
        this.factoryList();
    }
}
</script>
