<template>
    <div class="overTypeBox">
       <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>考勤设置</el-breadcrumb-item>
            <el-breadcrumb-item>加班类别管理</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- table start -->
        <div class="tableContent">
            <table>
                <thead>
                    <tr>
                        <th>加班类别名称</th>
                        <th>工资倍数</th>
                        <!-- <th>创建人</th>
                        <th>创建时间</th> -->
                        <!-- <th>操作</th> -->
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="tableData.length <= 0">
                        <td colspan="2">暂无数据!</td>
                    </tr>
                    <tr v-for="(item,index) in tableData" :key='index' v-else>
                        <td>{{item.overtimeTypeName}}</td>
                        <td>{{item.multiple}}</td>
                        <!-- <td>{{item.creatUser}}</td>
                        <td>{{item.creatTime}}</td> -->
                        <!-- <td> -->
                            <!-- <el-button type="text" @click="handleEdit()">编辑</el-button> -->
                        <!-- </td> -->
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- table end -->

        <!-- 编辑加班类别dialog start -->
        <el-dialog
            title="编辑加班类别"
            :visible.sync="dialogEdit"
            width="60%"
            :close-on-click-modal='false'
            @close="resetForm('ruleForm')"
            >
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px" class="demo-ruleForm">
                <el-form-item label="加班类别名称：">
                    {{ruleForm.nameType}}
                </el-form-item>
                <el-form-item label="工资倍数：" prop="wage">
                    <el-input v-model="ruleForm.wage" placeholder="请输入工资倍数"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleForm')">确定</el-button>
                    <el-button @click="resetForm('ruleForm')">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 编辑加班类别dialog end -->
    </div>
</template>
<script>
export default {
    data(){
        // 工资倍数验证
        var checkWage = (rule,value,callback) => {
            if(value == ''){
                callback(new Error('请输入工资倍数!'))
            }else{
                callback()
            }
        };
        return{
            // 表格
            tableData:[],
            dialogEdit:false,//编辑加班类别dialog
            // 编辑加班类别Form
            ruleForm:{
                nameType:'工作日',
                wage:''
            },
            rules:{
                wage:[
                    { validator: checkWage, trigger: 'blur' }
                ],
            }
        }
    },
    methods:{
        // 渲染数据
        initData(){
            this.$http.queryOvertimeTypeAll(null,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    this.tableData = res.data;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // table编辑
        handleEdit() {
            this.dialogEdit = true;
        },

        // 编辑加班类别确定
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    
                } else {
                    return false;
                }
            });
        },

        // 取消
        resetForm(formName){
            this.$refs[formName].resetFields();
            this.dialogEdit = false;
        },
    },
    mounted(){
        this.initData();
    }
}
</script>
