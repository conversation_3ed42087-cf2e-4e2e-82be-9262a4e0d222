<template>
    <div class="notificationmailBox">
       <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>考勤设置</el-breadcrumb-item>
            <el-breadcrumb-item>通知邮箱管理</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-button type="primary" @click="addBtn">新建</el-button>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableContent">
            <table>
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>邮箱</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="tableData.length <= 0">
                        <td colspan="3">暂无数据!</td>
                    </tr>
                    <tr v-for="(item,index) in tableData" :key='index' v-else>
                        <td>{{index+1}}</td>
                        <td>{{item.address}}</td>
                        <td>
                            <el-button type="text" @click="handleEdit(item)">编辑</el-button>
                            <el-button type="text" @click="handleDelete(item.emailId)">删除</el-button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- table end -->

        <!-- 分页 start -->
        <div class="paginationBox">
            <el-pagination v-show="total!=0" background layout="total, prev, pager, next, sizes, jumper" :total="total" :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData" @current-change="initData"></el-pagination>
        </div>
        <!-- 分页 end -->

        <!-- 新建dialog start -->
        <el-dialog
            :title="dialogTitle == 0 ? '新建' : '编辑'"
            :visible.sync="dialogAdd"
            width="60%"
            :close-on-click-modal='false'
            @close="resetForm('ruleAddForm')"
            >
            <el-form :model="ruleAddForm" :rules="rulesAdd" ref="ruleAddForm" label-width="120px" class="demo-ruleForm">
                <el-form-item label="邮箱：" prop="email">
                    <el-input v-model="ruleAddForm.email" placeholder="请输入邮箱" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleAddForm')">确定</el-button>
                    <el-button @click="resetForm('ruleAddForm')">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 新建dialog end -->
    </div>
</template>
<script>
export default {
    data(){
        return{
            // 表格
            tableData:[],
            // 分页
            currentPage:1,
            pageSize:10,
            total:0,
            dialogAdd:false,//新建班次dialog
            // 新建班次Form
            ruleAddForm:{
                email:''
            },
            rulesAdd:{
                email:[
                    { required: true, message: '请输入邮箱！', trigger: 'blur' },
                    { type: 'email', message: '请输入正确的邮箱地址！', trigger: 'blur' }
                ]
            },
            dialogTitle:'',
            id:''
        }
    },
    methods:{
        // 渲染数据 分页
        initData(){
            let data = {
                offset:this.pageSize,
                rows:this.currentPage,
            }
            this.$http.queryEmailPage(data,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    this.tableData = res.data.data;
                    this.total = res.data.count;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // 新建
        addBtn() {
            this.dialogAdd = true;
            this.dialogTitle = 0;
            this.ruleAddForm = {
                email:''
            }
        },

        // table编辑
        handleEdit(item) {
            this.dialogAdd = true;
            this.dialogTitle = 1;
            this.ruleAddForm.email = item.address;
            this.id = item.emailId;
        },

        // 添加/编辑班次确定
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if(this.dialogTitle == 0){
                        let data = {
                            address: this.ruleAddForm.email
                        }
                        this.$http.addEmail(data,(res)=>{
                            
                            if(res.code == 'SUCCESS'){
                                this.dialogAdd = false;
                                this.$refs[formName].resetFields();
                                this.initData()
                                this.$message.success(res.message);
                            }else{
                                this.$message.error(res.message);
                            }
                        },(errRes)=>{
                            
                            this.$message.error(errRes.message);
                        })
                    }else{
                        let data = {
                            emailId: this.id,
                            address: this.ruleAddForm.email
                        }
                        this.$http.updateEmail(data,(res)=>{
                            
                            if(res.code == 'SUCCESS'){
                                this.dialogAdd = false;
                                this.$refs[formName].resetFields();
                                this.initData()
                                this.$message.success(res.message);
                            }else{
                                this.$message.error(res.message);
                            }
                        },(errRes)=>{
                            
                            this.$message.error(errRes.message);
                        })
                    }
                } else {
                    return false;
                }
            });
        },

        // 取消
        resetForm(formName){
            this.$refs[formName].resetFields();
            this.dialogAdd = false;
        },

        // table删除
        handleDelete(id) {
            this.$confirm('确认删除？','提示',{
                type: 'warning',
            }).then(() => {
                let data = {
                    emailId:id
                }
                this.$http.deleteEmail(data,(res)=>{
                    
                    if(res.code == 'SUCCESS'){
                        this.initData();
                        this.$message.success(res.message);
                    }else{
                        this.$message.error(res.message);
                    }
                },(errRes)=>{
                    
                    this.$message.error(errRes.message);
                })
            }).catch(() => {

            });
        },
    },
    mounted(){
        this.initData();
    }
}
</script>
