<template>
    <div class="directBox">
       <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>报告数据查询</el-breadcrumb-item>
            <el-breadcrumb-item>直接人力工时报告</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
                <el-form-item label="时间：" prop="searchMonth">
                    <el-date-picker
                        v-model="formInline.searchMonth"
                        type="month"
                        placeholder="选择月份"
                        value-format="yyyy-MM"
                        :clearable = false
                        >
                    </el-date-picker>
                    <!-- <el-form-item prop="startYear">
                        <el-date-picker
                            v-model="formInline.startYear"
                            type="year"
                            :picker-options="pickerOptionsStart"
                            placeholder="开始年份"
                            :clearable = false>
                        </el-date-picker>
                    </el-form-item>
                    <span>~</span>
                    <el-form-item prop="endYear" class="endWeek">
                        <el-date-picker
                            v-model="formInline.endYear"
                            type="year"
                            :picker-options="pickerOptionsEnd"
                            placeholder="结束年份"
                            :clearable = false>
                        </el-date-picker>
                    </el-form-item> -->
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="initData">查询</el-button>
                    <el-button type="primary" @click="importBtn">导入</el-button>
                    <el-button type="primary" @click="exportBtn">导出</el-button>
                    <!-- <el-button type="primary" @click="dataBtn">生成数据</el-button> -->
                </el-form-item>
            </el-form>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableContent">
            <table>
                <thead>
                    <tr>
                        <th>厂区</th>
                        <th>车间</th>
                        <th>working hours</th>
                        <th>线外工时</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="tableData.length <= 0">
                        <td colspan="4">暂无数据!</td>
                    </tr>
                    <template v-for="(item,index) in tableData" v-else>
                        <tr :key="index">
                            <td :rowspan="item.children.length">{{item.plant}}</td>
                            <td >{{item.children[0].workshop}}</td>
                            <td >{{item.children[0].workingHours}}</td>
                            <td >{{item.children[0].otherHours}}</td>
                        </tr>
                        <tr v-for="(ele,inx) in item.children.length-1" :key="index+'-'+inx">
                            <td>{{item.children[ele].workshop}}</td>
                            <td>{{item.children[ele].workingHours}}</td>
                            <td>{{item.children[ele].otherHours}}</td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
        <!-- table end -->

        <!-- 分页 start -->
        <!-- <div class="paginationBox">
            <el-pagination v-show="total!=0" background layout="total, prev, pager, next, sizes, jumper" :total="total" :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData" @current-change="initData"></el-pagination>
        </div> -->
        <!-- 分页 end -->

        <!-- 导入dialog start -->
        <el-dialog
            title="导入"
            :visible.sync="dialogImport"
            width="60%"
            :close-on-click-modal='false'
            @close="cancelUpload()"
            >
            <el-form label-width="150px" class="demo-ruleForm">
                <el-form-item label="财务月：" prop="dialogMonth">
                    <el-date-picker
                        v-model="dialogMonth"
                        type="month"
                        placeholder="选择月份"
                        value-format="yyyy-MM"
                        :clearable = false
                        @change="queryMonth"
                        >
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="数据上传：">
                    <el-upload
                        class="upload-demo"
                        ref="uploadExcel"
                        action="/api/admin/reportHcController/importHCDetail"
                        :headers="header"
                        :data="{financeMonth:dialogMonth}"
                        :limit="limitNum"
                        accept=".XLS,.xlsx,.xls"
                        :file-list="fileList"
                        :before-upload="beforeFileUpload"
                        :on-exceed="exceedFile"
                        :on-success="handleFileSuccess"
                        :auto-upload="false"
                        :on-change="handleChange">
                        <el-button slot="trigger" type="text">上传文件</el-button>
                    </el-upload>
                    <div class="tip">当前待上传的数据是财务月{{dialogMonth}}（{{startDate}}~{{endDate}}）,请确认上传数据的时间日期</div>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitUpload()">确定</el-button>
                    <el-button @click="cancelUpload()">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 导入dialog end -->
    </div>
</template>
<script>
var axios = require('axios')// 引用axios
import utils from '../utils/index.js'//引用常用工具文件
export default {
    data(){
        return{
            // 头部查询
            formInline: {
                searchMonth:'',
                // startYear:'',
                // endYear:''
            },
            // pickerOptionsStart: { //disabledDate 文档上：设置禁用状态，参数为当前日期，要求返回 Boolean
            //     disabledDate: time => {
            //         let endYearVal = this.formInline.endYear;
            //         if (endYearVal) {
            //             return time.getTime() > new Date(endYearVal).getTime();
            //         }
            //     }
            // },
            // pickerOptionsEnd: {
            //     disabledDate: time => { //disabledDate 文档上：设置禁用状态，参数为当前日期，要求返回 Boolean
            //         let beginYearVal = this.formInline.startYear;
            //         if (beginYearVal) {
            //             return time.getTime() < new Date(beginYearVal).getTime();
            //         }
            //     },
            // },
            tableData:[],
            // 分页
            // currentPage:1,
            // pageSize:10,
            // total:100,
            dialogMonth:'——',
            startDate:'开始日期',
            endDate:'结束日期',
            // 导入dialog
            dialogImport:false,
            limitNum: 1,
            fileList: [],
            header:{
                'Authorization' : localStorage.getItem('Auth-Token')
            },
        }
    },
    methods:{
        // 渲染数据
        initData(){
            let data = {
                yearMonth:this.formInline.searchMonth
            }
            this.$http.queryReportHCPage(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    var resData= res.data.data;
                    var tempArr = [];
                    var nowArr = [];
                    for (var i = 0; i < resData.length; i++) {
                        if (tempArr.indexOf(resData[i].plant) === -1) {
                            nowArr.push({
                                plant: resData[i].plant,
                                children: [resData[i]]
                            });
                            tempArr.push(resData[i].plant);
                        } else {
                            for (var j = 0; j < nowArr.length; j++) {
                                if (nowArr[j].plant == resData[i].plant) {
                                    nowArr[j].children.push(resData[i]);
                                    break;
                                }
                            }
                        }
                    }
                    this.tableData = nowArr;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },

        // 导出
        exportBtn(){
            let data = {
                yearMonth:this.formInline.searchMonth
            }
            axios({
                url: 'api/admin/reportHcController/downloadReportHCData',
                method: "POST",
                data:data,
                responseType:"blob",
                headers:{
                    // 'Content-Type':'application/json;charset=UTF-8',
                    'Authorization' : localStorage.getItem('Auth-Token')
                },
            }).then(function (res) {
                if(!res){
                    return
                }
                utils.downloadExcel(res.data,'直接人力工时报告 ','xlsx')//调用公共方法
            })
        },

        // // 生成数据
        // dataBtn(){
        //     this.$router.push({ path:'/generate-data'})
        // }

        // 根据年月查询财务月数
        queryMonth(){
            let data = {
                year:this.dialogMonth
            }
            this.$http.queryFinanceDateDetail(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.startDate = res.data.data.startDate;
                    this.endDate = res.data.data.endDate;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },

        // 导入
        importBtn(){
            this.dialogImport = true;
        },
        // 上传文件之前的钩子，参数为上传的文件,若返回 false 或者返回 Promise 且被 reject，则停止上传
        beforeFileUpload (file){
            const extension = file.name.split('.')[1];
            if ((extension !== 'XLS') && (extension !== 'xlsx') && (extension !== 'xls')) {
                this.$message.warning('上传模板只能是 XLS、xlsx、xls格式!')
                return false
            }else{

            }
        },
        // 文件超出个数限制时的钩子
        exceedFile(files, fileList) {
            this.$message.warning({
                title: '警告',
                message: `只能选择 ${this.limitNum} 个文件，当前共选择了 ${files.length + fileList.length} 个`
            });
        },
        // 上传成功后的钩子
        handleFileSuccess(res, file) {
            if(res.code == 'SUCCESS'){
                this.dialogImport = false;//关闭弹框
                this.initData();
                this.$message.success(res.message);  
            }else{
                this.$message.error(res.message);
            }
        },
        // 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用
        handleChange(file, fileList) {
            this.fileList = fileList;
        },

        // 确定
        submitUpload(){
            if(this.fileList == ''){
                this.$message.error("请上传文件");
            }else{
                this.$refs.uploadExcel.submit();
            }
        },
        // 取消
        cancelUpload(){
            this.dialogMonth = '——',
            this.startDate = '开始日期',
            this.endDate = '结束日期',
            this.fileList = [];
            this.dialogImport = false;
        }
    },
    mounted(){
        var datD = new Date(); //当前格林时间 
        var nianD = datD.getFullYear();//当前年份 
        var yueD = datD.getMonth(); //当前月 （需要加1）
        this.formInline.searchMonth = nianD+ '-'+(yueD+1)
        this.dialogMonth = nianD+ '-'+(yueD+1);

        this.initData();
        this.queryMonth();
    }
}
</script>
