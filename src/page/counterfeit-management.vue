<template>
    <div class="counterfeitBox">
       <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>考勤设置</el-breadcrumb-item>
            <el-breadcrumb-item>假别管理</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-button type="primary" @click="addBtn">新建</el-button>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableContent">
            <table>
                <thead>
                    <tr>
                        <th>假别名称</th>
                        <th>最小请假小时数（h）</th>
                        <th>请假说明</th>
                        <th>附件必填</th>
                        <th>创建人</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="tableData.length <= 0">
                        <td colspan="7">暂无数据!</td>
                    </tr>
                    <tr v-for="(item,index) in tableData" :key='index' v-else>
                        <td>{{item.leaveTypeName}}</td>
                        <td>{{item.minHours}}</td>
                        <td>{{item.explains}}</td>
                        <td>{{item.required == '1'?'是':'否'}}</td>
                        <td>{{item.createBy}}</td>
                        <td>{{item.createTime}}</td>
                        <td>
                            <el-button type="text" @click="handleEdit(item.leaveTypeId)">编辑</el-button>
                            <!-- <el-button type="text" @click="handleApproval()">审批流</el-button> -->
                            <el-button type="text" @click="handleDelete(item.leaveTypeId)">删除</el-button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- table end -->

        <!-- 分页 start -->
        <div class="paginationBox">
            <el-pagination v-show="total!=0" background layout="total, prev, pager, next, sizes, jumper" :total="total" :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData" @current-change="initData"></el-pagination>
        </div>
        <!-- 分页 end -->

        <!-- 新建假别dialog start -->
        <el-dialog
            :title="dialogTitle == 0 ? '新建假别' : '编辑假别'"
            :visible.sync="dialogAdd"
            width="60%"
            :close-on-click-modal='false'
            @close="resetForm('ruleAddForm')"
            >
            <el-form :model="ruleAddForm" :rules="rulesAdd" ref="ruleAddForm" label-width="200px" class="demo-ruleForm">
                <el-form-item label="假别名称：" prop="name">
                    <el-input v-model="ruleAddForm.name" placeholder="请输入假别名称"></el-input>
                </el-form-item>
                <el-form-item label="最小请假小时数（h）：" prop="minHour">
                    <el-input v-model="ruleAddForm.minHour" placeholder="请输入最小请假小时数" @blur="blurText($event)" min="0"></el-input>
                </el-form-item>
                <el-form-item label="请假说明：" prop="leaveNote">
                    <el-input type="textarea" v-model="ruleAddForm.leaveNote" placeholder="请输入请假说明"></el-input>
                </el-form-item>
                <el-form-item label="附件必填：" prop="appendix">
                    <el-switch v-model="ruleAddForm.appendix"></el-switch>
                </el-form-item>
                <el-form-item label="正常审批流：" prop="normalFlow" class="normalFlow">
                    <el-checkbox v-model="ruleAddForm.doctorApprove">医生</el-checkbox>->
                    <el-checkbox v-model="ruleAddForm.healthApprove">EHS</el-checkbox>->
                    {{ruleAddForm.one}}->{{ruleAddForm.two}}-><el-checkbox v-model="ruleAddForm.normalFlow">HR</el-checkbox>
                </el-form-item>
                <el-form-item label="特殊审批：" prop="special">
                    <el-switch v-model="ruleAddForm.special"></el-switch>
                </el-form-item>
                <el-form-item>
                    {{ruleAddForm.tip1}}<br/>
                    {{ruleAddForm.tip2}}
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleAddForm')">确定</el-button>
                    <el-button @click="resetForm('ruleAddForm')">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 新建假别dialog end -->

        <!-- 审批流dialog start -->
        <el-dialog
            title="审批流"
            :visible.sync="dialogFlow"
            width="60%"
            :close-on-click-modal='false'
            @close="resetForm('ruleFlowForm')"
            >
            <el-form :model="ruleFlowForm" :rules="rulesFlow" ref="ruleFlowForm" label-width="200px" class="demo-ruleForm">
                <el-form-item label="假别名称：">
                    {{ruleFlowForm.name}}
                </el-form-item>
                <el-form-item label="正常审批流：" prop="normalFlow">
                    {{ruleFlowForm.one}}->{{ruleFlowForm.two}}-><el-checkbox v-model="ruleFlowForm.normalFlow">HR</el-checkbox>
                </el-form-item>
                <el-form-item label="特殊审批：" prop="special">
                    <el-switch v-model="ruleFlowForm.special"></el-switch>
                </el-form-item>
                <el-form-item>
                    {{ruleFlowForm.tip1}}<br/>
                    {{ruleFlowForm.tip2}}
                </el-form-item>
                <el-form-item label="HR：" prop="hr">
                    <el-select v-model="ruleFlowForm.hr" placeholder="请选择HR">
                        <el-option
                            v-for="item in hrOption"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="CEC functional head：" prop="cec">
                    <el-select v-model="ruleFlowForm.cec" placeholder="请选择CEC functional head">
                        <el-option
                            v-for="item in cecOption"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitFlow('ruleFlowForm')">确定</el-button>
                    <el-button @click="resetForm('ruleFlowForm')">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 审批流dialog end -->
    </div>
</template>
<script>
export default {
    data(){
        // 假别名称
        var checkName = (rule,value,callback) => {
            if(value == ''){
                callback(new Error('请输入假别名称!'))
            }else{
                callback()
            }
        };
        // 最小请假小时数
        var checkMinHour = (rule,value,callback) => {
            if(value == ''){
                callback(new Error('请输入最小请假小时数!'))
            }else{
                callback()
            }
        };
        // HR
        var checkHr = (rule,value,callback) => {
            if(value == ''){
                callback(new Error('请选择HR!'))
            }else{
                callback()
            }
        };
        // CEC functional head
        var checkCec = (rule,value,callback) => {
            if(value == ''){
                callback(new Error('请选择CEC functional head!'))
            }else{
                callback()
            }
        };
        return{
            // 表格
            tableData:[],
            // 分页
            currentPage:1,
            pageSize:10,
            total:0,
            dialogAdd:false,//新建假别dialog
            // 新建假别Form
            ruleAddForm:{
                name:'',
                minHour:'',
                leaveNote:'',
                appendix:true,
                one:'一级审批',
                two:'二级审批',
                normalFlow:false,
                special:true,
                tip1:'连续超过3天（含3天）或全年累计超过5天（含5天）',
                tip2:'正常审批流->HR->CEC functional head',
                doctorApprove:false,
                healthApprove:false
            },
            rulesAdd:{
                name:[
                    { validator: checkName, trigger: 'blur' }
                ],
                minHour:[
                    { validator: checkMinHour, trigger: 'blur' }
                ]
            },
            dialogTitle:'',
            // 审批流
            dialogFlow:false,
            ruleFlowForm:{
                name:'事假',
                // one:'一级审批',
                // two:'二级审批',
                // normalFlow:false,
                // special:true,
                // tip1:'连续超过3天（含3天）或全年累计超过5天（含5天）',
                // tip2:'一级审批->二级审批->HR->CEC functional head',
                hr:'',
                cec:''
            },
            hrOption:[
                {
                    id:1,
                    name:'1'
                },
                
            ],
            cecOption:[
                {
                    id:1,
                    name:'1'
                }
            ],
            rulesFlow:{
                hr:[
                    { validator: checkHr, trigger: 'change' }
                ],
                cec:[
                    { validator: checkCec, trigger: 'change' }
                ]
            },
            leaveTypeId:''
        }
    },
    methods:{
        // 渲染数据 分页
        initData(){
            let data = {
                explains:'',
                leaveTypeName:'',
                minHours:'',
                offset:this.pageSize,
                rows:this.currentPage,
            }
            this.$http.queryLeaveTypePage(data,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    this.tableData = res.data.data;
                    this.total = res.data.count;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // 新建
        addBtn() {
            this.dialogAdd = true;
            this.dialogTitle = 0;
            this.ruleAddForm = {
                ...this.ruleAddForm,
                name:'',
                minHour:'',
                leaveNote:'',
                appendix:true,
                one:'一级审批',
                two:'二级审批',
                normalFlow:false,
                special:true,
                doctorApprove:false,
                healthApprove:false
            }
        },

        // table编辑
        handleEdit(id) {
            let data = {
                leaveTypeId:id
            }
            this.$http.queryLeaveTypeDetail(data,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    this.dialogAdd = true;
                    this.dialogTitle = 1;
                    this.ruleAddForm.name = res.data.leaveTypeName;
                    this.ruleAddForm.minHour = res.data.minHours;
                    this.ruleAddForm.leaveNote = res.data.explains;
                    var appendix;
                    if(res.data.required == '0'){
                        appendix = false;
                    }else{
                        appendix = true;
                    }
                    this.ruleAddForm.appendix = appendix;

                    this.ruleAddForm.one = '一级审批';
                    this.ruleAddForm.two = '二级审批';

                    var humanResource;
                    if(res.data.humanResource == 2){
                        humanResource = false;
                    }else{
                        humanResource = true;
                    }
                    this.ruleAddForm.normalFlow = humanResource;

                    var special;
                    if(res.data.special == 2){
                        special = false;
                    }else{
                        special = true;
                    }
                    this.ruleAddForm.special = special;

                    var doctorA;
                    if(res.data.doctorApprove == 2){
                        doctorA = false;
                    }else{
                        doctorA = true;
                    }
                    this.ruleAddForm.doctorApprove = doctorA;

                    var healthA;
                    if(res.data.healthApprove == 2){
                        healthA = false;
                    }else{
                        healthA = true;
                    }
                    this.ruleAddForm.healthApprove = healthA;

                    this.leaveTypeId = id;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // 添加/编辑假别确定
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    var required;
                    var appendix = this.ruleAddForm.appendix;
                    if(appendix == true){
                        required = '1';
                    }else{
                        required = '0';
                    }

                    var humanResource;
                    var normalFlow = this.ruleAddForm.normalFlow;
                    if(normalFlow == true){
                        humanResource = '1';
                    }else{
                        humanResource = '2';
                    }

                    var special;
                    var specialM = this.ruleAddForm.special;
                    if(specialM == true){
                        special = '1';
                    }else{
                        special = '2';
                    }

                    var doctorA;
                    var doctorApprove = this.ruleAddForm.doctorApprove;
                    if(doctorApprove == true){
                        doctorA = '1';
                    }else{
                        doctorA = '2';
                    }

                    var healthA;
                    var healthApprove = this.ruleAddForm.healthApprove;
                    if(healthApprove == true){
                        healthA = '1';
                    }else{
                        healthA = '2';
                    }

                    if(this.dialogTitle == 0){
                        let data = {
                            explains: this.ruleAddForm.leaveNote,
                            leaveTypeName:this.ruleAddForm.name,
                            minHours: this.ruleAddForm.minHour,
                            required: required,
                            humanResource:humanResource,
                            special:special,
                            doctorApprove:doctorA,
                            healthApprove:healthA
                        }
                        this.$http.addLeaveType(data,(res)=>{
                            
                            if(res.code == 'SUCCESS'){
                                this.dialogAdd = false;
                                this.$refs[formName].resetFields();
                                this.initData()
                                this.$message.success(res.message);
                            }else{
                                this.$message.error(res.message);
                            }
                        },(errRes)=>{
                            
                            this.$message.error(errRes.message);
                        })
                    }else{
                        let data = {
                            explains: this.ruleAddForm.leaveNote,
                            leaveTypeId:this.leaveTypeId,
                            leaveTypeName:this.ruleAddForm.name,
                            minHours: this.ruleAddForm.minHour,
                            required: required,
                            humanResource:humanResource,
                            special:special,
                            doctorApprove:doctorA,
                            healthApprove:healthA
                        }
                        this.$http.updateLeaveType(data,(res)=>{
                            
                            if(res.code == 'SUCCESS'){
                                this.dialogAdd = false;
                                this.$refs[formName].resetFields();
                                this.initData()
                                this.$message.success(res.message);
                            }else{
                                this.$message.error(res.message);
                            }
                        },(errRes)=>{
                            
                            this.$message.error(errRes.message);
                        })
                    }
                } else {
                    return false;
                }
            });
        },

        // 取消
        resetForm(formName){
            this.$refs[formName].resetFields();
            this.dialogAdd = false;
            // this.dialogFlow = false;
        },

        // table删除
        handleDelete(id) {
            this.$confirm('确认删除？','提示',{
                type: 'warning',
            }).then(() => {
                let data = {
                    leaveTypeId:id
                }
                this.$http.deleteLeaveType(data,(res)=>{
                    
                    if(res.code == 'SUCCESS'){
                        this.initData();
                        this.$message.success(res.message);
                    }else{
                        this.$message.error(res.message);
                    }
                },(errRes)=>{
                    
                    this.$message.error(errRes.message);
                })
            }).catch(() => {

            });
        },

        // 审批流
        handleApproval(){
            this.dialogFlow = true;
        },

        // 审批流确定
        submitFlow(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    
                } else {
                    return false;
                }
            });
        },

        // 数字值验证
        blurText(e){
            let boolean = new RegExp("^[1-9][0-9]*$").test(e.target.value);
            if(!boolean){
                this.$message.warning("请输入数字值");
                e.target.value = '';
            }
        },
    },
    mounted(){
        this.initData();
    }
}
</script>
