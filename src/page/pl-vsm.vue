<template>
    <div class="auxiliaryBox">
        <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>考勤设置</el-breadcrumb-item>
            <el-breadcrumb-item :to="{ path: '/auxiliary-information' }">辅助基础信息</el-breadcrumb-item>
            <el-breadcrumb-item>产线和VSM对照表</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
                <el-form-item label="产线：" prop="pl">
                    <el-input v-model="formInline.pl" placeholder="请输入产线" clearable></el-input>
                </el-form-item>
                <el-form-item label="VSM：" prop="vsm">
                    <el-input v-model="formInline.vsm" placeholder="请输入VSM" clearable></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="initData(currentPage = 1)">查询</el-button>
                    <el-button type="primary" @click="addBtn">新增</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableContent">
            <table>
                <thead>
                    <tr>
                        <th>产线</th>
                        <th>VSM</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="tableData.length <= 0">
                        <td colspan="3">暂无数据!</td>
                    </tr>
                    <tr v-for="(item,index) in tableData" :key='index' v-else>
                        <td>{{item.pl}}</td>
                        <td>{{item.vsm}}</td>
                        <td>
                            <el-button type="text" @click="handleEdit(item.plVsmId)">编辑</el-button>
                            <el-button type="text" @click="handleDelete(item.plVsmId)">删除</el-button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- table end -->

        <!-- 分页 start -->
        <div class="paginationBox">
            <el-pagination v-show="total!=0" background layout="total, prev, pager, next, sizes, jumper" :total="total" :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData" @current-change="initData"></el-pagination>
        </div>
        <!-- 分页 end -->

        <!-- 新建dialog start -->
        <el-dialog
            :title="dialogTitle == 0 ? '新建' : '编辑'"
            :visible.sync="dialogAdd"
            width="60%"
            :close-on-click-modal='false'
            @close="resetForm('ruleAddForm')"
            >
            <el-form :model="ruleAddForm" :rules="rulesAdd" ref="ruleAddForm" label-width="100px" class="demo-ruleForm">
                <el-form-item label="产线：" prop="pl">
                    <el-input v-model="ruleAddForm.pl" placeholder="请输入产线"></el-input>
                </el-form-item>
                <el-form-item label="VSM：" prop="vsm">
                    <el-input v-model="ruleAddForm.vsm" placeholder="请输入VSM"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleAddForm')">确定</el-button>
                    <el-button @click="resetForm('ruleAddForm')">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 新建dialog end -->
    </div>
</template>
<script>
export default {
    data(){
        return{
            // 头部查询
            formInline: {
                pl: '',
                vsm: ''
            },
            // 表格
            tableData:[],
            // 分页
            currentPage:1,
            pageSize:10,
            total:0,
            dialogAdd:false,//新建dialog
            // 新建Form
            ruleAddForm:{
                pl: '',
                vsm: ''
            },
            rulesAdd:{
                pl: [
                    { required: true, message: '请输入产线', trigger: 'blur' }
                ],
                vsm: [
                    { required: true, message: '请输入VSM', trigger: 'blur' }
                ]
            },
            dialogTitle:'',
            currentId: ''
        }
    },
    methods:{
        // 渲染数据 分页
        initData(){
            let data = {
                pl: this.formInline.pl,
                vsm: this.formInline.vsm,
                offset: this.pageSize,
                rows: this.currentPage,
            }
            // 产线和VSM对照表分页查询
            this.$http.queryPlVsmPage(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.tableData = res.data.data;
                    this.total = res.data.count;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },
        // 新建
        addBtn() {
            this.dialogAdd = true;
            this.dialogTitle = 0;
            this.ruleAddForm = {
                pl: '',
                vsm: ''
            }
        },

        // table编辑
        handleEdit(id) {
            let data = {
              plVsmId: id
            }
            // 产线和VSM对照表详情查询
            this.$http.queryPlVsmDetail(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.dialogAdd = true;
                    this.dialogTitle = 1;
                    this.ruleAddForm.pl = res.data.pl;
                    this.ruleAddForm.vsm = res.data.vsm;
                    this.currentId = res.data.plVsmId;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },

        // 添加/编辑确定
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if(this.dialogTitle == 0){
                        let data = {
                            pl: this.ruleAddForm.pl,
                            vsm: this.ruleAddForm.vsm
                        }
                        // 产线和VSM对照表新增
                        this.$http.addPlVsm(data,(res)=>{
                            if(res.code == 'SUCCESS'){
                                this.dialogAdd = false;
                                this.$refs[formName].resetFields();
                                this.initData()
                                this.$message.success(res.message);
                            }else{
                                this.$message.error(res.message);
                            }
                        },(errRes)=>{
                            this.$message.error(errRes.message);
                        })
                    }else{
                        let data = {
                            plVsmId: this.currentId,
                            pl: this.ruleAddForm.pl,
                            vsm: this.ruleAddForm.vsm
                        }
                        // 产线和VSM对照表更新
                        this.$http.updatePlVsm(data,(res)=>{
                            if(res.code == 'SUCCESS'){
                                this.dialogAdd = false;
                                this.$refs[formName].resetFields();
                                this.initData()
                                this.$message.success(res.message);
                            }else{
                                this.$message.error(res.message);
                            }
                        },(errRes)=>{
                            this.$message.error(errRes.message);
                        })
                    }
                } else {
                    return false;
                }
            });
        },

        // 取消
        resetForm(formName){
            this.$refs[formName].resetFields();
            this.dialogAdd = false;
        },

        // table删除
        handleDelete(id) {
            this.$confirm('确认删除？','提示',{
                type: 'warning',
            }).then(() => {
                let data = {
                    plVsmId: id
                }
                // 产线和VSM对照表删除
                this.$http.deletePlVsm(data,(res)=>{
                    if(res.code == 'SUCCESS'){
                        this.initData();
                        this.$message.success(res.message);
                    }else{
                        this.$message.error(res.message);
                    }
                },(errRes)=>{
                    this.$message.error(errRes.message);
                })
            }).catch(() => {

            });
        }
    },
    mounted(){
        this.initData();
    }
}
</script>
