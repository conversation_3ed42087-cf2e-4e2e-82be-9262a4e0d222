<template>
    <div class="auxiliaryBox">
        <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>考勤设置</el-breadcrumb-item>
            <el-breadcrumb-item :to="{ path: '/auxiliary-information' }">辅助基础信息</el-breadcrumb-item>
            <el-breadcrumb-item>财务月</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
                <el-form-item label="财务年：" prop="startYear">
                    <el-date-picker
                        v-model="formInline.startYear"
                        type="year"
                        placeholder="请选择年份"
                        value-format="yyyy"
                        :clearable = false>
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="initData(currentPage = 1)">查询</el-button>
                    <el-button type="primary" @click="addBtn">新增</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableContent">
            <table>
                <thead>
                    <tr>
                        <th>财务年</th>
                        <th>财务月</th>
                        <th>开始时间</th>
                        <th>结束时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="tableData.length <= 0">
                        <td colspan="5">暂无数据!</td>
                    </tr>
                    <tr v-for="(item,index) in tableData" :key='index' v-else>
                        <td>{{item.year}}</td>
                        <td>{{item.financeDate}}</td>
                        <td>{{item.startDate}}</td>
                        <td>{{item.endDate}}</td>
                        <td>
                            <el-button type="text" @click="handleEdit(item.financeDateId)">编辑</el-button>
                            <el-button type="text" @click="handleDelete(item.financeDateId)">删除</el-button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- table end -->

        <!-- 分页 start -->
        <div class="paginationBox">
            <el-pagination v-show="total!=0" background layout="total, prev, pager, next, sizes, jumper" :total="total" :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData" @current-change="initData"></el-pagination>
        </div>
        <!-- 分页 end -->

        <!-- 新建dialog start -->
        <el-dialog
            :title="dialogTitle == 0 ? '新建' : '编辑'"
            :visible.sync="dialogAdd"
            width="60%"
            :close-on-click-modal='false'
            @close="resetForm('ruleAddForm')"
            >
            <el-form :model="ruleAddForm" :rules="rulesAdd" ref="ruleAddForm" label-width="100px" class="demo-ruleForm">
                <el-form-item label="财务年：" prop="year">
                    <el-date-picker
                        v-model="ruleAddForm.year"
                        type="year"
                        placeholder="请选择年份"
                        value-format="yyyy"
                        :clearable = false>
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="财务月：" prop="month">
                    <el-input v-model="ruleAddForm.month" placeholder="请输入月份"  @input="inputSet($event)"></el-input>
                </el-form-item>
                <el-form-item label="开始时间："  prop="startTime">
                    <el-date-picker
                        v-model="ruleAddForm.startTime"
                        type="date"
                        placeholder="请选择开始时间"
                        value-format="yyyy-MM-dd"
                        :picker-options="pickerOptionsStart"
                        :clearable = false>
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="结束时间：" prop="endTime">
                    <el-date-picker
                        v-model="ruleAddForm.endTime"
                        type="date"
                        placeholder="请选择结束时间"
                        value-format="yyyy-MM-dd"
                        :picker-options="pickerOptionsEnd"
                        :clearable = false>
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleAddForm')">确定</el-button>
                    <el-button @click="resetForm('ruleAddForm')">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 新建dialog end -->
    </div>
</template>
<script>
export default {
    data(){
        return{
            // 头部查询
            formInline: {
                startYear:''
            },
            // 表格
            tableData:[],
            // 分页
            currentPage:1,
            pageSize:10,
            total:0,
            dialogAdd:false,//新建dialog
            // 新建Form
            ruleAddForm:{
                year:'',
                month:'',
                startTime:'',
                endTime:''
            },
            rulesAdd:{
                year: [
                    { required: true, message: '请选择年份', trigger: 'change' }
                ],
                month: [
                    { required: true, message: '请输入月份', trigger: 'blur' }
                ],
                startTime: [
                    { required: true, message: '请选择开始时间', trigger: 'change' }
                ],
                endTime: [
                    { required: true, message: '请选择结束时间', trigger: 'change' }
                ]
            },
            dialogTitle:'',
            pickerOptionsStart: { //disabledDate 文档上：设置禁用状态，参数为当前日期，要求返回 Boolean
                disabledDate: time => {
                    let endTimeVal = this.ruleAddForm.endTime;
                    if (endTimeVal) {
                        return time.getTime() > new Date(endTimeVal).getTime();
                    }  
                }
            },
            pickerOptionsEnd: {
                disabledDate: time => { //disabledDate 文档上：设置禁用状态，参数为当前日期，要求返回 Boolean
                    let startTimeVal = this.ruleAddForm.startTime;
                    if (startTimeVal) {
                        return time.getTime() < new Date(startTimeVal).getTime();
                    }
                },
            },
            financeDateId:''
        }
    },
    methods:{
        // 渲染数据 分页
        initData(){
            let data = {
                year:this.formInline.startYear,
                offset:this.pageSize,
                rows:this.currentPage,
            }
            this.$http.queryFinanceDatePage(data,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    this.tableData = res.data.data;
                    this.total = res.data.count;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },
        // 新建
        addBtn() {
            this.dialogAdd = true;
            this.dialogTitle = 0;
            this.ruleAddForm  = {
                year:'',
                month:'',
                startTime:'',
                endTime:''
            }
        },

        // table编辑
        handleEdit(id) {
            let data = {
                financeDateId:id
            }
            this.$http.queryFinanceDateDetail(data,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    this.dialogAdd = true;
                    this.dialogTitle = 1;
                    this.ruleAddForm.year = res.data.data.year;
                    this.ruleAddForm.month = res.data.data.financeDate;
                    this.ruleAddForm.startTime = res.data.data.startDate;
                    this.ruleAddForm.endTime = res.data.data.endDate;
                    this.financeDateId = res.data.data.financeDateId;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // 添加/编辑确定
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if(this.dialogTitle == 0){
                        let data = {
                            endDate: this.ruleAddForm.endTime,
                            financeDate:this.ruleAddForm.month,
                            startDate:this.ruleAddForm.startTime,
                            year:this.ruleAddForm.year
                        }
                        this.$http.addFinanceDate(data,(res)=>{
                            
                            if(res.code == 'SUCCESS'){
                                this.dialogAdd = false;
                                this.$refs[formName].resetFields();
                                this.initData()
                                this.$message.success(res.message);
                            }else{
                                this.$message.error(res.message);
                            }
                        },(errRes)=>{
                            
                            this.$message.error(errRes.message);
                        })
                    }else{
                        let data = {
                            endDate: this.ruleAddForm.endTime,
                            financeDate:this.ruleAddForm.month,
                            financeDateId:this.financeDateId,
                            startDate:this.ruleAddForm.startTime,
                            year:this.ruleAddForm.year
                        }
                        this.$http.updateFinanceDate(data,(res)=>{
                            
                            if(res.code == 'SUCCESS'){
                                this.dialogAdd = false;
                                this.$refs[formName].resetFields();
                                this.initData()
                                this.$message.success(res.message);
                            }else{
                                this.$message.error(res.message);
                            }
                        },(errRes)=>{
                            
                            this.$message.error(errRes.message);
                        })
                    }
                } else {
                    return false;
                }
            });
        },

        // 取消
        resetForm(formName){
            this.$refs[formName].resetFields();
            this.dialogAdd = false;
        },

        // table删除
        handleDelete(id) {
            this.$confirm('确认删除？','提示',{
                type: 'warning',
            }).then(() => {
                let data = {
                    financeDateId:id
                }
                this.$http.deleteFinanceDate(data,(res)=>{
                    
                    if(res.code == 'SUCCESS'){
                        this.initData();
                        this.$message.success(res.message);
                    }else{
                        this.$message.error(res.message);
                    }
                },(errRes)=>{
                    
                    this.$message.error(errRes.message);
                })
            }).catch(() => {

            });
        },

        // 月份验证
        inputSet(e){
            let boolean = new RegExp("^[1-9][0-9]*$").test(e);
            if(!boolean){
                this.$message.warning("请输入数字值");
                this.ruleAddForm.month = '';
            }else{
                if(e > 13){
                    this.$message.warning("月份不得超过12");
                    this.ruleAddForm.month = '';
                }
            }
        }
    },
    mounted(){
        this.initData();
    }
}
</script>
