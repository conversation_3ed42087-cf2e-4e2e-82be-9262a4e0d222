<template>
  <div>
    <div v-if="$route.path == '/attendance-summary'" class="attendanceSummaryBox">
      <!-- 面包屑 start -->
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>考勤设置</el-breadcrumb-item>
        <el-breadcrumb-item>考勤汇总</el-breadcrumb-item>
      </el-breadcrumb>
      <!-- 面包屑 end -->

      <!-- search start -->
      <div class="searchFrom">
        <el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
          <el-form-item label="时间：" prop="term">
            <el-date-picker v-model="formInline.term" type="month" placeholder="请选择年月" value-format="yyyy-MM"
              :clearable="false">
            </el-date-picker>
          </el-form-item>

          <el-form-item label="考勤文员工号：" prop="hrId">
            <el-input v-model="formInline.hrId" placeholder="请输入考勤文员工号"></el-input>
          </el-form-item>

          <el-form-item>
            <el-button @click="importBtn">导入</el-button>
            <el-button type="primary" @click="initData((currentPage = 1))">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
      <!-- search end -->

      <div class="tableContant">
        <el-table :data="tableData" border style="width: 100%" v-loading="loading">
          <el-table-column align="center" prop="term" label="期间">
            <template slot-scope="scope">
              {{ scope.row.term.substring(0, 7) }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="create_time" label="最近更新时间" />
          <el-table-column align="center" prop="confirm_status" label="月度确认状态">
            <template slot-scope="scope">{{ statusFormatter(scope.row) }}
            </template></el-table-column>
          <el-table-column align="center" prop="confirm_count" label="已确认">
            <template slot-scope="scope">
              <el-button type="text" @click="handleDetails(scope.row.term.substring(0, 7), 1)">{{ scope.row.confirm_count
              }}</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="unconfirm_count" label="未确认">
            <template slot-scope="scope">
              <el-button type="text" @click="handleDetails(scope.row.term.substring(0, 7), 0)">{{
                scope.row.unconfirm_count }}</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <!-- confirm_status 0，未发起确认，1，已发起，确认中，2：已完成， -->
              <el-button type="text" v-if="scope.row.confirm_status === 1"
                @click="initiateAttendanceConfirm(2, scope.row.term)">确认完成</el-button>
              <el-button type="text" v-if="scope.row.confirm_status === 0"
                @click="initiateAttendanceConfirm(1, scope.row.term)">发起考勤确认</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 start -->
        <div class="paginationBox">
          <el-pagination v-show="total != 0" background layout="total, prev, pager, next, sizes, jumper" :total="total"
            :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData"
            @current-change="initData"></el-pagination>
        </div>
        <!-- 分页 end -->

        <!-- 导入dialog start -->
        <el-dialog title="导入" :visible.sync="dialogImport" width="60%" :close-on-click-modal="false"
          @close="cancelUpload()">
          <el-form label-width="150px" class="demo-ruleForm">
            <el-form-item label="选择文件：">
              <el-upload class="upload-demo" ref="uploadExcel"
                action="/api/admin/monthlyAttendanceAnalysisController/importMonthlyAttendanceAnalysis" :headers="header"
                :limit="limitNum" accept=".xlsx,.XLSX" :file-list="fileList" :before-upload="beforeFileUpload"
                :on-exceed="exceedFile" :on-success="handleFileSuccess" :auto-upload="false" :on-change="handleChange">
                <el-button slot="trigger" type="text">上传文件</el-button>
                <div slot="tip" class="el-upload__tip el-icon-info">
                  导入文件格式为Excel
                </div>
              </el-upload>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="submitUpload()">确定</el-button>
              <el-button @click="cancelUpload()">取消</el-button>
            </el-form-item>
          </el-form>
        </el-dialog>
        <!-- 导入dialog end -->
      </div>
    </div>
    <router-view />
  </div>
</template>
<script>
var axios = require("axios");
import utils from "../utils/index.js";
import dayjs from "dayjs";
export default {
  data() {
    return {
      formInline: {
        term: "",
        month: "",
      },
      tableData: [
        {
          date: "2023-06",
          dateTime: "2023-06",
          status: "已发起，确认中",
          confirmedNum: "212",
          unconfirmedNum: "212",
        },
        {
          date: "2023-06",
          dateTime: "2023-06",
          status: "未发起确认",
          confirmedNum: "212",
          unconfirmedNum: "212",
        },
      ],
      // 分页
      currentPage: 1,
      pageSize: 10,
      total: 0,
      ruleForm: {
        dateTime: "",
        shift: "",
      },

      dialogImport: false,
      limitNum: 1,
      fileList: [],
      header: {
        Authorization: localStorage.getItem("Auth-Token"),
      },
      loading: false,
    };
  },
  methods: {
    // 渲染数据 分页
    initData() {
      this.tableData = [];
      this.loading = true;
      let data = {
        term: dayjs(this.formInline.term).startOf("month").format("YYYY-MM-DD"),
        hrId: this.formInline.hrId,
        offset: this.pageSize,
        rows: this.currentPage,
      };
      this.$http.monthlyAttendance(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            this.loading = false;
            this.tableData = res.data.data;
            this.total = res.data.count;
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },
    statusFormatter(row) {
      // confirm_status 0，未发起确认，1，已发起，确认中，2：已完成
      const status = row.confirm_status;
      if (status == 0) {
        return "未发起确认";
      } else if (status == 1) {
        return "已发起，确认中";
      } else if (status == 2) {
        return "已完成";
      }
    },

    handleDetails(term, confirm_status) {
      this.$router.push({
        path: "/employee-attendance",
        query: { term: term, confirmStatus: confirm_status },
      });
    },
    // 导入
    importBtn() {
      this.dialogImport = true;
    },
    // 上传文件之前的钩子，参数为上传的文件,若返回 false 或者返回 Promise 且被 reject，则停止上传
    beforeFileUpload(file) {
      const extension = file.name.split(".")[1];

      if (extension !== "xlsx" && extension != "xlsx") {
        this.$message.warning("上传模板只能是Excel格式!");
        return false;
      } else {
      }
    },
    // 文件超出个数限制时的钩子
    exceedFile(files, fileList) {
      this.$message.warning({
        title: "警告",
        message: `只能选择 ${this.limitNum} 个文件，当前共选择了 ${files.length + fileList.length
          } 个`,
      });
    },
    // 上传成功后的钩子
    handleFileSuccess(res, file) {
      if (res.code == "SUCCESS") {
        this.dialogImport = false; //关闭弹框
        this.initData();
        this.$message.success(res.message);
      } else {
        this.$message.error(res.message);
      }
    },
    // 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用
    handleChange(file, fileList) {
      this.fileList = fileList;
    },

    // 确定
    submitUpload() {
      if (this.fileList == "") {
        this.$message.error("请上传文件");
      } else {
        this.$refs.uploadExcel.submit();
      }
    },
    // 取消
    cancelUpload() {
      this.fileList = [];
      this.dialogImport = false;
    },

    // 取消
    resetFormE(formName) {
      this.$refs[formName].resetFields();
      this.dialogEdit = false;
    },

    initiateAttendanceConfirm(status, term) {
      this.loading = true;
      let data = {
        term: term,
        status: status,
      };
      this.$http.initiateAttendanceConfirmation(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            this.loading = false;
            this.initData();
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

  },
  mounted() {
    if (this.$route.path == "/attendance-summary") {
      this.formInline.term = dayjs().startOf("month").format("YYYY-MM-DD");
      this.initData();
    }
  },
};
</script>
