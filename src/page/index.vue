<template>
  	<el-row class="container">
		<!-- header start-->
		<el-col :span="24" class="header">
			<el-col :span="20" class="logo">
				<div class="logo-title">TE 智能考勤系统</div>
				<i :class="{ 'el-icon-s-unfold': collapsed, 'el-icon-s-fold': !collapsed }" @click="showToggle()"></i>
			</el-col>
			<el-col :span="4" class="userInfo">
				<el-dropdown trigger="click">
					<span class="el-dropdown-link userInfo-inner">
						<img :src="sysUserImg"/> 
						{{sysUserName}}
					</span>
					<el-dropdown-menu slot="dropdown">
						<el-dropdown-item divided @click.native="bindModfity">修改密码</el-dropdown-item>
						<!-- <el-dropdown-item><router-link :to="{name:'user-center'}">用户中心</router-link></el-dropdown-item> -->
						<el-dropdown-item divided @click.native="logout">退出登录</el-dropdown-item>
					</el-dropdown-menu>
				</el-dropdown>
			</el-col>
		</el-col>
		<!-- header end-->

		<!-- main start-->
		<el-col :span="24" class="main">
			<!-- nav start-->
			<Nav :class="collapsed ? 'menu-collapsed' : ''"></Nav>
			<!-- nav end-->

			<!-- containter start-->
			<section class="content-container"  :class="collapsed ? 'menu-expand' : ''">
				<div class="grid-content bg-purple-light">
					<!-- 内容 start-->
					<el-col :span="24" class="content-wrapper">
						<transition>
							<router-view></router-view>
						</transition>
					</el-col>
					<!-- 内容 end-->
				</div>
			</section>
			<!-- containter end-->
		</el-col>
		<!-- main end-->

		<!-- 修改密码dialog start -->
        <el-dialog
            title="修改密码"
            :visible.sync="dialogModfity"
            width="30%"
            :close-on-click-modal='false'
            @close="resetForm('ruleModfityForm')"
            >
            <el-form :model="ruleModfityForm" :rules="rulesModfity" ref="ruleModfityForm" label-width="120px" class="demo-ruleForm">
                <el-form-item label="密码" prop="pass">
					<el-input type="password" v-model="ruleModfityForm.pass" autocomplete="off"></el-input>
				</el-form-item>
				<el-form-item label="确认密码" prop="checkPass">
					<el-input type="password" v-model="ruleModfityForm.checkPass" autocomplete="off"></el-input>
				</el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleModfityForm')">确定</el-button>
                    <el-button @click="resetForm('ruleModfityForm')">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 修改密码dialog end -->
	</el-row>
</template>
<script>
  // 引用组件
  import Nav from '../components/menu'
  import { Base64 } from 'js-base64';
  export default {
    components:{Nav},
    data() {
		var validatePass = (rule, value, callback) => {
			if (value === '') {
			callback(new Error('请输入密码'));
			} else {
			if (this.ruleModfityForm.checkPass !== '') {
				this.$refs.ruleModfityForm.validateField('checkPass');
			}
			callback();
			}
		};
		var validatePass2 = (rule, value, callback) => {
			if (value === '') {
			callback(new Error('请再次输入密码'));
			} else if (value !== this.ruleModfityForm.pass) {
			callback(new Error('两次输入密码不一致!'));
			} else {
			callback();
			}
		};
      return {
        sysUserImg:'../../static/images/icon_user.png',
        sysUserName: '',
        // 是否折叠
        collapsed:false,
		dialogModfity:false,
		ruleModfityForm:{
			pass:'',
			checkPass:''
		},
		rulesModfity:{
			pass: [{ validator: validatePass, trigger: 'blur' }],
			checkPass: [{ validator: validatePass2, trigger: 'blur' }],
		}
      }
    },
    methods: {
		//收缩展开菜单
		showToggle:function(){
			this.collapsed = !this.collapsed;
		},

		// 修改密码
		bindModfity(){
			this.dialogModfity = true;
		},
		submitForm(formName){
			var _this = this;
			this.$refs[formName].validate((valid) => {
				if (valid) {
					let data = {
						password: Base64.encode(_this.ruleModfityForm.pass),
					}
					_this.$http.updateUserPassword(data,(res)=>{
						
						if(res.code == 'SUCCESS'){
							_this.$refs[formName].resetFields();
							_this.dialogModfity = false;
							localStorage.clear();
							sessionStorage.removeItem('userInfo');
							_this.$router.push('/');
							_this.$message.success(res.message+'，请重新登录！');
						}else{
							_this.$message.error(res.message);
						}
					},(errRes)=>{
						
						_this.$message.error(errRes.message);
					})
				} else {
					return false;
				}
			});
		},
		resetForm(formName){
			this.$refs[formName].resetFields();
			this.dialogModfity = false;
		},

		//退出登录
      	logout: function () {
			var _this = this;
			this.$confirm('确认退出吗?', '提示', {
				//type: 'warning'
			}).then(() => {
				// this.$http.logout(null,(res)=>{
				// 	
				// 	if(res.code == 'SUCCESS'){
						localStorage.clear();
						sessionStorage.removeItem('userInfo');
						_this.$router.push('/');
						this.$message.success("已退出，请重新登录！");
				// 	}else{
				// 		this.$message.error(res.message);
				// 	}
				// },(errRes)=>{
				// 	
				// 	this.$message.error(errRes.message);
				// })
			}).catch(() => {

			});
		}
	}, 
    mounted(){
      var userInfo = JSON.parse(sessionStorage.getItem('userInfo'));
		// this.sysUserImg = userInfo.;
		this.sysUserName = userInfo.name;
    }   
  }
</script>