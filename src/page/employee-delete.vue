<template>
  <div class="employeeBox">
    <!-- 面包屑 start -->
    <el-breadcrumb separator="/">
      <el-breadcrumb-item>员工管理</el-breadcrumb-item>
      <el-breadcrumb-item>删除企业微信</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- search start -->
    <div class="searchFrom">
      <el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
        <el-form-item label="姓名：" prop="name">
          <el-input v-model="formInline.name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="工号：" prop="empId">
          <el-input v-model="formInline.empId" placeholder="请输入工号"></el-input>
        </el-form-item>
<!--        <el-form-item label="部门：" prop="department">-->
<!--          <el-input v-model="formInline.department" placeholder="请输入部门"></el-input>-->
<!--        </el-form-item>-->
        <el-form-item label="班组：" prop="workTeam">
          <el-input v-model="formInline.workTeam" placeholder="请输入班组"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="queryBtn((currentPage = 1))">查询</el-button>
          <el-button @click="resetFormR('formInline')">重置</el-button>
        </el-form-item>
        <el-form-item>
          <el-button @click="deleteWxEmployee">删除企业微信</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- search end -->
    <!-- 面包屑 end -->
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="离职员工" name="1">
        <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" border style="width: 100%"
          @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center"> </el-table-column>
          <el-table-column prop="name" label="姓名" align="center"> </el-table-column>
          <el-table-column prop="empId" label="工号" align="center"> </el-table-column>
<!--          <el-table-column prop="department" label="部门" align="center"> </el-table-column>-->
          <el-table-column prop="workTeam" label="班组" align="center"></el-table-column>
          <el-table-column prop="actived" label="在职状态" align="center"></el-table-column>
        </el-table>
        <!-- 分页 start -->
        <div class="paginationBox">
          <el-pagination v-show="total != 0" background layout="total, prev, pager, next, sizes, jumper" :total="total"
            :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData"
            @current-change="initData">
          </el-pagination>
        </div>
        <!-- 分页 end -->
      </el-tab-pane>
      <el-tab-pane label="企业微信已删除" name="2"> <el-table ref="multipleTable" :data="deleteTableData" tooltip-effect="dark"
          border style="width: 100%" @selection-change="handleSelectionChange">
          <el-table-column prop="name" label="姓名" align="center"> </el-table-column>
          <el-table-column prop="empId" label="工号" align="center"> </el-table-column>
          <el-table-column prop="department" label="部门" align="center"> </el-table-column>
          <el-table-column prop="workTeam" label="班组" align="center"></el-table-column>
          <el-table-column prop="actived" label="在职状态" align="center"></el-table-column>
        </el-table>
        <!-- 分页 start -->
        <div class="paginationBox">
          <el-pagination v-show="totalDeleted != 0" background layout="total, prev, pager, next, sizes, jumper"
            :total="totalDeleted" :current-page.sync="currentPageDeleted" :page-size.sync="pageSizeDeleted"
            @size-change="queryEmployeeDeletedList" @current-change="queryEmployeeDeletedList">
          </el-pagination>
        </div>
        <!-- 分页 end -->
      </el-tab-pane>
    </el-tabs>

  </div>
</template>
<script>
export default {
  data() {
    return {
      activeName: "1",
      formInline: {
        name: "",
        empId: "",
        department: "",
        workTeam: "",
      },
      tableData: [],
      deleteTableData: [],
      multipleSelection: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      openIds: '',
      empIds: '',
      currentPageDeleted: 1,
      pageSizeDeleted: 10,
      totalDeleted: 0
    };
  },
  methods: {
    queryBtn() {

      if (this.activeName == '1') {
        this.currentPage = 1
        this.initData()
      } else {
        this.currentPageDeleted = 1
        this.queryEmployeeDeletedList()
      }
    },
    resetFormR(formName) {
      this.$refs[formName].resetFields();
      this.$refs.multipleTable.clearSelection();
      if (this.activeName == '1') {
        this.currentPage = 1;
        this.initData()
      } else {
        this.currentPageDeleted = 1
        this.queryEmployeeDeletedList()
      }
    },
    handleClick(tab) {
      this.activeName = tab.name
      if (tab.name == '1') {
        this.currentPage = 1
        this.initData()
      } else {
        this.currentPageDeleted = 1
        this.queryEmployeeDeletedList()
      }
    },
    handleSelectionChange(val) {
      var openIds = val.map(item => item.openId ? item.openId : "''")
      var empIds = val.map(item => item.empId)
      this.openIds = openIds.join(',')
      this.empIds = empIds.join(',')
      this.multipleSelection = val;
    },
    // 渲染数据 分页
    initData() {
      let data = {
        department: this.formInline.department,
        empId: this.formInline.empId,
        name: this.formInline.name,
        workTeam: this.formInline.workTeam,
        offset: this.pageSize,
        rows: this.currentPage,
      }
      this.$http.queryEmployeeToBeDelete(data, (res) => {
        if (res.code == 'SUCCESS') {
          this.tableData = res.data.data;
          this.total = res.data.count;
        } else {
          this.$message.error(res.message);
        }
      }, (errRes) => {
        this.$message.error(errRes.message);
      })
    },

    queryEmployeeDeletedList() {
      let data = {
        department: this.formInline.department,
        empId: this.formInline.empId,
        name: this.formInline.name,
        workTeam: this.formInline.workTeam,
        offset: this.pageSizeDeleted,
        rows: this.currentPageDeleted,
      }
      this.$http.queryEmployeeDeleted(data, (res) => {
        if (res.code == 'SUCCESS') {
          this.deleteTableData = res.data.data;
          this.totalDeleted = res.data.count;
        } else {
          this.$message.error(res.message);
        }
      }, (errRes) => {
        this.$message.error(errRes.message);
      })
    },
    deleteWxEmployee() {
      if (this.multipleSelection.length <= 0) {
        this.$message.error("请选择需要删除的员工！")
      }
      const h = this.$createElement;
      this.$msgbox({
        type: "warning",
        title: "",
        message: h("p", null, [
          h("p", null, "确定将这些员工从企业微信中删除吗？ "),
          h("p", { style: "color: red" }, "删除后员工将退出企业微信，不能再使用，"),
          h("span", { style: "color: #999" }, "你还要继续吗？")
        ]),
        customClass: 'msgBox',
        showCancelButton: true,
        confirmButtonText: "继续",
        cancelButtonText: "取消",
        beforeClose: (action, instance, done) => {
          if (action === "confirm") {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = "执行中...";
            let data = {
              empIds: this.empIds,
              openIds: this.openIds,
            }
            this.$http.deleteAllWeWorkEmp(data, (res) => {
              if (res.code == 'SUCCESS') {
                instance.confirmButtonLoading = false;
                this.$message.success(res.message);
                done();
                this.initData();
              } else {
                this.$message.error(res.message);
                instance.confirmButtonLoading = false;
                done();
              }
            }, (errRes) => {
              done();
              this.$message.error(errRes.message);
              instance.confirmButtonLoading = false;
            })
          } else {
            done();
            instance.confirmButtonLoading = false;
          }
        },
      }).then((action) => {
      }).catch((cancel) => {
      });
    },
  },
  mounted() {
    this.initData();
  }
};
</script>
