<template>
    <div class="overtimeBox">
       <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>数据查询</el-breadcrumb-item>
            <el-breadcrumb-item>加班记录查询</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
                <el-form-item label="姓名：" prop="name">
                    <el-input v-model="formInline.name" placeholder="请输入姓名"></el-input>
                </el-form-item>
                <el-form-item label="工号：" prop="number">
                    <el-input v-model="formInline.number" placeholder="请输入工号"></el-input>
                </el-form-item>
                <el-form-item label="Plant：" prop="plant">
                    <el-input v-model="formInline.plant" placeholder="请输入Plant"></el-input>
                </el-form-item>
                <el-form-item label="Workshop：" prop="workshop">
                    <el-input v-model="formInline.workshop" placeholder="请输入Workshop"></el-input>
                </el-form-item>
                <el-form-item label="生产线：" prop="productLine">
                    <el-input v-model="formInline.productLine" placeholder="请输入生产线"></el-input>
                </el-form-item>
                <el-form-item label="部门：" prop="department">
                    <el-input v-model="formInline.department" placeholder="请输入部门"></el-input>
                </el-form-item>
                <el-form-item label="班组：" prop="team">
                    <el-input v-model="formInline.team" placeholder="请输入班组"></el-input>
                </el-form-item>
                <el-form-item label="班次：" prop="shifts">
                    <el-input v-model="formInline.shifts" placeholder="请输入班次"></el-input>
                </el-form-item>
                <el-form-item label="日期：" prop="dataTime">
                    <el-date-picker
                        v-model="formInline.dataTime"
                        type="daterange"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd"
                        range-separator="~"
                        :clearable = false>
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="申请通过时间：" prop="finishTime">
                    <el-date-picker
                        v-model="formInline.finishTime"
                        type="daterange"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        value-format="yyyy-MM-dd"
                        range-separator="~"
                        :clearable = false>
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="initData(currentPage = 1)">查询</el-button>
                    <el-button @click="resetForm('formInline')">重置</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="exportBtn">导出</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableContent">
            <table>
                <thead>
                    <tr>
                        <th>姓名</th>
                        <th>工号</th>
                        <th>Plant</th>
                        <th>Workshop</th>
                        <th>生产线</th>
                        <th>部门</th>
                        <th>班组</th>
                        <th>班次</th>
                        <th>加班日期</th>
                        <th>加班开始时间</th>
                        <th>加班结束时间</th>
                        <!-- <th>加班时长（h）</th> -->
                        <th>申请通过时间</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="tableData.length <= 0">
                        <td colspan="11">暂无数据!</td>
                    </tr>
                    <tr v-for="(item,index) in tableData" :key='index' v-else>
                        <td>{{item.operator}}</td>
                        <td>{{item.empId}}</td>
                        <td>{{item.plant}}</td>
                        <td>{{item.workshop}}</td>
                        <td>{{item.productionLine}}</td>
                        <td>{{item.department}}</td>
                        <td>{{item.workTeam}}</td>
                        <td>{{item.workType}}</td>
                        <td>{{item.overtimeDate}}</td>
                        <td>{{item.startTime}}</td>
                        <td>{{item.endTime}}</td>
                        <!-- <td>{{item.hours}}</td> -->
                        <td>{{item.finishTime}}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- table end -->

        <!-- 分页 start -->
        <div class="paginationBox">
            <el-pagination v-show="total!=0" background layout="total, prev, pager, next, sizes, jumper" :total="total" :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData" @current-change="initData"></el-pagination>
        </div>
        <!-- 分页 end -->
    </div>
</template>
<script>
var axios = require('axios')// 引用axios
import utils from '../utils/index.js'//引用常用工具文件
export default {
    data(){
        return{
            // 头部查询
            formInline: {
                name:'',
                number:'',
                plant:'',
                workshop:'',
                productLine:'',
                department:'',
                team:'',
                shifts:'',
                dataTime:'',
                finishTime:''
            },
            // 表格
            tableData:[],
            // 分页
            currentPage:1,
            pageSize:10,
            total:0,
        }
    },
    methods:{
        // 重置
        resetForm(formName) {
            this.$refs[formName].resetFields();
            this.currentPage = 1;
            this.initData();
        },

        // 渲染数据 分页
        initData(){
            let data = {
                department:this.formInline.department,
                empId:this.formInline.number,
                startDate:this.formInline.dataTime ? this.formInline.dataTime[0] : '',
                endDate:this.formInline.dataTime ? this.formInline.dataTime[1] : '',
                name:this.formInline.name,
                plant:this.formInline.plant,
                productionLine:this.formInline.productLine,
                workTeam:this.formInline.team,
                workshop:this.formInline.workshop,
                workType:this.formInline.shifts,
                offset:this.pageSize,
                rows:this.currentPage,
                finishStartDate:this.formInline.finishTime ? this.formInline.finishTime[0] : '',
                finishEndDate:this.formInline.finishTime ? this.formInline.finishTime[1] : '',
            }
            this.$http.queryOvertimeApplyPage(data,(res)=>{

                if(res.code == 'SUCCESS'){
                    this.tableData = res.data.data;
                    this.total = res.data.count;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{

                this.$message.error(errRes.message);
            })
        },

        // 导出
        exportBtn(){
            let data = {
                department:this.formInline.department,
                empId:this.formInline.number,
                startDate:this.formInline.dataTime ? this.formInline.dataTime[0] : '',
                endDate:this.formInline.dataTime ? this.formInline.dataTime[1] : '',
                name:this.formInline.name,
                plant:this.formInline.plant,
                productionLine:this.formInline.productLine,
                workTeam:this.formInline.team,
                workshop:this.formInline.workshop,
                workType:this.formInline.shifts,
                offset:this.pageSize,
                rows:this.currentPage,
                finishStartDate:this.formInline.finishTime ? this.formInline.finishTime[0] : '',
                finishEndDate:this.formInline.finishTime ? this.formInline.finishTime[1] : '',
            }
            axios({
                url: 'api/admin/overtimeApplyController/exportOvertime',
                method: "POST",
                data:data,
                responseType:"blob",
                headers:{
                    // 'Content-Type':'application/json;charset=UTF-8',
                    'Authorization' : localStorage.getItem('Auth-Token')
                },
            }).then(function (res) {
                if(!res){
                    return
                }
                utils.downloadExcel(res.data,'加班申请记录 ','xlsx')//调用公共方法
            })
        },
    },
    mounted(){
        this.initData();
    }
}
</script>
