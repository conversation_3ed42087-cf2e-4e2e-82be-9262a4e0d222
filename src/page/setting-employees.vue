<template>
    <div class="settingBox">
       <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>报告数据查询</el-breadcrumb-item>
            <el-breadcrumb-item>不计工时员工设置</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
                <el-form-item label="员工姓名：" prop="name">
                    <el-input v-model="formInline.name" placeholder="请输入员工姓名"></el-input>
                </el-form-item>
                <el-form-item label="员工工号：" prop="number">
                    <el-input v-model="formInline.number" placeholder="请输入员工工号"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="initData(currentPage = 1)">查询</el-button>
                    <el-button type="primary" @click="addBtn">添加</el-button>
                    <el-button type="primary" @click="importBtn">导入</el-button>
                    <el-button type="primary" @click="exportBtn">导出</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableContent">
            <table>
                <thead>
                    <tr>
                        <th>员工姓名</th>
                        <th>员工工号</th>
                        <th>在职状态</th>
                        <th>部门</th>
                        <th>班组</th>
                        <th>原因</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="tableData.length <= 0">
                        <td colspan="7">暂无数据!</td>
                    </tr>
                    <tr v-for="(item,index) in tableData" :key='index' v-else>
                        <td>{{item.name}}</td>
                        <td>{{item.empId}}</td>
                        <td>{{item.actived}}</td>
                        <td>{{item.workshop}}</td>
                        <td>{{item.workTeam}}</td>
                        <td>{{item.reason}}</td>
                        <td>
                            <el-button type="text" @click="handleDelete(item.outRangeEmployeeId)">删除</el-button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- table end -->

        <!-- 分页 start -->
        <div class="paginationBox">
            <el-pagination v-show="total!=0" background layout="total, prev, pager, next, sizes, jumper" :total="total" :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData" @current-change="initData"></el-pagination>
        </div>
        <!-- 分页 end -->

        <!-- 添加员工dialog start -->
        <el-dialog
            title="添加员工"
            :visible.sync="dialogAddEmploy"
            width="80%"
            :close-on-click-modal='false'
            @close="resetForm('ruleForm')"
            >
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
                <!-- 查询 start -->
                <el-form :inline="true" label-width="100px" class="demo-form-inline">
                    <el-form-item label="姓名：">
                        <el-input v-model="ruleForm.name" placeholder="请输入姓名"></el-input>
                    </el-form-item>
                    <el-form-item label="工号：">
                        <el-input v-model="ruleForm.number" placeholder="请输入工号"></el-input>
                    </el-form-item>
                    <el-form-item label="部门：">
                        <el-input v-model="ruleForm.department" placeholder="请输入部门"></el-input>
                    </el-form-item>
                    <el-form-item label="在职状态：">
                        <el-select v-model="ruleForm.status" placeholder="请选择在职状态">
                            <el-option
                                v-for="item in statusOption"
                                :key="item"
                                :label="item"
                                :value="item">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="班组：" prop="team">
                        <el-select v-model="ruleForm.team" placeholder="请选择班组">
                            <el-option
                                v-for="item in teamOption"
                                :key="item.id"
                                :label="item.workTeam"
                                :value="item.workTeam">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="checkBtn(currentPage = 1)">查询</el-button>
                    </el-form-item>
                </el-form>
                <!-- 查询 end -->
            
                <!-- 员工 start-->
                <div class="employeeTable">
                    <el-table
                        ref="multipleTable"
                        :data="tableDataE"
                        style="width: 100%"
                        @selection-change="handleSelectionChange"
                        :row-key="getRowKeys">
                        <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
                        <el-table-column prop="name" label="姓名"></el-table-column>
                        <el-table-column prop="empId" label="工号"></el-table-column>
                        <el-table-column prop="department" label="部门"></el-table-column>
                        <el-table-column prop="actived" label="在职状态"></el-table-column>
                        <el-table-column prop="workTeam" label="班组"></el-table-column>
                    </el-table>
                </div>
                <!-- 员工 end-->

                <!-- 子分页 start -->
                <div class="paginationBox">
                    <el-pagination v-show="totalE!=0" background layout="total, prev, pager, next, sizes, jumper" :total="totalE" :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="checkBtn" @current-change="checkBtn"></el-pagination>
                </div>
                <!-- 子页 end -->

                <div class="selectNotice">已选{{employeeNum}}名员工</div>

                <!-- 已选 start-->
                <div class="tableContent">
                    <table>
                        <thead>
                            <tr>
                                <th><el-button type="text" @click="deleteEmployeeAll">全部删除</el-button></th>
                                <th>姓名</th>
                                <th>工号</th>
                                <th>部门</th>
                                <th>在职状态</th>
                                <th>班组</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-if="tableSelectData.length<=0">
                                <td colspan="6">暂无数据</td>
                            </tr>
                            <tr v-else v-for="(item, index) in tableSelectData" :key="index">
                                <td><el-button type="text" @click="deleteEmployeeBtn(item)">删除</el-button></td>
                                <td>{{item.name}}</td>
                                <td>{{item.empId}}</td>
                                <td>{{item.department}}</td>
                                <td>{{item.actived}}</td>
                                <td>{{item.workTeam}}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!-- 已选 end-->

                <el-form-item label="原因：" prop="reason">
                    <el-select v-model="ruleForm.reason" placeholder="请选择原因">
                        <el-option
                            v-for="item in reasonOption"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-input v-if="ruleForm.reason == 2" v-model="ruleForm.reasonDesc" placeholder="请输入具体原因"></el-input>
                </el-form-item>

                <el-form-item>
                    <el-button @click="resetForm('ruleForm')">上一步</el-button>
                    <el-button type="primary" @click="submitForm('ruleForm')">完成</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 添加员工dialog end -->

        <!-- 导入dialog start -->
        <el-dialog
            title="导入"
            :visible.sync="dialogImport"
            width="60%"
            :close-on-click-modal='false'
            @close="cancelUpload()"
            >
            <el-form class="demo-ruleForm">
                <el-form-item label="不计工时导入：">
                    <el-upload
                        class="upload-demo"
                        ref="uploadExcel"
                        action="/api/admin/outRangeEmployeeController/importOutRangeEmployee"
                        :headers="header"
                        :limit="limitNum"
                        accept=".XLS,.xlsx"
                        :file-list="fileList"
                        :before-upload="beforeFileUpload"
                        :on-exceed="exceedFile"
                        :on-success="handleFileSuccess"
                        :auto-upload="false"
                        :on-change="handleChange">
                        <el-button slot="trigger" type="text">选择文件</el-button>
                        <div slot="tip" class="el-upload__tip el-icon-info">导入文件格式为Excel</div>
                    </el-upload>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitUpload()">确定</el-button>
                    <el-button @click="cancelUpload()">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 导入dialog end -->
    </div>
</template>
<script>
var axios = require('axios')// 引用axios
import utils from '../utils/index.js'//引用常用工具文件
export default {
    data(){
        return{
            // 头部查询
            formInline: {
                name:'',
                number:''
            },
            // 表格
            tableData:[],
            // 分页
            currentPage:1,
            pageSize:10,
            total:0,
            dialogAddEmploy:false,//添加员工dialog
            ruleForm:{
                // 表格查询
                name:'',
                number:'',
                department:'',
                status:'',
                team:'',

                reason:'',
                reasonDesc:''
            },
            rules:{
                reason: [
                    { required: true, message: '请选择原因', trigger: 'change' }
                ],
            },
            statusOption:['Active','InActive','Withdrawn'],
            teamOption:[],
            reasonOption:['线外人员','其他'],
            // 子表格
            tableDataE:[],
            // 子分页
            totalE:0,
            employeeNum:0,
            multipleSelection: [],// 员工表全选中
            tableSelectData:[],// 已选的人员信息
            employeeSelectIds:[],//已选的人员工号
            // 导入dialog
            dialogImport:false,
            limitNum: 1,
            fileList: [],
            header:{
                'Authorization' : localStorage.getItem('Auth-Token')
            },
        }
    },
    methods:{
        // 渲染数据 分页
        initData(){
            let data = {
                // actived:'',
                empId:this.formInline.number,
                name:this.formInline.name,
                reason:'',
                // workTeam:'',
                // workshop:'',
                offset:this.pageSize,
                rows:this.currentPage,
            }
            this.$http.queryOutRangeEmployeePage(data,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    this.tableData = res.data.data;
                    this.total = res.data.count;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // 添加
        addBtn() { 
            this.dialogAddEmploy = true;
        },

        // 导出
        exportBtn(){
            let data = {
                empId:this.formInline.number,
                name:this.formInline.name,
                offset:this.pageSize,
                rows:this.currentPage,
                reason:''
            }
            axios({
                url: 'api/admin/outRangeEmployeeController/exportOutRangeEmployee',
                method: "POST",
                data:data,
                responseType:"blob",
                headers:{
                    // 'Content-Type':'application/json;charset=UTF-8',
                    'Authorization' : localStorage.getItem('Auth-Token')
                },
            }).then(function (res) {
                if(!res){
                    return
                }
                utils.downloadExcel(res.data,'不计工时员工设置 ','xlsx')//调用公共方法
            })
        },

        // 导入
        importBtn(){
            this.dialogImport = true;
        },
        // 上传文件之前的钩子，参数为上传的文件,若返回 false 或者返回 Promise 且被 reject，则停止上传
        beforeFileUpload (file){
            const extension = file.name.split('.')[1];
            if ((extension !== 'XLS') && (extension !== 'xlsx')) {
                this.$message.warning('上传模板只能是 XLS、xlsx格式!')
                return false
            }else{

            }
        },
        // 文件超出个数限制时的钩子
        exceedFile(files, fileList) {
            this.$message.warning({
                title: '警告',
                message: `只能选择 ${this.limitNum} 个文件，当前共选择了 ${files.length + fileList.length} 个`
            });
        },
        // 上传成功后的钩子
        handleFileSuccess(res, file) {
            if(res.code == 'SUCCESS'){
                this.dialogImport = false;//关闭弹框
                this.initData();
                this.$message.success(res.message);  
            }else{
                this.$message.error(res.message);
            }
        },
        // 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用
        handleChange(file, fileList) {
            this.fileList = fileList;
        },

        // 确定
        submitUpload(){
            if(this.fileList == ''){
                this.$message.error("请上传文件");
            }else{
                this.$refs.uploadExcel.submit();
            }
        },
        // 取消
        cancelUpload(){
            this.fileList = [];
            this.dialogImport = false;
        },

        // table删除
        handleDelete(id) {
            this.$confirm('确认删除？','提示',{
                type: 'warning',
            }).then(() => {
                let data = {
                    outRangeEmployeeId:id
                }
                this.$http.deleteOutRangeEmployee(data,(res)=>{
                    
                    if(res.code == 'SUCCESS'){
                        this.initData();
                        this.$message.success(res.message);
                    }else{
                        this.$message.error(res.message);
                    }
                },(errRes)=>{
                    
                    this.$message.error(errRes.message);
                })
            }).catch(() => {

            });
        },

        // dialog查询
        checkBtn(){
            let data = {
                department:this.ruleForm.department,
                empId:this.ruleForm.number,
                // imgStatus:'',
                name:this.ruleForm.name,
                // plant:'',
                // productionLine:'',
                workTeam:this.ruleForm.team,
                // workshop:'',
                actived:this.ruleForm.status,
                offset:this.pageSize,
                rows:this.currentPage,
            }
            this.$http.queryEmployeePage(data,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    this.tableDataE = res.data.data;
                    this.totalE = res.data.count;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // 全选
        handleSelectionChange(val) {
            this.multipleSelection = val;
            this.tableSelectData = this.multipleSelection;
            this.employeeNum = this.tableSelectData.length;
            this.employeeSelectIds = val.map(item => {
                return item.empId
            })
        },

        // 获取row的key值
        getRowKeys(row) {
            return row.empId;
        },

        // 全部删除
        deleteEmployeeAll(){
            this.tableSelectData = [];
            this.employeeSelectIds = [];
            this.employeeNum = 0;
        },

        // 删除员工 
        deleteEmployeeBtn(item){
            var index = this.tableSelectData.indexOf(item)
            if (index !== -1) {
                this.tableSelectData.splice(index, 1)
                this.employeeSelectIds.splice(index, 1)
            }
        },

        // 取消/上一步
        resetForm(formName){
            this.$refs[formName].resetFields();
            this.dialogAddEmploy = false;
            this.multipleSelection = [];
            this.tableSelectData = [];
            this.employeeSelectIds = [];
            this.employeeNum = 0;
            this.$refs.multipleTable.clearSelection();//取消选择
        },

        // 完成
        submitForm(formName){
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if(this.employeeSelectIds == ''){
                        this.$message.error("请选择人员")
                    }else{
                        let data = {
                            empIds: this.employeeSelectIds,
                            reason: this.ruleForm.reason,
                        }
                        this.$http.addOutRangeEmployee(data,(res)=>{
                            
                            if(res.code == 'SUCCESS'){
                                this.dialogAddEmploy = false;
                                this.initData()
                                this.$message.success(res.message);
                                this.$refs[formName].resetFields();
                            }else{
                                this.$message.error(res.message);
                            }
                        },(errRes)=>{
                            
                            this.$message.error(errRes.message);
                        })
                    }
                } else {
                    return false;
                }
            }); 
        },

        // 班组 下拉选
        teamList(){
            this.$http.queryWorkGroupAll(null,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    this.teamOption = res.data.data;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        }
    },
    mounted(){
        this.initData();
        this.teamList();
    }
}
</script>
