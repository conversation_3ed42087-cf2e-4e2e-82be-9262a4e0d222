<template>
    <div class="loginBox">
        <!-- 登录 start -->
        <div class="loginCenter">
            <p class="centerTitle">TE-FRAS</p>
            <div class="centerForm">
                <!-- 账户密码登录 start -->
                <el-form :model="accountForm" :rules="rules" ref="accountForm" class="demo-ruleForm">
                    <el-form-item prop="account" label="账户：">
                        <el-input type="text" v-model="accountForm.account" placeholder="请输入账户" autocomplete="off"></el-input>
                    </el-form-item>
                    <el-form-item prop="pass" label="密码：">
                        <el-input type="password" v-model="accountForm.pass" placeholder="请输入密码" autocomplete="off"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-checkbox v-model="accountForm.checked">记住密码</el-checkbox>
                    </el-form-item>
                    <el-form-item>
                        <el-button class="loginBtn" type="primary" @click="login('accountForm')">登录</el-button>
                    </el-form-item>
                </el-form>
                <!-- 账户密码登录 end -->
            </div>
        </div>
        <!-- 登录 end -->
    </div>
</template>
<script>
//   import sha1 from 'js-sha1'
//   import Cryptojs from 'crypto-js'
  import { Base64 } from 'js-base64';
  export default {
    data() {
        // 账户验证
        var checkAccount = (rule,value,callback) => {
            if(!value){
                callback(new Error('账户不能为空！'))
            }else{
                callback()
            }
        };
        // 密码验证
        var checkPass = (rule,value,callback) => {
            if(!value){
                callback(new Error('密码不能为空!'))
            }else{
                callback()
            }
        };
      return {
        accountForm:{
            account:'',
            pass:'',
            checked:false
        },
        rules:{
            account:[
                { validator: checkAccount, trigger: 'blur' }
            ],
            pass:[
                { validator: checkPass, trigger: 'blur' }
            ],
        },
      }
    },
    created () {
        // 在页面加载时从cookie获取登录信息
        let account = this.getCookie("account")
        let pass = Base64.decode(this.getCookie("pass")) 
        // 如果存在赋值给表单，并且将记住密码勾选
        if(account && pass){
            this.accountForm.account = account;
            this.accountForm.pass = pass;
            this.accountForm.checked = true;
        }
    },
    methods: {
        // 登录
        login(formName){
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let data = {
                        username:this.accountForm.account,
                        password:Base64.encode(this.accountForm.pass)
                        // password:sha1(this.accountForm.pass)
                        // password:Cryptojs.HmacSHA1(this.accountForm.pass, this.accountForm.account).toString(),
                    }
                    this.$http.login(data,(res)=>{
                        
                        if(res.code == 'SUCCESS'){
                            this.setLoginInfo();// 储存登录信息
                            localStorage.setItem('Auth-Token', res.data.token);
                            sessionStorage.setItem('userInfo',JSON.stringify(res.data.user));
                            sessionStorage.setItem('menuList',JSON.stringify(res.data.menuList));
                            this.$message.success(res.message);
                            this.$router.push({ path: res.data.menuList[0].path })

                            // 是否会是领班
                            // this.$http.queryEmployeeIsLeader(null,(res)=>{
                            //     if(res.code == 'SUCCESS'){
                            //         // sessionStorage.setItem('isLeader',res.data.isLeader);
                            //         if(res.data.isLeader == true){
                            //             this.$router.push({ path: '/workforce-management' });
                            //         }else{
                            //             this.$router.push({ path: '/employee-management' });
                            //         }
                            //     }else{
                            //         this.$message.error(res.message);
                            //     }
                            // },(errRes)=>{
                                
                            //     this.$message.error(errRes.message);
                            // })

                        }else{
                            this.$message.error(res.message);
                        }
                    },(errRes)=>{
                        
                        this.$message.error(errRes.message);
                    })
                } else {
                    return false;
                }
            });
        },

        // 储存登录信息
        setLoginInfo: function () {
            // 判断用户是否勾选记住密码，如果勾选，向cookie中储存登录信息；如果没有勾选，储存的信息为空
            if(this.accountForm.checked){
                this.setCookie("account",this.accountForm.account);
                // base64加密密码
                let passWord = Base64.encode(this.accountForm.pass);
                this.setCookie("pass",passWord);
                // this.setCookie("checked",this.accountForm.checked);  
            }else{
                this.setCookie("account","")
                this.setCookie("pass","") 
                this.setCookie("checked",false) 
            } 
        },

        // 获取cookie
        getCookie: function (key) {
            if (document.cookie.length > 0) {
            var start = document.cookie.indexOf(key + '=')
            if (start !== -1) {
                start = start + key.length + 1
                var end = document.cookie.indexOf(';', start)
                if (end === -1) end = document.cookie.length
                return unescape(document.cookie.substring(start, end))
            }
            }
            return ''
        },

        // 设置cookie
        setCookie: function (cName, value, expiredays) {
            var exdate = new Date()
            exdate.setDate(exdate.getDate() + expiredays)
            document.cookie = cName + '=' + decodeURIComponent(value) +
            ((expiredays == null) ? '' : ';expires=' + exdate.toGMTString())
        },
    },
    mounted(){
        // 在页面加载时从cookie获取登录信息
        let account = this.getCookie("account")
        let pass = Base64.decode(this.getCookie("pass"))
        // 如果存在赋值给表单，并且将记住密码勾选
        if(account && pass){
            this.accountForm.account = account;
            this.accountForm.pass = pass;
            this.accountForm.checked = true;
        }else{
            this.accountForm.account = '';
            this.accountForm.pass = '';
            this.accountForm.checked = false;
        }
    }
  }
</script>
