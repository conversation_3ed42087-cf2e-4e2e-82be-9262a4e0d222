<template>
    <div class="dataBox">
       <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>报告数据查询</el-breadcrumb-item>
            <el-breadcrumb-item :to="{ path: '/direct-manpower' }">直接人力工时报告</el-breadcrumb-item>
            <el-breadcrumb-item>生成数据</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
                <el-form-item label="日期：" prop="dataTime">
                    <el-date-picker
                        v-model="formInline.dataTime"
                        type="daterange"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd"
                        range-separator="~"
                        :clearable = false>
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="dataBtn">生成数据</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableContent">
            <table>
                <thead>
                    <tr>
                        <th>Row Labels</th>
                        <th>Sum of 标准</th>
                        <th>Sum of OT</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="tableData.length <= 0">
                        <td colspan="3">暂无数据!</td>
                    </tr>
                    <tr v-for="(item,index) in tableData" :key='index' v-else>
                        <td>{{item.one}}</td>
                        <td>{{item.two}}</td>
                        <td>{{item.three}}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- table end -->
    </div>
</template>
<script>
export default {
    data(){
        return{
            // 头部查询
            formInline: {
                dataTime:''
            },
            // 表格
            tableData:[
                {  
                    one:'',  
                    two:'',  
                    three:''
                }
            ]
        }
    },
    methods:{
        // 渲染数据
        initData(){
            
        },

        // 生成数据
        dataBtn(){
            
        }
    },
    mounted(){
        this.initData();
    }
}
</script>
