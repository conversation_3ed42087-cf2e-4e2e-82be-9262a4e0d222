<template>
    <div class="staffBox">
       <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>报告数据查询</el-breadcrumb-item>
            <el-breadcrumb-item>员工班车统计</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
                <el-form-item label="日期：" prop="startTime">
                    <el-date-picker
                        v-model="formInline.startTime"
                        type="date"
                        placeholder="请选择日期"
                        value-format="yyyy-MM-dd"
                        :clearable = false>
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="员工姓名：" prop="name">
                    <el-input v-model="formInline.name" placeholder="请输入员工姓名"></el-input>
                </el-form-item>
                <el-form-item label="员工工号：" prop="number">
                    <el-input v-model="formInline.number" placeholder="请输入员工工号"></el-input>
                </el-form-item>
                <!-- <el-form-item label="今日班次：" prop="todayShift">
                    <el-input v-model="formInline.todayShift" placeholder="请输入今日班次"></el-input>
                </el-form-item> -->
                <el-form-item label="线路名称：" prop="roadName">
                    <el-input v-model="formInline.roadName" placeholder="请输入线路名称"></el-input>
                </el-form-item>
                <!-- <el-form-item label="上车人数：" prop="peopleNumber">
                    <el-input v-model="formInline.peopleNumber" placeholder="请输入上车人数"></el-input>
                </el-form-item> -->
                <el-form-item>
                    <el-button type="primary" @click="initData(currentPage = 1)">查询</el-button>
                    <el-button type="primary" @click="exportBtn">导出</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableContent">
            <table>
                <thead>
                    <tr>
                        <th>员工姓名</th>
                        <th>员工工号</th>
                        <th>班次</th>
                        <th>线路名称</th>
                        <th>站点名称</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="tableData.length <= 0">
                        <td colspan="4">暂无数据!</td>
                    </tr>
                    <tr v-for="(item,index) in tableData" :key='index' v-else>
                        <td>{{item.name}}</td>
                        <td>{{item.empId}}</td>
                        <td>{{item.workType}}</td>
                        <td>{{item.lineName}}</td>
                        <td>{{item.stationName}}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- table end -->

        <!-- 分页 start -->
        <div class="paginationBox">
            <el-pagination v-show="total!=0" background layout="total, prev, pager, next, sizes, jumper" :total="total" :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData" @current-change="initData"></el-pagination>
        </div>
        <!-- 分页 end -->
    </div>
</template>
<script>
var axios = require('axios')// 引用axios
import utils from '../utils/index.js'//引用常用工具文件
export default {
    data(){
        return{
            // 头部查询
            formInline: {
                startTime:'',
                name:'',
                number:'',
                // todayShift:'',
                roadName:'',
                // peopleNumber:''
            },
            // 表格
            tableData:[],
            // 分页
            currentPage:1,
            pageSize:10,
            total:0,
        }
    },
    methods:{
        // 渲染数据
        initData(){
            let data = {
                empId:this.formInline.number,
                lineName:this.formInline.roadName,
                name:this.formInline.name,
                offset:this.pageSize,
                rows:this.currentPage,
                schduleDate:this.formInline.startTime
            }
            this.$http.queryEmployeeLineStationPage(data,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    this.tableData = res.data.data;
                    this.total = res.data.count;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // 导出
        exportBtn(){
            let data = {
                empId:this.formInline.number,
                lineName:this.formInline.roadName,
                name:this.formInline.name,
                offset:this.pageSize,
                rows:this.currentPage,
                schduleDate:this.formInline.startTime
            }
            axios({
                url: 'api/admin/employeeLineStationController/exportEmployeeLineStation',
                method: "POST",
                data:data,
                responseType:"blob",
                headers:{
                    // 'Content-Type':'application/json;charset=UTF-8',
                    'Authorization' : localStorage.getItem('Auth-Token')
                },
            }).then(function (res) {
                if(!res){
                    return
                }
                utils.downloadExcel(res.data,'员工班车统计 ','xlsx')//调用公共方法
            })
        },
    },
    mounted(){
        this.initData();
    }
}
</script>
