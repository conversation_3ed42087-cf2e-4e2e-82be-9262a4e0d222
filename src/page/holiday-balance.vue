<template>
    <div class="machineBox">
        <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>假期余额管理</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
              <el-form-item label="班组名称：" prop="name">
                <el-select v-model="formInline.workTeam" filterable allow-create placeholder="请输入班组名称">
                  <el-option v-for="item in nameOption" :key="item.id" :label="item.name" :value="item.name">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="姓名：" prop="name">
                  <el-input v-model="formInline.name" placeholder="请输入姓名"></el-input>
              </el-form-item>
              <el-form-item label="工号：" prop="empId">
                <el-input v-model="formInline.empId" placeholder="请输入工号"></el-input>
              </el-form-item>
              <el-form-item label="有薪假类别：" prop="holidayType">
                <el-select v-model="formInline.holidayType" filterable placeholder="请选择有薪假类别">
                  <el-option
                    v-for="item in holidayTypeOption"
                    :label="item.name"
                    :value="item.name">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="更新日期" prop="updateDate">
                <el-col :span="11">
                  <el-date-picker type="date" placeholder="选择日期" v-model="formInline.updateDate" style="width: 200px;" value-format="yyyy-MM-dd"></el-date-picker>
                </el-col>
              </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="initData(currentPage = 1)">查询</el-button>
                    <el-button @click="resetForm('formInline')">重置</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableContent">
            <table>
                <thead>
                    <tr>
                        <th>姓名</th>
                        <th>工号</th>
                        <th>有薪假类别</th>
                        <th>年度</th>
                        <th>享用开始日期</th>
                        <th>有效截止日期</th>
                        <th>总可用小时数</th>
                        <th>总结余小时数</th>
                        <th>已使用小时数</th>
                        <th>当月可使用小时数</th>
                        <th>当月剩余小时数</th>
                        <th>单位</th>
                        <th>更新时间</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="tableData.length <= 0">
                        <td colspan="13">暂无数据!</td>
                    </tr>
                    <tr v-for="(item,index) in tableData" :key='index' v-else>
                        <td>{{item.name}}</td>
                        <td>{{item.empId}}</td>
                        <td>{{item.holidayType}}</td>
                        <td>{{item.holidayYear}}</td>
                        <td>{{item.validFrom}}</td>
                        <td>{{item.validTo}}</td>
                        <td>{{item.total}}</td>
                        <td>{{item.available}}</td>
                        <td>{{item.used}}</td>
                        <td>{{item.monthTotal}}</td>
                        <td>{{item.monthAvailable}}</td>
                        <td>{{item.uom}}</td>
                        <td>{{item.updateTime}}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- table end -->

        <!-- 分页 start -->
        <div class="paginationBox">
            <el-pagination v-show="total!=0" background layout="total, prev, pager, next, sizes, jumper" :total="total" :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData" @current-change="initData"></el-pagination>
        </div>
        <!-- 分页 end -->

    </div>
</template>
<script>
export default {
    data(){

        return{
            // 头部查询
            formInline: {
                name:'',
                empId:'',
                holidayType:'',
                updateDate:''
            },
            // 表格
            tableData:[],
            // 分页
            currentPage:1,
            pageSize:10,
            total:0,
            dialogTitle:'',
            holidayBalanceId:'',
            holidayTypeOption:[],
            loading: false,
            nameOption:[]
        }
    },
    methods:{
        // 重置
        resetForm(formName) {
          this.$refs[formName].resetFields();
          // 手动清空可能没有被resetFields正确处理的字段
          this.formInline.name = '';
          this.formInline.empId = '';
          this.formInline.holidayType = '';
          this.formInline.updateDate = '';
          this.currentPage = 1;
          this.initData();
        },
        // 渲染数据 分页
        initData(){
            let data = {
                empId: this.formInline.empId,
                name: this.formInline.name,
                holidayType: this.formInline.holidayType,
                updateDate: this.formInline.updateDate,
                workTeam: this.formInline.workTeam,
                offset:this.pageSize,
                rows:this.currentPage,
            }
            this.$http.queryHolidayBalancePage(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.tableData = res.data.data;
                    this.total = res.data.count;

                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },
        holidayTypeList(){
              this.$http.queryHolidayTypes(null,(res)=>{

                  if(res.code == 'SUCCESS'){
                      this.holidayTypeOption = res.data;
                  }else{
                      this.$message.error(res.message);
                  }
              },(errRes)=>{

                  this.$message.error(errRes.message);
              })
        },
        // 班组 下拉选
        teamList() {
          this.$http.queryWorkGroupAll(null, (res) => {
            if (res.code == 'SUCCESS') {
              // this.restaurantsT = res.data.data;
              this.nameOption = res.data.data;
            } else {
              this.$message.error(res.message);
            }
          }, (errRes) => {

            this.$message.error(errRes.message);
          })
        },
        },
        mounted(){
            this.teamList();
            this.initData();
            this.holidayTypeList();
        }
}
</script>
