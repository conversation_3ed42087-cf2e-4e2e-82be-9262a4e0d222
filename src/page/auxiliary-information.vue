<template>
    <div class="auxiliaryBox">
        <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>考勤设置</el-breadcrumb-item>
            <el-breadcrumb-item>辅助基础信息</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- table start -->
        <div class="tableContent">
            <table>
                <thead>
                    <tr>
                        <th>信息名称</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="tableData.length <= 0">
                        <td colspan="2">暂无数据!</td>
                    </tr>
                    <tr v-for="(item,index) in tableData" :key='index' v-else>
                        <td>{{item.name}}</td>
                        <td>
                            <el-button type="text" @click="handleLook(item.id)">查看</el-button>
                            <el-button v-if="item.isShow == 0" type="text" @click="handleUpdate(item.id)">更新</el-button>
                            <el-button v-if="item.isShow == 0" type="text" @click="handleDownload(item.id)">下载当前版本</el-button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- table end -->

        <!-- 更新dialog start -->
        <el-dialog
            title="导入"
            :visible.sync="dialogImport"
            width="60%"
            :close-on-click-modal='false'
            @close="cancelUpload()"
            >
            <el-form class="demo-ruleForm">
                <el-form-item label="信息导入：">
                    <el-upload
                        class="upload-demo"
                        ref="uploadExcel"
                        :action="uploadExcelAPi"
                        :headers="header"
                        :limit="limitNum"
                        accept=".xlsx"
                        :file-list="fileList"
                        :before-upload="beforeFileUpload"
                        :on-exceed="exceedFile"
                        :on-success="handleFileSuccess"
                        :auto-upload="false"
                        :on-change="handleChange">
                        <el-button slot="trigger" type="text">选择文件</el-button>
                    </el-upload>
                </el-form-item>
                <div class="tips">请使用模板格式导入，模板下载请点击：<span @click="downloadModal()">下载当前版本</span></div>
                <el-form-item>
                    <el-button type="primary" @click="submitUpload()">确定</el-button>
                    <el-button @click="cancelUpload()">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 更新dialog end -->
    </div>
</template>
<script>
var axios = require('axios')// 引用axios
import utils from '../utils/index.js'//引用常用工具文件
export default {
    data(){
        return{
            // 表格
            tableData:[
                {
                    id:1,
                    name:'cost center - 车间',
                    isShow:0
                },
                // {
                //     id:2,
                //     name:'部门'
                // },
                {
                    id:3,
                    name:'职位',
                    isShow:0
                },
                {
                    id:4,
                    name:'财务月',
                    isShow:1
                },
                {
                    id:5,
                    name:'审批人员维护',
                    isShow:1
                },
                {
                  id:6,
                  name:'邮件接收人',
                  isShow:0
                },
                {
                  id:7,
                  name:'产线和VSM对照表',
                  isShow:0
                }
            ],
            // 导入dialog
            dialogImport:false,
            uploadExcelAPi:'',
            limitNum: 1,
            fileList: [],
            header:{
                'Authorization' : localStorage.getItem('Auth-Token')
            },
            id:''
        }
    },
    methods:{
        // 查看
        handleLook(id){
            if(id == 1){
                this.$router.push({ path:'/workshop'})
            }else if(id == 2){
                this.$router.push({ path:'/department'})
            }else if(id == 3){
                this.$router.push({ path:'/position'})
            }else if(id == 4){
                this.$router.push({ path:'/financial'})
            }else if(id == 5){
                this.$router.push({ path:'/approval'})
            }else if(id == 6){
              this.$router.push({ path:'/report-email'})
            }else if(id == 7){
              this.$router.push({ path:'/pl-vsm'})
            }
        },

        // 更新
        handleUpdate(id){
            this.dialogImport = true;
            this.id = id;
            if(id == 1){
                this.uploadExcelAPi = '/api/admin/relationDataController/importCostCenter'
            }else if(id == 2){
                this.uploadExcelAPi = ''
            }else if(id == 3){
                this.uploadExcelAPi = '/api/admin/relationDataController/importPosition'
            }else if(id == 6){
              this.uploadExcelAPi = '/api/admin/reportEmail/import'
            }else if(id == 7){
              this.uploadExcelAPi = '/api/admin/plVsmController/import'
            }
        },
        // 上传文件之前的钩子，参数为上传的文件,若返回 false 或者返回 Promise 且被 reject，则停止上传
        beforeFileUpload (file){
            const extension = file.name.split('.')[1];
            if (extension !== 'xlsx') {
                this.$message.warning('上传模板只能是 xlsx格式!')
                return false
            }else{

            }
        },
        // 文件超出个数限制时的钩子
        exceedFile(files, fileList) {
            this.$message.warning({
                title: '警告',
                message: `只能选择 ${this.limitNum} 个文件，当前共选择了 ${files.length + fileList.length} 个`
            });
        },
        // 上传成功后的钩子
        handleFileSuccess(res, file) {
            if(res.code == 'SUCCESS'){
                this.dialogImport = false;//关闭弹框
                this.$message.success(res.message);
            }else{
                this.$message.error(res.message);
            }
        },
        // 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用
        handleChange(file, fileList) {
            this.fileList = fileList;
        },

        // 确定
        submitUpload(){
            if(this.fileList == ''){
                this.$message.error("请上传文件");
            }else{
                this.$refs.uploadExcel.submit();
                // this.dialogImport = false;
            }
        },
        // 取消
        cancelUpload(){
            this.fileList = [];
            this.dialogImport = false;
        },

        // 下载
        downloadModal(){
            if(this.id == 1){
                this.downloadCostCenter()
            }else if(this.id == 2){

            }else if(this.id == 3){
                this.downloadPosition()
            }else if(this.id == 7){
                this.downloadPlVsm()
            }
        },

        // 下载当前版本
        handleDownload(id){
            if(id == 1){
                this.downloadCostCenter()
            }else if(id == 2){

            }else if(id == 3){
                this.downloadPosition()
            }else if(id == 6){
                this.downloadReportEmail()
            }else if(id == 7){
                this.downloadPlVsm()
            }
        },

        // 下载成本中心信息
        downloadCostCenter(){
            axios({
                url: 'api/admin/relationDataController/downloadCostCenter',
                method: "POST",
                data:null,
                responseType:"blob",
                headers:{
                    // 'Content-Type':'application/json;charset=UTF-8',
                    'Authorization' : localStorage.getItem('Auth-Token')
                },
            }).then(function (res) {
                if(!res){
                    return
                }
                utils.downloadExcel(res.data,'成本中心信息导出 ','xlsx')//调用公共方法
            })
        },

        // 下载职位信息
        downloadPosition(){
            axios({
                url: 'api/admin/relationDataController/downloadPosition',
                method: "POST",
                data:null,
                responseType:"blob",
                headers:{
                    // 'Content-Type':'application/json;charset=UTF-8',
                    'Authorization' : localStorage.getItem('Auth-Token')
                },
            }).then(function (res) {
                if(!res){
                    return
                }
                utils.downloadExcel(res.data,'职位信息导出 ','xlsx')//调用公共方法
            })
        },

        // 下载邮件接收人
        downloadReportEmail(){
          axios({
            url: 'api/admin/reportEmail/download',
            method: "POST",
            data:null,
            responseType:"blob",
            headers:{
              // 'Content-Type':'application/json;charset=UTF-8',
              'Authorization' : localStorage.getItem('Auth-Token')
            },
          }).then(function (res) {
            if(!res){
              return
            }
            utils.downloadExcel(res.data,'邮件接收人导出 ','xlsx')//调用公共方法
          })
        },

        // 下载产线和VSM对照表
        downloadPlVsm(){
          axios({
            url: 'api/admin/plVsmController/download',
            method: "POST",
            data:null,
            responseType:"blob",
            headers:{
              // 'Content-Type':'application/json;charset=UTF-8',
              'Authorization' : localStorage.getItem('Auth-Token')
            },
          }).then(function (res) {
            if(!res){
              return
            }
            utils.downloadExcel(res.data,'产线和VSM对照表导出 ','xlsx')//调用公共方法
          })
        }


    },
    mounted(){

    }
}
</script>
