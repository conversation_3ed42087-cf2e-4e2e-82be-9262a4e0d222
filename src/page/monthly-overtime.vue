<template>
    <div class="monthlyBox">
       <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>报告数据查询</el-breadcrumb-item>
            <el-breadcrumb-item>月加班汇总报告</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
                <el-form-item label="年：" class="weekday">
                    <el-date-picker
                        v-model="formInline.startYear"
                        type="year"
                        placeholder="请选择年份"
                        value-format="yyyy"
                        :clearable = false>
                    </el-date-picker>
                    <!-- <el-form-item prop="startYear">
                        <el-date-picker
                            v-model="formInline.startYear"
                            type="year"
                            :picker-options="pickerOptionsStart"
                            placeholder="开始年份"
                            :clearable = false>
                        </el-date-picker>
                    </el-form-item>
                    <span>~</span>
                    <el-form-item prop="endYear" class="endWeek">
                        <el-date-picker
                            v-model="formInline.endYear"
                            type="year"
                            :picker-options="pickerOptionsEnd"
                            placeholder="结束年份"
                            :clearable = false>
                        </el-date-picker>
                    </el-form-item> -->
                </el-form-item>
                <el-form-item label="plant：" prop="plant">
                    <el-select v-model="formInline.plant" clearable placeholder="请选择plant">
                        <el-option
                            v-for="item in plantOption"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="Workshop：" prop="workshop">
                    <el-select v-model="formInline.workshop" clearable placeholder="请选择Workshop" :disabled="disableWork" @change="handleSelect" @clear="setValueWork">
                        <el-option
                            v-for="item in workOption"
                            :key="item.plant + item.workshop"
                            :label="item.workshop"
                            :value="item.workshop">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="VSM：" prop="vsm">
                    <el-select v-model="formInline.vsm" clearable placeholder="请选择VSM" :disabled="disableVsm" @change="handleSelect" @clear="setValueVsm">
                        <el-option
                            v-for="item in vsmOption"
                            :key="item.plant + item.workshop"
                            :label="item.workshop"
                            :value="item.workshop">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="initData" v-loading.fullscreen.lock="fullscreenLoading">查询</el-button>
                    <el-button @click="resetForm('formInline')">重置</el-button>
                    <el-button type="primary" @click="importBtn">导入</el-button>
                    <el-button type="primary" @click="exportBtn">导出</el-button>
                </el-form-item>
                <br/>
                <!-- <el-form-item prop="isWorkshop" class="isRadio">
                    <el-radio-group v-model="formInline.isWorkshop">
                        <el-radio-button label="2">By VSM</el-radio-button>
                        <el-radio-button label="1">By workshop</el-radio-button>
                    </el-radio-group>
                </el-form-item> -->
            </el-form>
        </div>
        <!-- search end -->

        <!-- echart start -->
        <div v-for="(item,index) in tableData" :key='index'>
            <div :id="'chartLeader_'+index" style="width:100%;height:400px;"></div>
            <div :id="'chartOperator_'+index" style="width:100%;height:400px;"></div>
            <div :id="'chartTechnician_'+index" style="width:100%;height:400px;"></div>
        </div>
        <!-- echart end -->

        <!-- 分页 start -->
        <!-- <div class="paginationBox">
            <el-pagination v-show="total!=0" background layout="total, prev, pager, next, sizes, jumper" :page-sizes="pageSizes" :total="total" :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData" @current-change="initData"></el-pagination>
        </div> -->
        <!-- 分页 end -->
  
        <!-- 导入dialog start -->
        <el-dialog
            title="导入"
            :visible.sync="dialogImport"
            width="60%"
            :close-on-click-modal='false'
            @close="cancelUpload()"
            >
            <el-form class="demo-ruleForm">
                <el-form-item label="月加班汇总报告：">
                    <el-upload
                        class="upload-demo"
                        ref="uploadExcel"
                        action="/api/admin/reportOvertimeController/importOvertimeDetail"
                        :headers="header"
                        :limit="limitNum"
                        accept=".XLS,.xlsx"
                        :file-list="fileList"
                        :before-upload="beforeFileUpload"
                        :on-exceed="exceedFile"
                        :on-success="handleFileSuccess"
                        :auto-upload="false"
                        :on-change="handleChange">
                        <el-button slot="trigger" type="text">选择文件</el-button>
                        <div slot="tip" class="el-upload__tip el-icon-info">导入文件格式为Excel</div>
                    </el-upload>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitUpload()">确定</el-button>
                    <el-button @click="cancelUpload()">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 导入dialog end -->
    </div>
</template>
<script>
// import echarts from 'echarts'
const echarts = require('echarts');
var axios = require('axios')// 引用axios
import utils from '../utils/index.js'//引用常用工具文件
export default {
    data(){
        return{
            fullscreenLoading: false,
            // 头部查询
            formInline: {
                startYear:'',
                // endYear:'',
                // isWorkshop:'2'
                plant:'',
                workshop:'',
                vsm:''
            },
            plantOption:[],
            workOption:[],
            vsmOption:[],
            isWorkshop:'',
            disableWork:false,
            disableVsm:false,
            // pickerOptionsStart: { //disabledDate 文档上：设置禁用状态，参数为当前日期，要求返回 Boolean
            //     disabledDate: time => {
            //         let endYearVal = this.formInline.endYear;
            //         if (endYearVal) {
            //             return time.getTime() > new Date(endYearVal).getTime();
            //         }
            //     }
            // },
            // pickerOptionsEnd: {
            //     disabledDate: time => { //disabledDate 文档上：设置禁用状态，参数为当前日期，要求返回 Boolean
            //         let beginYearVal = this.formInline.startYear;
            //         if (beginYearVal) {
            //             return time.getTime() < new Date(beginYearVal).getTime();
            //         }
            //     },
            // },
            tableData:[],
            // 分页
            currentPage:1,
            pageSize:1,
            total:0,
            pageSizes:[2,4],
            // 导入dialog
            dialogImport:false,
            limitNum: 1,
            fileList: [],
            header:{
                'Authorization' : localStorage.getItem('Auth-Token')
            },
            picList:[]
        }
    },
    methods:{
        // 重置
        resetForm(formName){
            this.$refs[formName].resetFields();
            this.disableWork = false,
            this.disableVsm = false,
            this.initData();
        },

        // 渲染数据 分页
        initData(){
            if((this.formInline.startYear == '') || (this.formInline.plant == '')){
                this.$message.error('请同时输入年份以及plant！');
                return
            }
            var workShop = '';
            var vsm = '';
            if(this.isWorkshop == 1){
                workShop = this.formInline.workshop;
                vsm = '';
            }else if(this.isWorkshop == 2){
                workShop = '';
                vsm = this.formInline.vsm;
            }
            this.fullscreenLoading = true;
            let data = {
                year:this.formInline.startYear,
                offset:this.pageSize,
                rows:this.currentPage,
                isWorkshop:this.isWorkshop,
                plant:this.formInline.plant,
                workshop:workShop,
                vsm:vsm
            }
            this.picList = []
            this.$http.queryReportOvertimePage(data,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    this.fullscreenLoading = false;
                    
                    this.tableData = res.data.data;
                    this.total = res.data.count;
                
                    for(let i = 0;i<res.data.data.length;i++){
                        this.getChartLeader(res.data.data[i].Leader,i);
                        this.getChartOperator(res.data.data[i].Operator,i);
                        this.getChartTechnician(res.data.data[i].Technician,i);
                    }
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // 导出
        exportBtn(){
            var workShop = '';
            var vsm = '';
            if(this.isWorkshop == 1){
                workShop = this.formInline.workshop;
                vsm = '';
            }else if(this.isWorkshop == 2){
                workShop = '';
                vsm = this.formInline.vsm;
            }
            let data = {
                year:this.formInline.startYear,
                offset:this.pageSize,
                rows:this.currentPage,
                isWorkshop:this.isWorkshop,
                plant:this.formInline.plant,
                workshop:workShop,
                vsm:vsm,
                imgUrls:this.picList
            }
            axios({
                url: 'api/admin/reportOvertimeController/exportOvertimeReport',
                method: "POST",
                data:data,
                responseType:"blob",
                headers:{
                    // 'Content-Type':'application/json;charset=UTF-8',
                    'Authorization' : localStorage.getItem('Auth-Token')
                },
            }).then(function (res) {
                if(!res){
                    return
                }
                utils.downloadExcel(res.data,'月加班汇总报告 ','xlsx')//调用公共方法
            })
        },

        // Leader
        getChartLeader(item,i){
            var dataList = item.dataList;
            var oneList = [],
            twoList = [],
            threeList = [],
            averageList = [],
            xList = [];
            for(let j = 0;j<dataList.length;j++){
                oneList.push(dataList[j].oneTime);
                twoList.push(dataList[j].twoTime);
                threeList.push(dataList[j].threeTime);
                averageList.push(dataList[j].average);
                xList.push(dataList[j].yearMonth);
            }

            var sumArr = {oneList,twoList,threeList}; 
            var sumResult = [];  
            for(var key in sumArr){
                //遍历数组的每一项
                sumArr[key].forEach((value,index) => {
                    if(sumResult[index] == null ||  sumResult[index] == ""){
                        sumResult[index] = 0 ;
                    }
                    sumResult[index] += parseFloat(value) ;		
                })		
            }

            var mOneList = [],
            mTwoList = [],
            mThreeList = [];
            for(let m = 0;m<sumResult.length;m++){
                let mOne,mTwo,mThree;
                if(sumResult[m] == 0){
                    mOne = 0;
                    mTwo = 0;
                    mThree = 0;
                }else{
                    mOne = Math.round((oneList[m]/sumResult[m])*100);
                    mTwo = Math.round((twoList[m]/sumResult[m])*100);
                    mThree = Math.round((threeList[m]/sumResult[m])*100);
                }
                
                mOneList.push(mOne);
                mTwoList.push(mTwo);
                mThreeList.push(mThree);
            }
            
            this.$nextTick(() => {
                let myChart = echarts.init(document.getElementById('chartLeader_'+i));
                let option = {
                    animation: false,
                    // 标题
                    title: { 
                        text: item.plant+"    "+item.workshop,
                        left:'center'
                    },
                    legend: {
                        // left:'center',
                        top:'40',
                        data:['Leader OT 1.5','Leader OT 2.0','Leader OT 3.0','Leader Average OT']
                    },
                    color:['#dcb5ff','#a6ffa8','#ffaf60','#46a3ff'],
                    // 直角坐标系内绘图网格 
                    grid:{
                        top:'80',
                        containLabel: true//grid 区域是否包含坐标轴的刻度标签。
                    },
                    // 提示
                    tooltip: {
                        //触发类型,axis:坐标轴触发，主要在柱状图，折线图等会使用类目轴的图表中使用。
                        trigger: "axis",
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效，默认为直线'
                            type: "shadow"
                        }
                    }, 
                    //直角坐标系 grid 中的 x 轴
                    xAxis: [
                        {
                            //'category' 类目轴
                            type: "category",
                            boundaryGap:true,
                            axisLabel: {
                                interval:0,//0表示强制显示所有标签，设置为1的话，隔一个标签显示一个标签，以此类推
                                rotate:45,//代表逆时针旋转45度
                            },
                            //坐标数据
                            data: xList
                        }
                    ],
                    //直角坐标系 grid 中的 y 轴
                    yAxis: [
                        {
                            //'value' 数值轴，适用于连续数据
                            type: "value",
                            // 坐标轴轴线相关设置
                            axisLine: {
                                show:false
                            },
                            // 坐标轴刻度相关设置
                            axisTick : {
                                show:false
                            },
                        },
                        {
                            //'value' 数值轴，适用于连续数据
                            type: "value",
                            // 坐标轴轴线相关设置
                            axisLine: {
                                show:false
                            },
                            // 坐标轴刻度相关设置
                            axisTick : {
                                show:false
                            },
                            axisLabel: {
                                formatter: '{value} %'
                            }
                        }
                    ],
                    //系列列表。每个系列通过 type 决定自己的图表类型
                    series: [
                        {
                            type:'bar',
                            name:'Leader OT 1.5',
                            stack: 'Leader',
                            label: {//图形上的文本标签
                                normal: {
                                    show: true,
                                    position: 'inside',
                                    color:'#333333',
                                    formatter: function (params) {
                                        if (params.value > 0) {
                                            return params.value+'%';
                                        } else {
                                            return '';
                                        }
                                    }
                                }
                            },
                            // data:oneList
                            data:mOneList
                        },
                        {
                            type:'bar',
                            name:'Leader OT 2.0',
                            stack: 'Leader',
                            label: {//图形上的文本标签
                                normal: {
                                    show: true,
                                    position: 'inside',
                                    color:'#333333',
                                    formatter: function (params) {
                                        if (params.value > 0) {
                                            return params.value+'%';
                                        } else {
                                            return '';
                                        }
                                    }
                                }
                            },
                            // data:twoList
                            data:mTwoList
                        },
                        {
                            type:'bar',
                            name:'Leader OT 3.0',
                            stack: 'Leader',
                            label: {//图形上的文本标签
                                normal: {
                                    show: true,
                                    position: 'inside',
                                    color:'#333333',
                                    formatter: function (params) {
                                        if (params.value > 0) {
                                            return params.value+'%';
                                        } else {
                                            return '';
                                        }
                                    }
                                }
                            },
                            // data:threeList
                            data:mThreeList
                        },
                        {
                            type:'line',
                            name:'Leader Average OT',
                            yAxisIndex: 1,
                            symbol: "none",
                            label: {//图形上的文本标签
                                normal: {
                                    show: true,
                                    position: 'top',
                                    color:'#333333',
                                    formatter: function (params) {
                                        if (params.value > 0) {
                                            return params.value;
                                        } else {
                                            return '';
                                        }
                                    }
                                }
                            },
                            data:averageList
                        },
                    ]
                }
                myChart.setOption(option); 
                this.picList.push(myChart.getDataURL())  
            })
        },

        // Operator
        getChartOperator(item,i){
            var dataList = item.dataList;
            var oneList = [],
            twoList = [],
            threeList = [],
            averageList = [],
            xList = [];
            for(let j = 0;j<dataList.length;j++){
                oneList.push(dataList[j].oneTime);
                twoList.push(dataList[j].twoTime);
                threeList.push(dataList[j].threeTime);
                averageList.push(dataList[j].average);
                xList.push(dataList[j].yearMonth);
            }

            var sumArr = {oneList,twoList,threeList}; 
            var sumResult = [];  
            for(var key in sumArr){
                //遍历数组的每一项
                sumArr[key].forEach((value,index) => {
                    if(sumResult[index] == null ||  sumResult[index] == ""){
                        sumResult[index] = 0 ;
                    }
                    sumResult[index] += parseFloat(value) ;		
                })		
            }

            var mOneList = [],
            mTwoList = [],
            mThreeList = [];
            for(let m = 0;m<sumResult.length;m++){
                let mOne,mTwo,mThree;
                if(sumResult[m] == 0){
                    mOne = 0;
                    mTwo = 0;
                    mThree = 0;
                }else{
                    mOne = Math.round((oneList[m]/sumResult[m])*100);
                    mTwo = Math.round((twoList[m]/sumResult[m])*100);
                    mThree = Math.round((threeList[m]/sumResult[m])*100);
                }
                
                mOneList.push(mOne);
                mTwoList.push(mTwo);
                mThreeList.push(mThree);
            }
            
            this.$nextTick(() => {
                let myChart = echarts.init(document.getElementById('chartOperator_'+i));
                let option = {
                    animation: false,
                    // 标题
                    title: { 
                        text: item.plant+"    "+item.workshop,
                        left:'center'
                    },
                    legend: {
                        // left:'center',
                        top:'40',
                        data:['Operator OT 1.5','Operator OT 2.0','Operator OT 3.0','Operator Average OT']
                    },
                    color:['#dcb5ff','#a6ffa8','#ffaf60','#46a3ff'],
                    // 直角坐标系内绘图网格 
                    grid:{
                        top:'80',
                        containLabel: true//grid 区域是否包含坐标轴的刻度标签。
                    },
                    // 提示
                    tooltip: {
                        //触发类型,axis:坐标轴触发，主要在柱状图，折线图等会使用类目轴的图表中使用。
                        trigger: "axis",
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效，默认为直线'
                            type: "shadow"
                        }
                    }, 
                    //直角坐标系 grid 中的 x 轴
                    xAxis: [
                        {
                            //'category' 类目轴
                            type: "category",
                            boundaryGap:true,
                            axisLabel: {
                                interval:0,//0表示强制显示所有标签，设置为1的话，隔一个标签显示一个标签，以此类推
                                rotate:45,//代表逆时针旋转45度
                            },
                            //坐标数据
                            data: xList
                        }
                    ],
                    //直角坐标系 grid 中的 y 轴
                    yAxis: [
                        {
                            //'value' 数值轴，适用于连续数据
                            type: "value",
                            // 坐标轴轴线相关设置
                            axisLine: {
                                show:false
                            },
                            // 坐标轴刻度相关设置
                            axisTick : {
                                show:false
                            },
                        },
                        {
                            //'value' 数值轴，适用于连续数据
                            type: "value",
                            // 坐标轴轴线相关设置
                            axisLine: {
                                show:false
                            },
                            // 坐标轴刻度相关设置
                            axisTick : {
                                show:false
                            },
                            axisLabel: {
                                formatter: '{value} %'
                            }
                        }
                    ],
                    //系列列表。每个系列通过 type 决定自己的图表类型
                    series: [
                        {
                            type:'bar',
                            name:'Operator OT 1.5',
                            stack: 'Operator',
                            label: {//图形上的文本标签
                                normal: {
                                    show: true,
                                    position: 'inside',
                                    color:'#333333',
                                    formatter: function (params) {
                                        if (params.value > 0) {
                                            return params.value+'%';
                                        } else {
                                            return '';
                                        }
                                    }
                                }
                            },
                            // data:oneList
                            data:mOneList
                        },
                        {
                            type:'bar',
                            name:'Operator OT 2.0',
                            stack: 'Operator',
                            label: {//图形上的文本标签
                                normal: {
                                    show: true,
                                    position: 'inside',
                                    color:'#333333',
                                    formatter: function (params) {
                                        if (params.value > 0) {
                                            return params.value+'%';
                                        } else {
                                            return '';
                                        }
                                    }
                                }
                            },
                            // data:twoList
                            data:mTwoList
                        },
                        {
                            type:'bar',
                            name:'Operator OT 3.0',
                            stack: 'Operator',
                            label: {//图形上的文本标签
                                normal: {
                                    show: true,
                                    position: 'inside',
                                    color:'#333333',
                                    formatter: function (params) {
                                        if (params.value > 0) {
                                            return params.value+'%';
                                        } else {
                                            return '';
                                        }
                                    }
                                }
                            },
                            // data:threeList
                            data:mThreeList
                        },
                        {
                            type:'line',
                            name:'Operator Average OT',
                            yAxisIndex: 1,
                            symbol: "none",
                            label: {//图形上的文本标签
                                normal: {
                                    show: true,
                                    position: 'top',
                                    color:'#333333',
                                    formatter: function (params) {
                                        if (params.value > 0) {
                                            return params.value;
                                        } else {
                                            return '';
                                        }
                                    }
                                }
                            },
                            data:averageList
                        },
                    ]
                }
                myChart.setOption(option);  
                this.picList.push(myChart.getDataURL()) 
            })
        },

        // Technician
        getChartTechnician(item,i){
            var dataList = item.dataList;
            var oneList = [],
            twoList = [],
            threeList = [],
            averageList = [],
            xList = [];
            for(let j = 0;j<dataList.length;j++){
                oneList.push(dataList[j].oneTime);
                twoList.push(dataList[j].twoTime);
                threeList.push(dataList[j].threeTime);
                averageList.push(dataList[j].average);
                xList.push(dataList[j].yearMonth);
            }

            var sumArr = {oneList,twoList,threeList}; 
            var sumResult = [];  
            for(var key in sumArr){
                //遍历数组的每一项
                sumArr[key].forEach((value,index) => {
                    if(sumResult[index] == null ||  sumResult[index] == ""){
                        sumResult[index] = 0 ;
                    }
                    sumResult[index] += parseFloat(value) ;		
                })		
            }

            var mOneList = [],
            mTwoList = [],
            mThreeList = [];
            for(let m = 0;m<sumResult.length;m++){
                let mOne,mTwo,mThree;
                if(sumResult[m] == 0){
                    mOne = 0;
                    mTwo = 0;
                    mThree = 0;
                }else{
                    mOne = Math.round((oneList[m]/sumResult[m])*100);
                    mTwo = Math.round((twoList[m]/sumResult[m])*100);
                    mThree = Math.round((threeList[m]/sumResult[m])*100);
                }
                
                mOneList.push(mOne);
                mTwoList.push(mTwo);
                mThreeList.push(mThree);
            }
            
            this.$nextTick(() => {
                let myChart = echarts.init(document.getElementById('chartTechnician_'+i));
                let option = {
                    animation: false,
                    // 标题
                    title: { 
                        text: item.plant+"    "+item.workshop,
                        left:'center'
                    },
                    legend: {
                        // left:'center',
                        top:'40',
                        data:['Technician OT 1.5','Technician OT 2.0','Technician OT 3.0','Technician Average OT']
                    },
                    color:['#dcb5ff','#a6ffa8','#ffaf60','#46a3ff'],
                    // 直角坐标系内绘图网格 
                    grid:{
                        top:'80',
                        containLabel: true//grid 区域是否包含坐标轴的刻度标签。
                    },
                    // 提示
                    tooltip: {
                        //触发类型,axis:坐标轴触发，主要在柱状图，折线图等会使用类目轴的图表中使用。
                        trigger: "axis",
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效，默认为直线'
                            type: "shadow"
                        }
                    }, 
                    //直角坐标系 grid 中的 x 轴
                    xAxis: [
                        {
                            //'category' 类目轴
                            type: "category",
                            boundaryGap:true,
                            axisLabel: {
                                interval:0,//0表示强制显示所有标签，设置为1的话，隔一个标签显示一个标签，以此类推
                                rotate:45,//代表逆时针旋转45度
                            },
                            //坐标数据
                            data: xList
                        }
                    ],
                    //直角坐标系 grid 中的 y 轴
                    yAxis: [
                        {
                            //'value' 数值轴，适用于连续数据
                            type: "value",
                            // 坐标轴轴线相关设置
                            axisLine: {
                                show:false
                            },
                            // 坐标轴刻度相关设置
                            axisTick : {
                                show:false
                            },
                        },
                        {
                            //'value' 数值轴，适用于连续数据
                            type: "value",
                            // 坐标轴轴线相关设置
                            axisLine: {
                                show:false
                            },
                            // 坐标轴刻度相关设置
                            axisTick : {
                                show:false
                            },
                            axisLabel: {
                                formatter: '{value} %'
                            }
                        }
                    ],
                    //系列列表。每个系列通过 type 决定自己的图表类型
                    series: [
                        {
                            type:'bar',
                            name:'Technician OT 1.5',
                            stack: 'Technician',
                            label: {//图形上的文本标签
                                normal: {
                                    show: true,
                                    position: 'inside',
                                    color:'#333333',
                                    formatter: function (params) {
                                        if (params.value > 0) {
                                            return params.value+'%';
                                        } else {
                                            return '';
                                        }
                                    }
                                }
                            },
                            // data:oneList
                            data:mOneList
                        },
                        {
                            type:'bar',
                            name:'Technician OT 2.0',
                            stack: 'Technician',
                            label: {//图形上的文本标签
                                normal: {
                                    show: true,
                                    position: 'inside',
                                    color:'#333333',
                                    formatter: function (params) {
                                        if (params.value > 0) {
                                            return params.value+'%';
                                        } else {
                                            return '';
                                        }
                                    }
                                }
                            },
                            // data:twoList
                            data:mTwoList
                        },
                        {
                            type:'bar',
                            name:'Technician OT 3.0',
                            stack: 'Technician',
                            label: {//图形上的文本标签
                                normal: {
                                    show: true,
                                    position: 'inside',
                                    color:'#333333',
                                    formatter: function (params) {
                                        if (params.value > 0) {
                                            return params.value+'%';
                                        } else {
                                            return '';
                                        }
                                    }
                                }
                            },
                            // data:threeList
                            data:mThreeList
                        },
                        {
                            type:'line',
                            name:'Technician Average OT',
                            yAxisIndex: 1,
                            symbol: "none",
                            label: {//图形上的文本标签
                                normal: {
                                    show: true,
                                    position: 'top',
                                    color:'#333333',
                                    formatter: function (params) {
                                        if (params.value > 0) {
                                            return params.value;
                                        } else {
                                            return '';
                                        }
                                    }
                                }
                            },
                            data:averageList
                        },
                    ]
                }
                myChart.setOption(option);  
                this.picList.push(myChart.getDataURL()) 
            })
        },

        // 导入
        importBtn(){
            this.dialogImport = true;
        },
        // 上传文件之前的钩子，参数为上传的文件,若返回 false 或者返回 Promise 且被 reject，则停止上传
        beforeFileUpload (file){
            
            const extension = file.name.split('.')[1];
            
            if ((extension !== 'XLS') && (extension !== 'xlsx')) {
                this.$message.warning('上传模板只能是 XLS、xlsx格式!')
                return false
            }else{

            }
        },
        // 文件超出个数限制时的钩子
        exceedFile(files, fileList) {
            this.$message.warning({
                title: '警告',
                message: `只能选择 ${this.limitNum} 个文件，当前共选择了 ${files.length + fileList.length} 个`
            });
        },
        // 上传成功后的钩子
        handleFileSuccess(res, file) {
            if(res.code == 'SUCCESS'){
                this.dialogImport = false;//关闭弹框
                this.initData();
                this.$message.success(res.message);  
            }else{
                this.$message.error(res.message);
            }
        },
        // 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用
        handleChange(file, fileList) {
            this.fileList = fileList;
        },

        // 确定
        submitUpload(){
            if(this.fileList == ''){
                this.$message.error("请上传文件");
            }else{
                this.$refs.uploadExcel.submit();
            }
        },
        // 取消
        cancelUpload(){
            this.fileList = [];
            this.dialogImport = false;
        },

        // 厂区
        plantList(){
            this.$http.queryPlantList(null,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.plantOption = res.data;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },

        // Workshop
        workList(){
            let data = {
                plant:''
            }
            this.$http.queryPlantWorkshopAll(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.workOption = res.data.data;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },

        // VSM
        vsmList(){
            let data = {
                plant:''
            }
            this.$http.queryPlantVSMAll(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.vsmOption = res.data.data;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },

        // 选择Workshop  选择VSM
        handleSelect(){
            if(this.formInline.workshop !=''){
                this.disableVsm = true;
                this.isWorkshop = 1;
            }else{
                this.disableWork = true;
                this.isWorkshop = 2;
            }
        },

        setValueWork(){
            this.formInline.workshop == '';
            this.disableVsm = false;
            this.disableWork = false;
        },
        setValueVsm(){
            this.formInline.vsm == '';
            this.disableVsm = false;
            this.disableWork = false;
        }
    },
    mounted(){
        var datD = new Date(); //当前格林时间 
        var nianD = datD.getFullYear();//当前年份 
        // this.formInline.startYear = nianD.toString();
        this.formInline.startYear = '2020';
        // this.initData();
        this.plantList();
        this.workList();
        this.vsmList();
    }
}
</script>
