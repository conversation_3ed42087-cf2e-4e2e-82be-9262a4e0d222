<template>
  <div class="workforceBox">
    <!-- 面包屑 start -->
    <el-breadcrumb separator="/">
      <el-breadcrumb-item>考勤设置</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/workforce-management' }">排班管理</el-breadcrumb-item>
      <el-breadcrumb-item>批量修改班次</el-breadcrumb-item>
    </el-breadcrumb>
    <!-- 面包屑 end -->

    <!-- search start -->
    <div class="searchFrom">
      <el-form :model="formInline" :inline="true" class="demo-form-inline">
        <el-form-item label="姓名：" prop="name">
          <el-input v-model="formInline.name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="工号：" prop="empId">
          <el-input v-model="formInline.empId" placeholder="请输入工号"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="initData(currentPage = 1)">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button @click="importBtn">导入</el-button>
          <el-button @click="exportBtn">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- search end -->

    <div class="tableContant">
      <el-table :data="tableData" border style="width: 100%" v-loading="loading">
        <!-- <el-table-column fixed prop="plant" label="plant"></el-table-column>
                <el-table-column fixed prop="workshop" label="workshop"></el-table-column> -->
        <el-table-column fixed prop="workTeam" label="班组"></el-table-column>
        <el-table-column fixed prop="employee" label="工号"></el-table-column>
        <el-table-column fixed prop="name" label="姓名"></el-table-column>
        <el-table-column v-for="(item, index) in dateLength" :key="index" :label="item" :prop="item">
          <template slot-scope="scope">
            <span :class="{
              tdActive:
                scope.row[item].isWeek == 0 || scope.row[item].isWeek == 6,
            }" @click="workforceEdit(scope.row, item)">{{ scope.row[item].work_type }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- <div class="tableFixed">
            <table class="tb1_1">
                <thead>
                    <tr v-if="tableData.length > 0">
                        <th>plant</th>
                        <th>workshop</th>
                        <th>班组</th>
                        <th>工号</th>
                        <th>姓名</th>
                    </tr>
                </thead>
            </table>
            <table class="tb2_1">
                <thead>
                    <tr v-if="tableData.length > 0">
                        <th v-for="(m,index2) in dateLength" :key='index2'>{{m.schedule_date}}</th>
                    </tr>
                </thead>
            </table>
            <table class="tb3_1">
                <tbody>
                    <tr v-for="(item,index3) in tableData" :key='index3'>
                        <td>{{item.plant}}</td>
                        <td>{{item.workshop}}</td>
                        <td>{{item.workTeam}}</td>
                        <td>{{item.employee}}</td>
                        <td>{{item.name}}</td>
                    </tr>
                </tbody>
            </table>
            <table class="tb4_1">
                <tbody>
                    <tr v-for="(item,index4) in tableData" :key='index4'>
                        <td :colspan="dateLength.length" v-if="item.dateList == null">&nbsp;&nbsp;&nbsp;</td>
                        <td v-for="(n,index6) in item.dateList" :key='index6' :class="{'tdActive':n.isWeek == 0 || n.isWeek == 6}" @click="workforceEdit(item,n)" v-else>{{n.work_type}}</td>
                    </tr>
                </tbody>
            </table>
        </div> -->

    <!-- 分页 start -->
    <div class="paginationBox">
      <el-pagination v-show="total != 0" background layout="total, prev, pager, next, sizes, jumper" :total="total"
        :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData"
        @current-change="initData"></el-pagination>
    </div>
    <!-- 分页 end -->

    <!-- 编辑dialog start -->
    <el-dialog title="编辑" :visible.sync="dialogEdit" width="60%" :close-on-click-modal="false"
      @close="resetForm('ruleForm')">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px" class="demo-ruleForm">
        <el-form-item label="日期：">
          {{ ruleForm.dateTime }}
        </el-form-item>
        <el-form-item label="班次：" prop="shift">
          <el-select v-model="ruleForm.shift" filterable placeholder="请选择班次" style="width: 100%">
            <el-option v-for="item in shiftOptions" :key="item.id" :label="item.name" :value="item.name">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm('ruleForm')">确定</el-button>
          <el-button @click="resetForm('ruleForm')">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 编辑dialog end -->

    <!-- 导入dialog start -->
    <el-dialog title="导入" :visible.sync="dialogImport" width="60%" :close-on-click-modal="false" @close="cancelUpload()">
      <el-form label-width="150px" class="demo-ruleForm">
        <el-form-item label="批量修改班次：">
          <el-upload class="upload-demo" ref="uploadExcel" action="/api/admin/scheduleController/importEmployeeSchedule"
            :headers="header" :limit="limitNum" accept=".XLS,.xlsx" :file-list="fileList"
            :before-upload="beforeFileUpload" :on-exceed="exceedFile" :on-success="handleFileSuccess" :auto-upload="false"
            :on-change="handleChange">
            <el-button slot="trigger" type="text">选择文件</el-button>
            <div slot="tip" class="el-upload__tip el-icon-info">
              导入文件格式为Excel
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitUpload()">确定</el-button>
          <el-button @click="cancelUpload()">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 导入dialog end -->
  </div>
</template>
<script>
var axios = require("axios"); // 引用axios
import utils from "../utils/index.js"; //引用常用工具文件
import dayjs from "dayjs";
export default {
  data() {
    // 班次验证
    var checkShift = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请选择班次!"));
      } else {
        callback();
      }
    };
    return {
      formInline: {
        name: '',
        empId: ''
      },

      // 表格
      tableData: [],
      // 分页
      currentPage: 1,
      pageSize: 10,
      total: 0,
      dialogEdit: false, //编辑dialog
      ruleForm: {
        dateTime: "",
        shift: "",
      },
      rules: {
        shift: [{ validator: checkShift, trigger: "blur" }],
      },
      shiftOptions: [],
      plant: "",
      workTeam: "",
      workshop: "",
      fmonth: "",
      empId: "",
      // 导入dialog
      dialogImport: false,
      limitNum: 1,
      fileList: [],
      header: {
        Authorization: localStorage.getItem("Auth-Token"),
      },
      dateLength: [],
      loading: false,
    };
  },
  methods: {
    // 渲染数据 分页
    initData() {
      this.tableData = [];
      this.dateLength = [];
      this.loading = true;
      let data = {
        month: this.fmonth.split("-")[1],
        plant: this.plant,
        workTeam: this.workTeam,
        workshop: this.workshop,
        year: this.fmonth.split("-")[0],
        offset: this.pageSize,
        rows: this.currentPage,
        name: this.formInline.name,
        empId: this.formInline.empId
      };

      let myYear = data.year;
      let myMonth = data.month;
      let startDate = "";
      let endDate = "";
      if (data.month == 1) {
        myYear -= 1;
        myMonth = 12;
        startDate = myYear + "-" + myMonth + "-" + "16";
        endDate = data.year + "-" + data.month + "-" + "16";
      } else {
        startDate = data.year + "-" + (data.month - 1) + "-" + "16";
        endDate = data.year + "-" + data.month + "-" + "16";
      }

      let currentDay = startDate;
      let dateArray = [];
      do {
        dateArray.push(dayjs(currentDay).format("YYYY-MM-DD"));
        currentDay = dayjs(currentDay).add(1, "day");
      } while (dayjs(currentDay).isBefore(dayjs(endDate)));
      this.dateLength = dateArray;
      this.$http.queryEmployeeSchedulePage(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            this.loading = false;
            if (res.data.data) {
              var resData = res.data.data;
              var lengthArr = []; //每个dateList
              for (let i = 0; i < resData.length; i++) {
                if (resData[i].dateList != null) {
                  lengthArr.push(resData[i].dateList); //日期

                  // 排班标识
                  for (let j = 0; j < resData[i].dateList.length; j++) {
                    var scheduleDate = resData[i].dateList[j].schedule_date;
                    var isWeek = new Date(scheduleDate).getDay(); //0-周日，6-周六
                    resData[i].dateList[j]["isWeek"] = isWeek;

                    resData[i][scheduleDate] = {
                      work_type: resData[i].dateList[j].work_type,
                      isWeek: isWeek,
                    };
                  }
                }
              }
              this.tableData = resData;
              this.total = res.data.count;
            }
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.loading = false;
          this.$message.error(errRes.message);
        }
      );
    },

    // 导入
    importBtn() {
      this.dialogImport = true;
    },
    // 上传文件之前的钩子，参数为上传的文件,若返回 false 或者返回 Promise 且被 reject，则停止上传
    beforeFileUpload(file) {
      const extension = file.name.split(".")[1];

      if (extension !== "XLS" && extension !== "xlsx") {
        this.$message.warning("上传模板只能是 XLS、xlsx格式!");
        return false;
      } else {
      }
    },
    // 文件超出个数限制时的钩子
    exceedFile(files, fileList) {
      this.$message.warning({
        title: "警告",
        message: `只能选择 ${this.limitNum} 个文件，当前共选择了 ${files.length + fileList.length
          } 个`,
      });
    },
    // 上传成功后的钩子
    handleFileSuccess(res, file) {
      if (res.code == "SUCCESS") {
        this.dialogImport = false; //关闭弹框
        this.initData();
        this.$message.success(res.message);
      } else {
        this.$message.error(res.message);
      }
    },
    // 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用
    handleChange(file, fileList) {
      this.fileList = fileList;
    },

    // 确定
    submitUpload() {
      if (this.fileList == "") {
        this.$message.error("请上传文件");
      } else {
        this.$refs.uploadExcel.submit();
      }
    },
    // 取消
    cancelUpload() {
      this.fileList = [];
      this.dialogImport = false;
    },

    // 导出
    exportBtn() {
      let data = {
        month: this.fmonth.split("-")[1],
        plant: this.plant,
        workTeam: this.workTeam,
        workshop: this.workshop,
        year: this.fmonth.split("-")[0],
        offset: this.pageSize,
        rows: this.currentPage,
      };
      axios({
        url: "api/admin/scheduleController/downloadEmployeeSchedule",
        method: "POST",
        data: data,
        responseType: "blob",
        headers: {
          Authorization: localStorage.getItem("Auth-Token"),
        },
      }).then(function (res) {
        if (!res) {
          return;
        }
        utils.downloadExcel(res.data, "批量修改班次", "xlsx"); //调用公共方法
      });
    },

    // 班次 下拉选
    shiftList() {
      this.$http.queryWorkTypeAll(
        null,
        (res) => {
          if (res.code == "SUCCESS") {
            this.shiftOptions = res.data.data;
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 编辑班次
    workforceEdit(item, n) {
      this.dialogEdit = true;
      this.ruleForm.dateTime = n;
      this.ruleForm.shift = item[n].work_type;
      this.empId = item.employee;
    },

    // 编辑确定
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = {
            scheduleDate: this.ruleForm.dateTime,
            workType: this.ruleForm.shift,
            empId: this.empId,
          };
          this.$http.updateEmployeeSchedule(
            data,
            (res) => {
              if (res.code == "SUCCESS") {
                this.dialogEdit = false;
                this.initData();
                this.$message.success(res.message);
              } else {
                this.$message.error(res.message);
              }
            },
            (errRes) => {
              this.$message.error(errRes.message);
            }
          );
        } else {
          return false;
        }
      });
    },

    // 取消
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.dialogEdit = false;
    },
  },
  mounted() {
    var item = JSON.parse(this.$route.query.item);
    this.plant = item.plant;
    this.workshop = item.workshop;
    this.workTeam = item.workTeam;
    this.fmonth = this.$route.query.fmonth;
    this.initData();
    this.shiftList();
  },
};
</script>
