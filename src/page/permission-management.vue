<template>
    <div class="permissionBox">
       <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>权限管理</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-button type="primary" @click="addBtn">新增权限</el-button>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableContent">
            <table>
                <thead>
                    <tr>
                        <th>权限名称</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="tableData.length <= 0">
                        <td colspan="2">暂无数据!</td>
                    </tr>
                    <tr v-for="(item,index) in tableData" :key='index' v-else>
                        <td>{{item.role_NAME}}</td>
                        <td>
                            <el-button type="text" @click="handleEdit(item.rnumber)">编辑</el-button>
                            <el-button type="text" @click="handleMandate(item.rnumber)">授权</el-button>
                            <el-button type="text" @click="handlePersonnel(item.rnumber)">人员管理</el-button>
                            <el-button type="text" @click="handleDelete(item.rnumber)">删除</el-button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- table end -->

        <!-- 添加权限dialog start -->
        <el-dialog
            :title="dialogTitle == 0 ? '添加权限' : '编辑权限'"
            :visible.sync="dialogAdd"
            width="60%"
            :close-on-click-modal='false'
            @close="resetForm('ruleAddForm')"
            >
            <el-form :model="ruleAddForm" :rules="rulesAdd" ref="ruleAddForm" label-width="120px" class="demo-ruleForm">
                <el-form-item label="权限名称：" prop="name">
                    <el-input v-model="ruleAddForm.name" placeholder="请输入权限名称"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button @click="resetForm('ruleAddForm')">取消</el-button>
                    <el-button type="primary" @click="submitForm('ruleAddForm')">确定</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 添加权限dialog end -->
    </div>
</template>
<script>
export default {
    data(){
        return{
            // 表格
            tableData:[],
            dialogAdd:false,//添加权限dialog
            // 添加权限Form
            ruleAddForm:{
                name:''
            },
            rulesAdd:{
                name: [
                    { required: true, message: '请输入权限名称', trigger: 'blur' }
                ]
            },
            dialogTitle:'',
            rnumber:'',
        }
    },
    methods:{
        // 渲染数据
        initData(){
            this.$http.queryRoleList(null,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    this.tableData = res.data;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // 新建
        addBtn() {
            this.dialogAdd = true;
            this.dialogTitle = 0;
            this.ruleAddForm = {
                name:''
            }
        },

        // table编辑
        handleEdit(rnumber) {
            let data = {
                roleNumber:rnumber
            }
            this.$http.queryRoleDetail(data,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    this.dialogAdd = true;
                    this.dialogTitle = 1;
                    this.ruleAddForm.name = res.data.role_NAME;
                    this.rnumber = res.data.rnumber;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // 添加/编辑班次确定
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if(this.dialogTitle == 0){
                        let data = {
                            roleName: this.ruleAddForm.name
                        }
                        this.$http.addRole(data,(res)=>{
                            
                            if(res.code == 'SUCCESS'){
                                this.dialogAdd = false;
                                this.$refs[formName].resetFields();
                                this.initData()
                                this.$message.success(res.message);
                            }else{
                                this.$message.error(res.message);
                            }
                        },(errRes)=>{
                            
                            this.$message.error(errRes.message);
                        })
                    }else{
                        let data = {
                            roleNumber: this.rnumber,
                            roleName: this.ruleAddForm.name
                        }
                        this.$http.updateRoleName(data,(res)=>{
                            
                            if(res.code == 'SUCCESS'){
                                this.dialogAdd = false;
                                this.$refs[formName].resetFields();
                                this.initData()
                                this.$message.success(res.message);
                            }else{
                                this.$message.error(res.message);
                            }
                        },(errRes)=>{
                            
                            this.$message.error(errRes.message);
                        })
                    }
                } else {
                    return false;
                }
            });
        },

        // 取消
        resetForm(formName){
            this.$refs[formName].resetFields();
            this.dialogAdd = false;
        },

        // table删除
        handleDelete(rnumber) {
            this.$confirm('确认删除？','提示',{
                type: 'warning',
            }).then(() => {
                let data = {
                    roleNumber:rnumber
                }
                this.$http.deleteRole(data,(res)=>{
                    
                    if(res.code == 'SUCCESS'){
                        this.initData();
                        this.$message.success(res.message);
                    }else{
                        this.$message.error(res.message);
                    }
                },(errRes)=>{
                    
                    this.$message.error(errRes.message);
                })
            }).catch(() => {

            });
        },

        // table授权
        handleMandate(rnumber){
            this.$router.push({ path:'/mandate',query:{rnumber:rnumber}})
        },

        // table人员管理
        handlePersonnel(rnumber){
            this.$router.push({ path:'/personnel-management',query:{rnumber:rnumber}})
        }
    },
    mounted(){
        this.initData();
    }
}
</script>
