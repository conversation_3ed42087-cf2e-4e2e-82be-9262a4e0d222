<template>
    <div class="teamBox">
       <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>考勤设置</el-breadcrumb-item>
            <el-breadcrumb-item>班组管理</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
                <!-- <el-form-item label="班组名称：" prop="name">
                    <el-input v-model="formInline.name" placeholder="请输入班组名称"></el-input>
                </el-form-item> -->
                <el-form-item label="班组名称：" prop="name">
                    <el-select v-model="formInline.name" filterable placeholder="请输入班组名称">
                        <el-option
                            v-for="item in nameOption"
                            :key="item.id"
                            :label="item.name"
                            :value="item.name">
                        </el-option>
                    </el-select>
                </el-form-item>
                <!-- <el-form-item label="车间：" prop="workshop">
                    <el-input v-model="formInline.workshop" placeholder="请输入车间"></el-input>
                </el-form-item> -->
                <!-- <el-form-item label="车间：" prop="workshop">
                    <el-select v-model="formInline.workshop" filterable placeholder="请输入车间">
                        <el-option
                            v-for="item in workshopOption"
                            :key="item.workshop +'_'+item.plant"
                            :label="item.workshop"
                            :value="item.workshop">
                        </el-option>
                    </el-select>
                </el-form-item> -->
                <!-- <el-form-item label="产线：" prop="productLine">
                    <el-input v-model="formInline.productLine" placeholder="请输入产线"></el-input>
                </el-form-item> -->
                <!-- <el-form-item label="厂区：" prop="plant">
                    <el-input v-model="formInline.plant" placeholder="请输入厂区"></el-input>
                </el-form-item> -->
                <!-- <el-form-item label="厂区：" prop="plant">
                    <el-select v-model="formInline.plant" placeholder="请选择厂区">
                        <el-option
                            v-for="item in plantOption"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                    </el-select>
                </el-form-item> -->
                <el-form-item>
                    <el-button type="primary" @click="initData(currentPage = 1)">查询</el-button>
                    <el-button type="primary" @click="initBtn">初始化</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableContent">
            <table>
                <thead>
                    <tr>
                        <th>班组名称</th>
                        <th>周期天数</th>
                        <th>上班天数</th>
                        <th>休假天数</th>
                        <!-- <th>车间（领班）</th> -->
                        <!-- <th>产线</th> -->
                        <!-- <th>厂区（领班）</th> -->
                        <th>可选班次</th>
                        <th>不可选班次</th>
                        <th>领班</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="tableData.length <= 0">
                        <td colspan="8">暂无数据!</td>
                    </tr>
                    <tr v-for="(item,index) in tableData" :key='index' v-else>
                        <td>{{item.name}}</td>
                        <td>{{item.cycleDay}}</td>
                        <td>{{item.workDay}}</td>
                        <td>{{item.restDay}}</td>
                        <!-- <td>{{item.workshop}}</td> -->
                        <!-- <td>{{item.productionLine}}</td> -->
                        <!-- <td>{{item.plant}}</td> -->
                        <td>{{item.validWorkType}}</td>
                        <td>{{item.notValidType}}</td>
                        <td>{{item.leaderName}}</td>
                        <td>
                            <el-button type="text" @click="handleEdit(item.id)">编辑</el-button>
                            <!-- <el-button type="text" @click="handleDelete()">删除</el-button> -->
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- table end -->

        <!-- 分页 start -->
        <div class="paginationBox">
            <el-pagination v-show="total!=0" background layout="total, prev, pager, next, sizes, jumper" :total="total" :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData" @current-change="initData"></el-pagination>
        </div>
        <!-- 分页 end -->

        <!-- 编辑班组dialog start -->
        <el-dialog
            title="编辑班组"
            :visible.sync="dialogAdd"
            width="60%"
            :close-on-click-modal='false'
            @close="resetForm('ruleAddForm')"
            >
            <el-form :model="ruleAddForm" :rules="rulesAdd" ref="ruleAddForm" label-width="120px" class="demo-ruleForm">
                <el-form-item label="班组名称：" prop="name">
                    <el-input v-model="ruleAddForm.name" placeholder="请输入班组名称" disabled></el-input>
                </el-form-item>
                <el-form-item label="周期天数：" prop="cycleDay">
                    <el-input v-model="ruleAddForm.cycleDay" placeholder="请输入周期天数" @input="radioSet($event)"></el-input>
                </el-form-item>

                <div v-if="ruleAddForm.cycleDay">
                    <el-form-item 
                        v-for="(item,index) in ruleAddForm.dayLists" 
                        :key='index' 
                        :label="'Day '+(index+1)+'：'" 
                        :prop="'dayLists.' + index + '.isRadio'"
                        :rules="{
                            required: true, message: '请选择工作状态', trigger: 'change'
                        }"
                    >
                        <el-radio-group v-model="item.isRadio">
                            <el-radio :label="0">工作</el-radio>
                            <el-radio :label="1">休假</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </div>

                <!-- <el-form-item label="车间：" prop="workshop">
                    <el-input v-model="ruleAddForm.workshop" placeholder="请输入车间" disabled></el-input>
                </el-form-item> -->
                <!-- <el-form-item label="产线：" prop="productionLine">
                    <el-input v-model="ruleAddForm.productionLine" placeholder="请输入产线"></el-input>
                </el-form-item> -->
                <el-form-item label="可选班次：" prop="validWorkTypeId">
                    <!-- <el-input v-model="ruleAddForm.validWorkTypeId" placeholder="请输入可选班次"></el-input> -->
                    <el-select v-model="ruleAddForm.validWorkTypeId" placeholder="请选择可选班次" multiple @change="selectShift" style="width:100%;">
                        <el-option
                            v-for="item in shiftOptions"
                            :key="item.id"
                            :label="item.name"
                            :value="item.name"
                            :disabled="item.disabled">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="不可选班次：" prop="notValidTypeId">
                    <!-- <el-input v-model="ruleAddForm.notValidTypeId" placeholder="请输入不可选班次"></el-input> -->
                    <el-select v-model="ruleAddForm.notValidTypeId" placeholder="请选择不可选班次" multiple @change="selectNotShift" style="width:100%;">
                        <el-option
                            v-for="item in shiftNotOptions"
                            :key="item.id"
                            :label="item.name"
                            :value="item.name"
                            :disabled="item.disabled">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="领班：" prop="leaderId">
                    <!-- <el-input v-model="ruleAddForm.leaderId" placeholder="请输入领班工号"></el-input> -->
                    <!-- <el-autocomplete
                        v-model="ruleAddForm.leaderId"
                        placeholder="请输入领班工号的4位尾数"
                        :fetch-suggestions="querySearch"
                        :trigger-on-focus="false"
                        @select="handleSelect"
                        @input="handleInput"
                        style="width:100%;"
                        ></el-autocomplete> -->
                        <el-select 
                            v-model="ruleAddForm.leaderId" 
                            filterable 
                            placeholder="请输入领班工号的4位尾数" 
                            :filter-method="filterMethod"
                             style="width:100%;">
                            <el-option
                                v-for="item in leaderOptions"
                                :key="item.empId"
                                :label="item.empId"
                                :value="item.empId">
                            </el-option>
                        </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleAddForm')">确定</el-button>
                    <el-button @click="resetForm('ruleAddForm')">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 编辑班组dialog end -->
    </div>
</template>
<script>
export default {
    data(){
        return{
            // 头部查询
            formInline: {
                name:'',
                // workshop:'',
                // productLine:'',
                // plant:''
            },
            // 表格
            tableData:[],
            // 分页
            currentPage:1,
            pageSize:10,
            total:0,
            dialogAdd:false,//编辑班组dialog
            // 编辑班组Form
            ruleAddForm:{
                name:'',
                cycleDay:'',
                // workshop:'',
                // productionLine:'',
                validWorkTypeId:[],
                notValidTypeId:[],
                leaderId:'',

                dayLists:[]
            },
            rulesAdd:{
                name: [
                    { required: true, message: '请输入班次名称', trigger: 'blur' }
                ],
                cycleDay: [
                    { required: true, message: '请输入周期天数', trigger: 'blur' },
                ],
                leaderId: [
                    { required: true, message: '请选择领班工号', trigger: 'blur,change' },
                ]
            },
            id:'',
            plant:'',
            newShiftArr:[],//班次原始数据
            shiftOptions:[],//可选班次
            shiftNotOptions:[],//不可选班次
            // restaurants: [],//领班模糊查询列表
            leaderOptions:[],
            nameOption:[],//班组名称下拉选
            // workshopOption:[],//车间下拉选
            // plantOption:[],//厂区下拉选
        }
    },
    methods:{
        // 渲染数据 分页
        initData(){
            let data = {
                name: this.formInline.name,
                productionLine: '',
                // workshop: this.formInline.workshop,
                offset:this.pageSize,
                rows:this.currentPage,
                // plant:this.formInline.plant
            }
            this.$http.queryWorkGroupPage(data,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    var resData = res.data.data;
                    for(let i = 0;i<resData.length;i++){
                        if(resData[i].validWorkType != null){
                            resData[i].validWorkType = resData[i].validWorkType.replace('|',',');
                        }
                        if(resData[i].notValidType != null){
                            resData[i].notValidType = resData[i].notValidType.replace('|',',');
                        }
                    }
                    this.tableData = resData;
                    this.total = res.data.count;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // 初始化
        initBtn() {
            this.$http.initWorkGroup(null,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    this.$message.success(res.message);
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // table编辑
        handleEdit(id) {
            let data = {
                id:id
            }
            this.$http.queryWorkGroupDetail(data,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    this.dialogAdd = true;
                    this.ruleAddForm.dayLists = [];
                    // this.plant = res.data.plant;
                    this.ruleAddForm.name = res.data.name;
                    this.ruleAddForm.cycleDay = res.data.cycleDay;
                    // this.ruleAddForm.workshop = res.data.workshop;
                    // this.ruleAddForm.productionLine = res.data.productionLine;
                    this.ruleAddForm.leaderId = res.data.leaderId;
                    this.id = id;
 
                    if(res.data.validWorkType != null){
                        this.ruleAddForm.validWorkTypeId = res.data.validWorkType.split("|");
                    }else{
                        this.ruleAddForm.validWorkTypeId = [];
                    }
                    if(res.data.notValidType != null){
                        this.ruleAddForm.notValidTypeId = res.data.notValidType.split("|");
                    }else{
                        this.ruleAddForm.notValidTypeId = [];
                    }
                    // 可选班次 
                    var shiftsArr = JSON.parse(JSON.stringify(this.newShiftArr));
                    for(let i = 0;i<shiftsArr.length;i++){
                        for(let j = 0;j<this.ruleAddForm.notValidTypeId.length;j++){
                            if(shiftsArr[i].name == this.ruleAddForm.notValidTypeId[j]){
                                shiftsArr[i].disabled = true;                                         
                            }
                        }
                    }
                    this.shiftOptions = shiftsArr;
                    // 不可选班次
                    var noShiftsArr = JSON.parse(JSON.stringify(this.newShiftArr));
                    for(let i = 0;i<noShiftsArr.length;i++){
                        for(let j = 0;j<this.ruleAddForm.validWorkTypeId.length;j++){
                            if(noShiftsArr[i].name == this.ruleAddForm.validWorkTypeId[j]){
                                noShiftsArr[i].disabled = true;                                         
                            }
                        }
                    }
                    this.shiftNotOptions = noShiftsArr;
                    
                    for(let i = 0;i<res.data.cycleDay;i++){
                        this.ruleAddForm.dayLists.push({isRadio:''}) 
                    }
                    // if((res.data.dayFormat != null) && (res.data.dayFormat.includes(",") == true)){
                    if(res.data.dayFormat != null){
                        var dayFormatArray = res.data.dayFormat.split(",")
                        for(let k = 0;k<dayFormatArray.length;k++){
                            this.ruleAddForm.dayLists[k].isRadio = parseInt(dayFormatArray[k]);
                        }
                    }else{
                        var dayFormatArr = [];
                        dayFormatArr.push(res.data.dayFormat);
                        for(let k = 0;k<dayFormatArr.length;k++){
                            this.ruleAddForm.dayLists[k].isRadio = parseInt(dayFormatArr[k]);
                        }
                    }
                    // this.plant = res.data.plant;

                    // this.radioSet();// 周期设置
                    this.leaderList(res.data.leaderId);
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // 编辑班组确定
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    var newArr = [];
                    for(let i = 0;i<this.ruleAddForm.dayLists.length;i++){
                        newArr.push(this.ruleAddForm.dayLists[i].isRadio)
                    }
                    var restDay = []
                    ,workDay = [];
                    for(let j = 0;j<newArr.length;j++){
                        if(newArr[j] == 0){
                            workDay.push(newArr[j])
                        }else if(newArr[j] == 1){
                            restDay.push(newArr[j])
                        }
                    }  
                    let data = {
                        id: this.id,
                        cycleDay: this.ruleAddForm.cycleDay,
                        dayFormat:newArr.join(','),
                        leaderId:this.ruleAddForm.leaderId,
                        notValidType:this.ruleAddForm.notValidTypeId.join('|'),
                        productionLine:'',
                        restDay:restDay.length,
                        validWorkType:this.ruleAddForm.validWorkTypeId.join('|'),
                        workDay:workDay.length
                    }
                    this.$http.updateWorkGroup(data,(res)=>{
                        
                        if(res.code == 'SUCCESS'){
                            this.dialogAdd = false;
                            this.initData()
                            this.$message.success(res.message);
                        }else{
                            this.$message.error(res.message);
                        }
                    },(errRes)=>{
                        
                        this.$message.error(errRes.message);
                    })
                } else {
                    return false;
                }
            });
        },

        // 取消
        resetForm(formName){
            this.$refs[formName].resetFields();
            this.dialogAdd = false;
        },

        // // table删除
        // handleDelete() {
        //     this.$confirm('确认删除？','提示',{
        //         type: 'warning',
        //     }).then(() => {
                
        //     }).catch(() => {

        //     });
        // },

        // 周期设置
        radioSet(e){
            this.ruleAddForm.dayLists = [];
            let boolean = new RegExp("^[1-9][0-9]*$").test(e);
            if(!boolean){
                this.$message.warning("请输入数字值");
                this.ruleAddForm.cycleDay = '';
            }else{
                if(e > 10){
                    this.$message.warning("周期不得超过10");
                    this.ruleAddForm.cycleDay = '';
                }else{
                    for(let i = 0;i<e;i++){
                        this.ruleAddForm.dayLists.push({isRadio:''})
                    }
                }
            }
        },

        // 可选班次 下拉选
        shiftList(){
            this.$http.queryWorkTypeAll(null,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    for(let i = 0;i<res.data.data.length;i++){
                        res.data.data[i]["disabled"] = false;
                    }
                    this.newShiftArr = JSON.parse(JSON.stringify(res.data.data));
                    this.shiftOptions = JSON.parse(JSON.stringify(res.data.data)); 
                    this.shiftNotOptions = JSON.parse(JSON.stringify(res.data.data));
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // 领班 下拉选
        leaderList(val){
            let data = {
                // empId:this.ruleAddForm.leaderId,
                empId:val,
                // workTeam:this.ruleAddForm.name,
                plant:this.plant,
                // workshop:this.ruleAddForm.workshop,
            }
            this.$http.queryEmployeeByWorkTeam(data,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    // this.restaurants = res.data.data;  
                    // if(res.data.data ==  ''){
                    //     this.ruleAddForm.leaderId = '';
                    //     this.$message.error('暂无数据,请确保输入正确！');
                    // }
                    this.leaderOptions = res.data.data;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // // 返回输入建议的方法，仅当你的输入建议数据 resolve 时，通过调用 callback(data:[]) 来返回它
        // querySearch(queryString, cb) {
        //     var restaurants = this.restaurants;
        //     // 因为autocomplete只识别value字段并在下拉列中显示，添加value字段
        //     for(let i=0; i<restaurants.length; i++){
        //         restaurants[i]["value"] = restaurants[i].empId;
        //     }
        //     console.log("777777777777777777777777777777777777")
        //     console.log(this.restaurants)
        //     var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
        //     // 调用 callback 返回建议列表的数据
        //     cb(results);
        // },

        // createFilter(queryString) {
        //     return (restaurant) => {
        //         return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
        //     };
        // },

        // // 在 Input 值改变时触发（选择）
        // handleSelect(item) {
        //     console.log(item);
        // },

        //  // 在 Input 值改变时触发（输入）
        // handleInput(){
        //     this.leaderList();
        // },

        // select 领班
        filterMethod(query){
            if(query.length >= 4){
                this.leaderList(query);
            }
        },

        // select 可选班次
        selectShift(val){
            this.shiftNotOptions = JSON.parse(JSON.stringify(this.newShiftArr));
            for(let i = 0;i<this.shiftNotOptions.length;i++){
                for(let j = 0;j<val.length;j++){
                    if(this.shiftNotOptions[i].name == val[j]){
                        this.shiftNotOptions[i].disabled = true;
                    }
                }
            }
            if(val.length == this.newShiftArr.length){
                this.ruleAddForm.notValidTypeId = [];
            }
        },

        // select 不可选班次
        selectNotShift(val){
            this.shiftOptions = JSON.parse(JSON.stringify(this.newShiftArr));
            for(let i = 0;i<this.shiftOptions.length;i++){
                for(let j = 0;j<val.length;j++){
                    if(this.shiftOptions[i].name == val[j]){
                        this.shiftOptions[i].disabled = true;
                    }
                }
            }
            if(val.length == this.newShiftArr.length){
                this.ruleAddForm.validWorkTypeId = [];
            }
        },

        // 班组名称
        nameList(){
            this.$http.queryWorkGroupAll(null,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.nameOption = res.data.data;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // // 车间
        // workshopList(){
        //     var data = {
        //         plant:''
        //     }
        //     this.$http.queryPlantWorkshopAll(data,(res)=>{
        //         if(res.code == 'SUCCESS'){
        //             this.workshopOption = res.data.data;
        //         }else{
        //             this.$message.error(res.message);
        //         }
        //     },(errRes)=>{
                
        //         this.$message.error(errRes.message);
        //     })
        // },

        // // 厂区
        // plantList(){
        //     this.$http.queryPlantList(null,(res)=>{
        //         if(res.code == 'SUCCESS'){
        //             this.plantOption = res.data;
        //         }else{
        //             this.$message.error(res.message);
        //         }
        //     },(errRes)=>{
                
        //         this.$message.error(errRes.message);
        //     })
        // }
    },
    mounted(){
        this.initData();
        this.shiftList();
        this.nameList();
        // this.workshopList();
        // this.plantList();
    }
}
</script>
