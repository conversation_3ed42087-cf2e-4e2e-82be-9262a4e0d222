<template>
    <div class="exceptionBox">
       <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>数据查询</el-breadcrumb-item>
            <el-breadcrumb-item>出勤异常查询</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
                <el-form-item label="姓名：" prop="name">
                    <el-input v-model="formInline.name" placeholder="请输入姓名"></el-input>
                </el-form-item>
                <el-form-item label="工号：" prop="number">
                    <el-input v-model="formInline.number" placeholder="请输入工号"></el-input>
                </el-form-item>
                <el-form-item label="Plant：" prop="plant">
                    <el-input v-model="formInline.plant" placeholder="请输入Plant"></el-input>
                </el-form-item>
                <el-form-item label="Workshop：" prop="workshop">
                    <el-input v-model="formInline.workshop" placeholder="请输入Workshop"></el-input>
                </el-form-item>
                <el-form-item label="生产线：" prop="productLine">
                    <el-input v-model="formInline.productLine" placeholder="请输入生产线"></el-input>
                </el-form-item>
                <el-form-item label="部门：" prop="department">
                    <el-input v-model="formInline.department" placeholder="请输入部门"></el-input>
                </el-form-item>
                <el-form-item label="班组：" prop="team">
                    <el-input v-model="formInline.team" placeholder="请输入班组"></el-input>
                </el-form-item>
                <el-form-item label="班次：" prop="shifts">
                    <el-input v-model="formInline.shifts" placeholder="请输入班次"></el-input>
                </el-form-item>
                <el-form-item label="日期：" prop="dataTime">
                    <el-date-picker
                        v-model="formInline.dataTime"
                        type="daterange"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd"
                        range-separator="~"
                        :clearable = false>
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="initData(currentPage = 1)">查询</el-button>
                    <el-button @click="resetForm('formInline')">重置</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="importBtn">导入</el-button>
                    <!-- <el-button type="primary" @click="exportBtn">导出</el-button> -->
                </el-form-item>
            </el-form>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableContent">
            <table>
                <thead>
                    <tr>
                        <th>日期</th>
                        <th>工号</th>
                        <th>姓名</th>
                        <th>Plant</th>
                        <th>Workshop</th>
                        <th>班次</th>
                        <th>班组</th>
                        <th>生产线</th>
                        <th>部门</th>
                        <!-- <th>异常类型</th> -->
                        <th>刷卡开始时间</th>
                        <th>刷卡结束时间</th>
                        <th>奇次卡次数</th>
                        <th>旷工时数</th>
                        <th>迟到分钟</th>
                        <th>早退分钟</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="tableData.length <= 0">
                        <td colspan="14">暂无数据!</td>
                    </tr>
                    <tr v-for="(item,index) in tableData" :key='index' v-else>
                        <td>{{item.calendarDay}}</td>
                        <td>{{item.empId}}</td>
                        <td>{{item.name}}</td>
                        <td>{{item.plant}}</td>
                        <td>{{item.workshop}}</td>
                        <td>{{item.workType}}</td>
                        <td>{{item.workTeam}}</td>
                        <td>{{item.productionLine}}</td>
                        <td>{{item.department}}</td>
                        <!-- <td>{{item.type}}</td> -->
                        <td>{{item.startTime}}</td>
                        <td>{{item.endTime}}</td>
                        <td>{{item.loseSignCount}}</td>
                        <td>{{item.absenteeismMinutes}}</td>
                        <td>{{item.lateMinutes}}</td>
                        <td>{{item.leaveEarlyMinutes}}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- table end -->

        <!-- 分页 start -->
        <div class="paginationBox">
            <el-pagination v-show="total!=0" background layout="total, prev, pager, next, sizes, jumper" :total="total" :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData" @current-change="initData"></el-pagination>
        </div>
        <!-- 分页 end -->

        <!-- 导入dialog start -->
        <el-dialog
            title="导入"
            :visible.sync="dialogImport"
            width="60%"
            :close-on-click-modal='false'
            @close="cancelUpload()"
            >
            <el-form label-width="150px" class="demo-ruleForm">
                <el-form-item label="考勤异常：">
                    <el-upload
                        class="upload-demo"
                        ref="uploadExcel"
                        action="/api/admin/attendanceAnalysisController/uploadAttendanceAnalysis"
                        :headers="header"
                        :limit="limitNum"
                        accept=".XLS,.xlsx"
                        :file-list="fileList"
                        :before-upload="beforeFileUpload"
                        :on-exceed="exceedFile"
                        :on-success="handleFileSuccess"
                        :auto-upload="false"
                        :on-change="handleChange">
                        <el-button slot="trigger" type="text">选择文件</el-button>
                        <div slot="tip" class="el-upload__tip el-icon-info">导入文件格式为Excel</div>
                    </el-upload>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitUpload()">确定</el-button>
                    <el-button @click="cancelUpload()">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 导入dialog end -->
    </div>
</template>
<script>
var axios = require('axios')// 引用axios
import utils from '../utils/index.js'//引用常用工具文件
export default {
    data(){
        return{
            // 头部查询
            formInline: {
                name:'',
                number:'',
                plant:'',
                workshop:'',
                productLine:'',
                department:'',
                team:'',
                shifts:'',
                dataTime:''
            },
            // 表格
            tableData:[],
            // 分页
            currentPage:1,
            pageSize:10,
            total:0,
            // 导入dialog
            dialogImport:false,
            limitNum: 1,
            fileList: [],
            header:{
                'Authorization' : localStorage.getItem('Auth-Token')
            },
        }
    },
    methods:{
        // 重置
        resetForm(formName) {
            this.$refs[formName].resetFields();
            this.currentPage = 1;
            this.initData();
        },

        // 渲染数据 分页
        initData(){
            let data = {
                calendarDay:'',
                costCenter:'',
                department:this.formInline.department,
                empId:this.formInline.number,
                endQueryDate:this.formInline.dataTime[1],
                name:this.formInline.name,
                plant:this.formInline.plant,
                productionLine:this.formInline.productLine,
                startQueryDate:this.formInline.dataTime[0],
                workTeam:this.formInline.team,
                workType:this.formInline.shifts,
                workshop:this.formInline.workshop,
                offset:this.pageSize,
                rows:this.currentPage,
            }
            this.$http.queryAttendanceAnalysisPage(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.tableData = res.data.data;
                    this.total = res.data.count;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },

        // // 导出
        // exportBtn(){
            
        // },

        // 导入
        importBtn(){
            this.dialogImport = true;
        },
        // 上传文件之前的钩子，参数为上传的文件,若返回 false 或者返回 Promise 且被 reject，则停止上传
        beforeFileUpload (file){
            const extension = file.name.split('.')[1];
            if ((extension !== 'XLS') && (extension !== 'xlsx')) {
                this.$message.warning('上传模板只能是 XLS、xlsx格式!')
                return false
            }else{

            }
        },
        // 文件超出个数限制时的钩子
        exceedFile(files, fileList) {
            this.$message.warning({
                title: '警告',
                message: `只能选择 ${this.limitNum} 个文件，当前共选择了 ${files.length + fileList.length} 个`
            });
        },
        // 上传成功后的钩子
        handleFileSuccess(res, file) {
            if(res.code == 'SUCCESS'){
                this.dialogImport = false;//关闭弹框
                this.initData();
                this.$message.success(res.message);  
            }else{
                this.$message.error(res.message);
            }
        },
        // 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用
        handleChange(file, fileList) {
            this.fileList = fileList;
        },

        // 确定
        submitUpload(){
            if(this.fileList == ''){
                this.$message.error("请上传文件");
            }else{
                this.$refs.uploadExcel.submit();
            }
        },
        // 取消
        cancelUpload(){
            this.fileList = [];
            this.dialogImport = false;
        }
    },
    mounted(){
        this.initData();
    }
}
</script>
