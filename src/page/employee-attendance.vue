<template>
  <div class="employeeBox">
    <!-- 面包屑 start -->
    <el-breadcrumb separator="/">
      <el-breadcrumb-item>考勤设置</el-breadcrumb-item>
       <el-breadcrumb-item :to="{ path: '/attendance-summary' }">考勤汇总</el-breadcrumb-item>
        <el-breadcrumb-item>{{this.$route.query.confirmStatus== 0?'未确认人员':'已确认人员'}}</el-breadcrumb-item>
    </el-breadcrumb>
    <!-- 面包屑 end -->

    <!-- search start -->
    <div class="searchFrom">
      <el-form
        :inline="true"
        :model="formInline"
        ref="formInline"
        class="demo-form-inline"
      >
        <el-form-item label="姓名：" prop="name">
          <el-input
            v-model="formInline.name"
            placeholder="请输入姓名"
          ></el-input>
        </el-form-item>
        <el-form-item label="工号：" prop="empId">
          <el-input
            v-model="formInline.empId"
            placeholder="请输入工号"
          ></el-input>
        </el-form-item>
        <el-form-item label="部门：" prop="department">
          <el-input
            v-model="formInline.department"
            placeholder="请输入部门"
          ></el-input>
        </el-form-item>
        <el-form-item label="生产线：" prop="productLine">
          <el-input
            v-model="formInline.productLine"
            placeholder="请输入生产线"
          ></el-input>
        </el-form-item>

        <el-form-item label="班组：" prop="workTeam">
          <el-input
            v-model="formInline.workTeam"
            placeholder="请输入班组"
          ></el-input>
        </el-form-item>

        <el-form-item label="在职状态：" prop="actived">
          <el-input
            v-model="formInline.actived"
            placeholder="请输入在职状态"
          ></el-input>
        </el-form-item>

        <el-form-item label="考勤文员工号：" prop="hrId">
          <el-input v-model="formInline.hrId" placeholder="请输入考勤文员工号"></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="initData((currentPage = 1))"
            >查询</el-button
          >
<!--          <el-button type="primary" @click="initHRData((currentPage = 1))"
          >考勤文员</el-button
          >-->

          <el-button @click="resetFormR('formInline')">重置</el-button>
          <el-button type="primary" @click="exportExcel">导出</el-button>
          <el-button v-if="formInline.confirmStatus == 0" type="primary" @click="batchNotification">批量通知</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- search end -->

    <!-- table start -->
    <div class="tableContent">
      <el-table ref="multipleTable" :data="tableData" style="width: 100%">
        <el-table-column prop="ym" label="期间"></el-table-column>
        <el-table-column prop="confirmTime" label="员工签字确认时间" :formatter="formatTime"></el-table-column>
        <el-table-column prop="sendTime" label="系统发起时间" :formatter="formatSendTime"></el-table-column>
        <el-table-column prop="empId" label="工号"></el-table-column>
        <el-table-column prop="name" label="姓名"></el-table-column>
        <el-table-column prop="plant" label="公司"></el-table-column>
        <el-table-column prop="productionLine" label="部门"></el-table-column>
        <el-table-column prop="workTeam" label="班组"></el-table-column>
        <el-table-column prop="pnhr" label="应工作时数"></el-table-column>
        <el-table-column prop="wkhr" label="工作时数"></el-table-column>
        <el-table-column prop="adhr" label="出勤工时"></el-table-column>
        <el-table-column prop="uahr" label="旷工时数"></el-table-column>
        <el-table-column prop="limn" label="迟到分钟"></el-table-column>
        <el-table-column prop="eomn" label="早退分钟"></el-table-column>
        <el-table-column prop="an15" label="OT1.5" :formatter="formatDecimal"></el-table-column>
        <el-table-column prop="an20" label="OT2.0" :formatter="formatDecimal"></el-table-column>
        <el-table-column prop="ot3" label="OT3.0" :formatter="formatDecimal"></el-table-column>
        <el-table-column prop="" label="加班总工时" :formatter="formatTotalOvertimeHours"></el-table-column>
        <el-table-column prop="perl" label="事假"></el-table-column>
        <el-table-column prop="otcl" label="调休"></el-table-column>
        <el-table-column prop="occl" label="调休假（公司原因）"></el-table-column>
        <el-table-column prop="annl" label="年假"></el-table-column>
        <el-table-column prop="pskl" label="全薪病假"></el-table-column>
        <el-table-column prop="bpsl" label="扣薪病假"></el-table-column>
        <el-table-column prop="" label="有薪假工时" :formatter="formatPaidLeaveHours"></el-table-column>
        <el-table-column prop="nbos" label="轮班次数"></el-table-column>
        <el-table-column prop="lsch" label="欠班时数"></el-table-column>
        <el-table-column prop="actived" label="在职状态"></el-table-column>
        <el-table-column v-if="formInline.confirmStatus == 0" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="mini"
              @click="singleNotification(scope.row)"
            >
              通知
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- table end -->

    <!-- 分页 start -->
    <div class="paginationBox">
      <el-pagination
        v-show="total != 0"
        background
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        @size-change="initData"
        @current-change="initData"
      ></el-pagination>
    </div>
    <!-- 分页 end -->
  </div>
</template>
<script>
var axios = require("axios"); // 引用axios
import utils from "../utils/index.js"; //引用常用工具文件
export default {
  data() {
    return {
      // 头部查询
      formInline: {
        name: "",
        number: "",
        workshop: "",
        productLine: "",
        department: "",
        team: "",
        confirmStatus: "",
        term: "",
        workTeam: "",
        empId: "",
        actived: "",
      },
      // 表格
      tableData: [],
      // 考勤文员
      hr: 0,
      // 分页
      currentPage: 1,
      pageSize: 10,
      total: 0,
    };
  },
  methods: {
    // 重置
    resetFormR(formName) {
      this.$refs[formName].resetFields();
      this.$refs.multipleTable.clearSelection();
      this.currentPage = 1;
      this.initData();
    },

    // 渲染数据 分页
    initData() {
      let data = {
        department: this.formInline.department,
        empId: this.formInline.empId,
        productionLine: this.formInline.productLine,
        name: this.formInline.name,
        offset: this.pageSize,
        rows: this.currentPage,
        workTeam: this.formInline.workTeam,
        term: this.formInline.term,
        confirmStatus: this.formInline.confirmStatus,
        actived: this.formInline.actived,
        hrId: this.formInline.hrId,
      };
      this.$http.monthlyAttendanceEmployee(
        data,
        (res) => {
          if (res.code == "SUCCESS") {
            this.tableData = res.data.data;
            this.total = res.data.count;
          } else {
            this.$message.error(res.message);
          }
        },
        (errRes) => {
          this.$message.error(errRes.message);
        }
      );
    },

    // 格式化时间
    formatTime(row, column, cellValue) {
      if (!cellValue) return '';
      return cellValue.replace('T', ' ').split('.')[0];
    },

    // 格式化系统发起时间（未确认时显示空值）
    formatSendTime(row, column, cellValue) {
      // 当confirmStatus=0（未确认）时，系统发起时间留空
      if (row.confirmStatus === 0) {
        return '';
      }
      if (!cellValue) return '';
      return cellValue.replace('T', ' ').split('.')[0];
    },

    // 格式化小数点后2位
    formatDecimal(row, column, cellValue) {
      const value = parseFloat(cellValue) || 0;
      return value.toFixed(2);
    },

    // 格式化加班总工时
    formatTotalOvertimeHours(row, column, cellValue) {
      const ot1 = parseFloat(row.an15) || 0;
      const ot2 = parseFloat(row.an20) || 0;
      const ot3 = parseFloat(row.ot3) || 0;
      const total = ot1 + ot2 + ot3;
      // return total;
      return total.toFixed(2);
    },

    // 格式化有薪假工时
    formatPaidLeaveHours(row, column, cellValue) {
      // 有薪假类型包括：工伤假+丧假+出差+婚假+产假检查假+陪产假+产假+难产假+哺乳假+晚育假+计划生育假+流产假+停产假+育儿假+三八妇女节假+护理假+其他假
      const paidLeaveTypes = [
        'isjl',    // 工伤假
        'coml',    // 丧假
        'fanf',    // 出差
        'marl',    // 婚假
        'mlpl',  // 产假检查假
        'patl',    // 陪产假
        'matl',    // 产假
        'dsfl',    // 难产假
        'brfl',    // 哺乳假
        'cifl',    // 晚育假
        'fpgl',  // 计划生育假
        'mscl',    // 流产假
        'pofl',    // 停产假
        'childl',    // 育儿假
        'ladl', // 三八妇女节假
        'nursl',    // 护理假
        'othl1'     // 其他假
      ];

      let total = 0;
      paidLeaveTypes.forEach(leaveType => {
        total += parseFloat(row[leaveType]) || 0;
      });

      return total;
      // return total.toFixed(2);
    },

    // 导出Excel
    exportExcel() {
      // 显示加载状态
      const loading = this.$loading({
        lock: true,
        text: '正在导出Excel文件...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      let data = {
        department: this.formInline.department,
        empId: this.formInline.empId,
        productionLine: this.formInline.productLine,
        name: this.formInline.name,
        workTeam: this.formInline.workTeam,
        term: this.formInline.term,
        confirmStatus: this.formInline.confirmStatus,
      };

      // 使用项目中定义的API方法
      this.$http.exportAttendanceExcel(
        data,
        (response) => {
          loading.close();

          // 创建blob对象
          const blob = new Blob([response.data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          });

          // 创建下载链接
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;

          // 设置文件名
          const now = new Date();
          const dateStr = now.getFullYear() +
            String(now.getMonth() + 1).padStart(2, '0') +
            String(now.getDate()).padStart(2, '0') + '_' +
            String(now.getHours()).padStart(2, '0') +
            String(now.getMinutes()).padStart(2, '0');

          // 根据确认状态设置文件名后缀
          const statusSuffix = (data.confirmStatus === 0) ? '未确认' : '已确认';
          a.download = `月度考勤分析详情_${dateStr}_${statusSuffix}.xlsx`;

          // 触发下载
          document.body.appendChild(a);
          a.click();

          // 清理
          document.body.removeChild(a);
          window.URL.revokeObjectURL(url);

          this.$message.success('Excel文件导出成功');
        },
        (error) => {
          loading.close();
          console.error('导出失败:', error);
          this.$message.error('导出Excel文件失败，请稍后重试');
        }
      );
    },

    // 批量通知
    batchNotification() {
      this.$confirm('确定要发送批量考勤确认通知吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 显示加载状态
        const loading = this.$loading({
          lock: true,
          text: '正在发送通知...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        let data = {
          term: this.formInline.term,
          empId: '' // 空值表示批量通知
        };

        this.$http.reSendAttendanceConfirmation(
          data,
          (res) => {
            loading.close();
            if (res.code == "SUCCESS") {
              this.$message.success('批量通知发送成功');
            } else {
              this.$message.error(res.message || '批量通知发送失败');
            }
          },
          (errRes) => {
            loading.close();
            this.$message.error(errRes.message || '批量通知发送失败');
          }
        );
      }).catch(() => {
        // 用户取消操作
      });
    },

    // 单条通知
    singleNotification(row) {
      this.$confirm(`确定要向员工 ${row.name}(${row.empId}) 发送考勤确认通知吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 显示加载状态
        const loading = this.$loading({
          lock: true,
          text: '正在发送通知...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        let data = {
          term: this.formInline.term,
          empId: row.empId // 具体员工工号
        };

        this.$http.reSendAttendanceConfirmation(
          data,
          (res) => {
            loading.close();
            if (res.code == "SUCCESS") {
              this.$message.success(`向员工 ${row.name} 发送通知成功`);
            } else {
              this.$message.error(res.message || '通知发送失败');
            }
          },
          (errRes) => {
            loading.close();
            this.$message.error(errRes.message || '通知发送失败');
          }
        );
      }).catch(() => {
        // 用户取消操作
      });
    },
  },
  mounted() {
    if (this.$route.path == "/employee-attendance") {
      this.formInline.term = this.$route.query.term + "-" + "01";
      this.formInline.confirmStatus = this.$route.query.confirmStatus;
      this.initData();
    }
  },
};
</script>
