<template>
    <div class="withdrawalApplicationBox">
        <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>申请管理</el-breadcrumb-item>
            <el-breadcrumb-item>撤销申请</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
                <el-form-item label="姓名：" prop="name">
                    <el-input v-model="formInline.name" placeholder="请输入姓名"></el-input>
                </el-form-item>
                <el-form-item label="工号：" prop="number">
                    <el-input v-model="formInline.number" placeholder="请输入工号"></el-input>
                </el-form-item>
                <el-form-item label="Plant：" prop="plant">
                    <el-input v-model="formInline.plant" placeholder="请输入Plant"></el-input>
                </el-form-item>
                <el-form-item label="Workshop：" prop="workshop">
                    <el-input v-model="formInline.workshop" placeholder="请输入Workshop"></el-input>
                </el-form-item>
                <el-form-item label="生产线：" prop="productLine">
                    <el-input v-model="formInline.productLine" placeholder="请输入生产线"></el-input>
                </el-form-item>
                <el-form-item label="部门：" prop="department">
                    <el-input v-model="formInline.department" placeholder="请输入部门"></el-input>
                </el-form-item>
                <el-form-item label="班组：" prop="team">
                    <el-input v-model="formInline.team" placeholder="请输入班组"></el-input>
                </el-form-item>
                <el-form-item label="班次：" prop="shifts">
                    <el-input v-model="formInline.shifts" placeholder="请输入班次"></el-input>
                </el-form-item>
                <el-form-item label="状态：" prop="statusQ">
                    <el-input v-model="formInline.statusQ" placeholder="请输入状态"></el-input>
                </el-form-item>
                <el-form-item label="申请日期：" prop="dataTime">
                    <el-date-picker
                        v-model="formInline.dataTime"
                        type="daterange"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd"
                        range-separator="~"
                        :clearable = false>
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="申请类别：" prop="applicatType">
                    <el-select v-model="formInline.applicatType" placeholder="请选择">
                        <el-option
                            v-for="item in applicatTypeOption"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="initData(currentPage = 1)">查询</el-button>
                    <el-button @click="resetForm('formInline')">重置</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="withdrawalBtn">批量撤销</el-button>
                    <el-button type="primary" @click="downBtn">下载</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableContent">
            <el-table
                ref="multipleTable"
                :data="tableData"
                style="width: 100%"
                @selection-change="handleSelectionChange"
                :row-key="getRowKeys">
                <el-table-column type="selection" width="55" :reserve-selection="true" :selectable="checkSelectable"></el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-button v-if="scope.row.nodeName === '已完成'" type="text" @click="handleWithdrawal(scope.row.processinstanceid)">撤销</el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="nodeName" label="状态"></el-table-column>
                <el-table-column prop="name" label="姓名"></el-table-column>
                <el-table-column prop="empId" label="工号"></el-table-column>
                <el-table-column prop="plant" label="Plant"></el-table-column>
                <el-table-column prop="workshop" label="Workshop"></el-table-column>
                <el-table-column prop="productionLine" label="生产线"></el-table-column>
                <el-table-column prop="department" label="部门"></el-table-column>
                <el-table-column prop="workTeam" label="班组"></el-table-column>
                <el-table-column prop="workType" label="班次"></el-table-column>
                <el-table-column prop="applyStatusName" label="申请类别"></el-table-column>
                <el-table-column prop="leaveTypeName" label="请假类型"></el-table-column>
                <el-table-column prop="applyTime" label="申请时间"></el-table-column>
                <el-table-column prop="startTime" label="申请开始时间"></el-table-column>
                <el-table-column prop="endTime" label="申请结束时间"></el-table-column>
                <el-table-column prop="timeCount" label="申请时长（h）"></el-table-column>
                <el-table-column prop="approver" label="申请详情"></el-table-column>
                <el-table-column prop="approverName" label="当前审批人"></el-table-column>
                <el-table-column prop="processName" label="审批流程"></el-table-column>
                <el-table-column prop="remark" label="撤销备注"></el-table-column>
            </el-table>
        </div>
        <!-- table end -->

        <!-- 分页 start -->
        <div class="paginationBox">
            <el-pagination v-show="total!=0" background layout="total, prev, pager, next, sizes, jumper" :total="total" :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData" @current-change="initData"></el-pagination>
        </div>
        <!-- 分页 end -->

        <!-- 撤销确认dialog start -->
        <el-dialog
            title="撤销确认"
            :visible.sync="dialogVisible"
            width="60%"
            :close-on-click-modal='false'
            @close="resetFormC('ruleForm')"
            >
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px" class="demo-ruleForm">
                <div class="comfirmStyle">请确认是否取消本申请单，确认撤销后，本申请单将作废处理，并且不能恢复，请确认</div>
                <el-form-item label="撤销备注：" prop="remark">
                    <el-input type="textarea" v-model="ruleForm.remark" placeholder="请输入撤销备注"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleForm')">确定</el-button>
                    <el-button @click="resetFormC('ruleForm')">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 撤销确认dialog end -->
    </div>
</template>
<script>
var axios = require('axios')// 引用axios
import utils from '../utils/index.js'//引用常用工具文件
export default {
    data(){
        return{
            // 头部查询
            formInline: {
                name:'',
                number:'',
                plant:'',
                workshop:'',
                productLine:'',
                department:'',
                team:'',
                shifts:'',
                dataTime:'',
                applicatType:'',
                statusQ:'',
            },
            applicatTypeOption:[
                {
                    id:1,
                    name:'请假申请'
                },
                {
                    id:2,
                    name:'加班申请'
                },
                {
                    id:4,
                    name:'借调申请'
                },
                {
                    id:3,
                    name:'补卡申请'
                }
            ],
            // 表格
            tableData:[],
            // 分页
            currentPage:1,
            pageSize:10,
            total:0,
            dialogVisible:false,//撤销确认dialog
            ruleForm:{
                remark:''
            },
            rules:{
                remark: [
                    { required: true, message: '请输入撤销备注', trigger: 'blur' }
                ]
            },
            multipleSelectIds:[],//已选选项的id
            dialogStatus:'',
            dialogId:''
        }
    },
    methods:{
        // 重置
        resetForm(formName){
            this.$refs[formName].resetFields();
            this.$refs.multipleTable.clearSelection();
            this.currentPage = 1;
            this.initData();
        },

        // 渲染数据 分页
        initData(){
            let data = {
                applyEndTime:this.formInline.dataTime ? this.formInline.dataTime[1] : '',
                applyStartTime:this.formInline.dataTime ? this.formInline.dataTime[0] : '',
                applyStatus:this.formInline.applicatType,
                department:this.formInline.department,
                empId:this.formInline.number,
                name:this.formInline.name,
                nodeName:this.formInline.statusQ,
                plant:this.formInline.plant,
                productionLine:this.formInline.productLine,
                workTeam:this.formInline.team,
                workType:this.formInline.shifts,
                workshop:this.formInline.workshop,
                offset:this.pageSize,
                rows:this.currentPage,
            }
            this.$http.queryProcessFormPage(data,(res)=>{

                if(res.code == 'SUCCESS'){
                    this.tableData = res.data.data;
                    this.total = res.data.count;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // 批量撤销
        withdrawalBtn(){
            if(this.multipleSelectIds.length <= 0){
                this.$message.error("请先选择要撤销的数据！");
            }else{
                this.dialogVisible = true;
                this.dialogStatus = 1;
            }
        },

        // 表格全选
        handleSelectionChange(val) {
            this.multipleSelectIds = val.map(item => {
                return item.processinstanceid
            })
        },

        // 获取row的key值
        getRowKeys(row) {
            return row.processinstanceid;
        },

        // 设置表格某行不能选择
        checkSelectable(row,index){
            console.log(row)
            let flag = true;
            if( row.nodeName != '已完成'){
                flag = false
            }
            return flag
        },

        // 表格撤销
        handleWithdrawal(id){
            this.dialogVisible = true;
            this.dialogId = id;
            this.dialogStatus = 2;
        },

        // 撤销确认
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    var processinstanceid = [];
                    if(this.dialogStatus == 1){
                        processinstanceid = this.multipleSelectIds;
                    }else{
                        processinstanceid.push(this.dialogId)
                    }

                    let data = {
                        processinstanceid:processinstanceid,
                        remark:this.ruleForm.remark
                    }
                    this.$http.revocationProcessList(data,(res)=>{

                        if(res.code == 'SUCCESS'){
                            this.$refs[formName].resetFields();
                            this.$refs.multipleTable.clearSelection();
                            this.dialogVisible = false;
                            this.initData()
                        }else{
                            this.$message.error(res.message);
                        }
                    },(errRes)=>{
                        
                        this.$message.error(errRes.message);
                    })
                } else {
                    return false;
                }
            });
        },
        // 撤销取消
        resetFormC(formName){
            this.$refs[formName].resetFields();
            this.$refs.multipleTable.clearSelection();
            this.dialogVisible = false;
        },

        // 下载
        downBtn(){
            let data = {
                applyEndTime:this.formInline.dataTime ? this.formInline.dataTime[1] : '',
                applyStartTime:this.formInline.dataTime ? this.formInline.dataTime[0] : '',
                applyStatus:this.formInline.applicatType,
                department:this.formInline.department,
                empId:this.formInline.number,
                name:this.formInline.name,
                nodeName:this.formInline.statusQ,
                plant:this.formInline.plant,
                productionLine:this.formInline.productLine,
                workTeam:this.formInline.team,
                workType:this.formInline.shifts,
                workshop:this.formInline.workshop,
                offset:this.pageSize,
                rows:this.currentPage,
            }
            axios({
                url: 'api/admin/processFormController/exportProcessForm',
                method: "POST",
                data:data,
                responseType:"blob",
                headers:{
                    // 'Content-Type':'application/json;charset=UTF-8',
                    'Authorization' : localStorage.getItem('Auth-Token')
                },
            }).then(function (res) {
                if(!res){
                    return
                }
                utils.downloadExcel(res.data,'流程记录信息导出','xlsx')//调用公共方法
            })
        },
    },
    mounted(){
        this.initData();
    }
}
</script>
