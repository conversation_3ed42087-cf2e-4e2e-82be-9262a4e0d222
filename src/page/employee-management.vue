<template>
    <div class="employeeBox">
        <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>员工管理</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
                <el-form-item label="姓名：" prop="name">
                    <el-input v-model="formInline.name" placeholder="请输入姓名"></el-input>
                </el-form-item>
                <el-form-item label="工号：" prop="number">
                    <el-input v-model="formInline.number" placeholder="请输入工号"></el-input>
                </el-form-item>
                <el-form-item label="Plant：" prop="plant">
                    <el-input v-model="formInline.plant" placeholder="请输入Plant"></el-input>
                </el-form-item>
                <el-form-item label="Workshop：" prop="workshop">
                    <el-input v-model="formInline.workshop" placeholder="请输入Workshop"></el-input>
                </el-form-item>
                <el-form-item label="生产线：" prop="productLine">
                    <el-input v-model="formInline.productLine" placeholder="请输入生产线"></el-input>
                </el-form-item>
                <el-form-item label="部门：" prop="department">
                    <el-input v-model="formInline.department" placeholder="请输入部门"></el-input>
                </el-form-item>
                <el-form-item label="是否已录入人脸照片：" prop="isInput">
                    <el-radio-group v-model="formInline.isInput">
                        <el-radio :label="2">是</el-radio>
                        <el-radio :label="1">否</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="班组：" prop="team">
                    <el-input v-model="formInline.team" placeholder="请输入班组"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="initData(currentPage = 1)">查询</el-button>
                    <el-button @click="resetFormR('formInline')">重置</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button @click="importBtn">人员导入</el-button>
                    <el-button @click="exportBtn">导出</el-button>
                    <!-- <el-button @click="modifyBtn">批量修改</el-button> -->
                </el-form-item>
                <el-form-item label="员工自行修改照片：" prop="selfModify">
                    <el-switch
                        v-model="formInline.selfModify"
                        active-value="0"
                        inactive-value="1"
                        active-text="允许"
                        inactive-text="禁止"
                        @change="changeImgUrl">
                    </el-switch>
                </el-form-item>
            </el-form>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableContent">
            <el-table
                ref="multipleTable"
                :data="tableData"
                style="width: 100%"
                @selection-change="handleSelectionChange"
                :row-key="getRowKeys">
                <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
                <el-table-column prop="name" label="员工姓名"></el-table-column>
                <el-table-column prop="empId" label="工号"></el-table-column>
                <el-table-column prop="plant" label="Plant"></el-table-column>
                <el-table-column prop="workshop" label="Workshop"></el-table-column>
                <el-table-column prop="productionLine" label="生产线"></el-table-column>
                <el-table-column prop="department" label="部门"></el-table-column>
                <el-table-column prop="directName" label="职位"></el-table-column>
                <el-table-column prop="actived" label="在职状态"></el-table-column>
                <el-table-column prop="firstLevelMgrId" label="1st Level Mgr ID"></el-table-column>
                <el-table-column prop="secondLevelMgrId" label="2st Level Mgr ID"></el-table-column>
                <el-table-column prop="workHourType" label="工时制类型"></el-table-column>
                <el-table-column prop="workTeam" label="班组"></el-table-column>
                <el-table-column prop="roleName" label="角色"></el-table-column>
                <el-table-column prop="isImage" label="是否已录入人脸照片"></el-table-column>
                <el-table-column prop="entryDate" label="入职日期"></el-table-column>
                <el-table-column label="离职日期">
                    <template slot-scope="scope">
                        {{ scope.row.lastWorkDate || scope.row.leaveDate }}
                    </template>
                </el-table-column>
                <el-table-column prop="empGroup" label="雇员类型"></el-table-column>
                <!-- <el-table-column prop="machineName" label="可打卡考勤机"></el-table-column> -->
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-button type="text" @click="handlelook(scope.row.id)">查看详情</el-button>
                        <el-button type="text" @click="handleEdit(scope.row.id)">编辑</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- table end -->

        <!-- 分页 start -->
        <div class="paginationBox">
            <el-pagination v-show="total!=0" background layout="total, prev, pager, next, sizes, jumper" :total="total" :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData" @current-change="initData"></el-pagination>
        </div>
        <!-- 分页 end -->

        <!-- 批量修改dialog start -->
        <el-dialog
            :title="dialogTitle == 0 ? '批量修改' : '编辑'"
            :visible.sync="dialogModify"
            width="60%"
            :close-on-click-modal='false'
            @close="resetForm('ruleModifyForm')"
            >
            <el-form :model="ruleModifyForm" :rules="rulesModify" ref="ruleModifyForm" label-width="120px" class="demo-ruleForm">
                <!-- <el-form-item label="班组：" prop="team">
                    <el-autocomplete
                        v-model="ruleModifyForm.team"
                        placeholder="请选择班组"
                        :fetch-suggestions="querySearch"
                        :trigger-on-focus="false"
                        @select="handleSelect"
                        ></el-autocomplete>
                </el-form-item> -->
                <el-form-item label="角色：" prop="role" v-if="dialogTitle == 1">
                    <el-select v-model="ruleModifyForm.role" placeholder="请选择角色">
                        <el-option
                            v-for="item in roleOption"
                            :key="item.RNUMBER"
                            :label="item.ROLE_NAME"
                            :value="item.RNUMBER">
                        </el-option>
                    </el-select>
                </el-form-item>
                <!-- <el-form-item label="可打卡考勤机：" prop="isClock">
                    <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
                    <el-checkbox-group v-model="ruleModifyForm.isClock" @change="handleCheckedChange">
                        <el-checkbox v-for="machine in machines" :label="machine.machine_sn" :key="machine.machine_sn">{{machine.machine_name}}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item> -->
                <el-form-item label="人脸照片：" prop="imageUrl" v-if="dialogTitle == 1">
                    <el-upload
                        class="avatar-uploader"
                        action="/api/admin/qiniu/upload"
                        :show-file-list="false"
                        accept='image/*'
                        :headers="header"
                        :on-success="handleAvatarSuccess"
                        :before-upload="beforeAvatarUpload">
                        <img v-if="ruleModifyForm.imageUrl" :src="ruleModifyForm.imageUrl" class="avatar">
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleModifyForm')">确定</el-button>
                    <el-button @click="resetForm('ruleModifyForm')">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 批量修改dialog end -->

        <!-- 查看详情dialog start -->
        <el-dialog
            title="查看详情"
            :visible.sync="dialogDetail"
            width="60%"
            :close-on-click-modal='false'
            @close="cancelDetail()"
           >
            <el-form :model="formDetail" label-width="150px">
                <el-form-item label="员工姓名：">{{formDetail.name}}</el-form-item>
                <el-form-item label="工号：">{{formDetail.empId}}</el-form-item>
                <el-form-item label="厂区：">{{formDetail.plant}}</el-form-item>
                <el-form-item label="Workshop：">{{formDetail.workshop}}</el-form-item>
                <el-form-item label="生产线：">{{formDetail.productionLine}}</el-form-item>
                <el-form-item label="部门：">{{formDetail.department}}</el-form-item>
                <!-- <el-form-item label="子部门：">{{formDetail.departChild}}</el-form-item> -->
                <el-form-item label="职位：">{{formDetail.directName}}</el-form-item>
                <el-form-item label="在职状态：">{{formDetail.actived}}</el-form-item>
                <el-form-item label="1st Level Mgr ID：">{{formDetail.firstLevelMgrId}}</el-form-item>
                <el-form-item label="2st Level Mgr ID：">{{formDetail.secondLevelMgrId}}</el-form-item>
                <el-form-item label="工时制类型：">{{formDetail.workHourType}}</el-form-item>
                <el-form-item label="班组：">{{formDetail.workTeam}}</el-form-item>
                <!-- <el-form-item label="是否借调中：">{{formDetail.isLoan}}</el-form-item> -->
                <el-form-item label="角色：">{{formDetail.roleName}}</el-form-item>
                <el-form-item label="人脸照片："><img class="detailsImg" :src="formDetail.faceUrl" alt=""></el-form-item>
                <!-- <el-form-item label="可打卡考勤机:">{{formDetail.machineName}}</el-form-item> -->
            </el-form>
        </el-dialog>
        <!-- 查看详情dialog end -->

        <!-- 人员导入dialog start -->
        <el-dialog
            title="人员导入"
            :visible.sync="dialogImport"
            width="60%"
            :close-on-click-modal='false'
            @close="cancelUpload()"
            >
            <el-form label-width="150px" class="demo-ruleForm">
                <el-form-item label="考勤信息：">
                    <el-upload
                        class="upload-demo"
                        ref="uploadExcel"
                        action="/api/admin/employeeController/importEmployee"
                        :headers="header"
                        :limit="limitNum"
                        accept=".xls,.xlsx"
                        :file-list="fileList"
                        :before-upload="beforeFileUpload"
                        :on-exceed="exceedFile"
                        :on-success="handleFileSuccess"
                        :auto-upload="false"
                        :on-change="handleChange">
                        <el-button slot="trigger" type="text">选择文件</el-button>
                        <div slot="tip" class="el-upload__tip el-icon-info">导入文件格式为Excel</div>
                    </el-upload>
                </el-form-item>
                <el-form-item label="职员基本信息：">
                    <el-upload
                        class="upload-demo"
                        ref="uploadExcelE"
                        action="/api/admin/employeeController/importEmployeeTool"
                        :headers="header"
                        :limit="limitNum"
                        accept=".xls,.xlsx"
                        :file-list="fileListE"
                        :before-upload="beforeFileUploadE"
                        :on-exceed="exceedFileE"
                        :on-success="handleFileSuccessE"
                        :auto-upload="false"
                        :on-change="handleChangeE">
                        <el-button slot="trigger" type="text">选择文件</el-button>
                        <div slot="tip" class="el-upload__tip el-icon-info">导入文件格式为Excel</div>
                    </el-upload>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitUpload()">确定</el-button>
                    <el-button @click="cancelUpload()">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 人员导入dialog end -->
    </div>
</template>
<script>
var axios = require('axios')// 引用axios
import utils from '../utils/index.js'//引用常用工具文件
export default {
    data(){
        // // 班组验证
        // var checkTeam = (rule,value,callback) => {
        //     if(value == ''){
        //         callback(new Error('请选择班组!'))
        //     }else{
        //         callback()
        //     }
        // };
        // 角色验证
        var checkRole = (rule,value,callback) => {
            if(value == ''){
                callback(new Error('请选择角色!'))
            }else{
                callback()
            }
        };
        // 可打卡考勤机验证
        var checkClock = (rule,value,callback) => {
            if(value == ''){
                callback(new Error('请至少选择一个考勤机!'))
            }else{
                callback()
            }
        };
        return{
            // 头部查询
            formInline: {
                name:'',
                number:'',
                plant:'',
                workshop:'',
                productLine:'',
                department:'',
                isInput:'',
                team:'',
                selfModify:'1'
            },
            // 表格
            tableData:[],
            // 分页
            currentPage:1,
            pageSize:10,
            total:0,
            dialogModify:false,//批量修改dialog
            ruleModifyForm:{
                // team:'',
                role:'',
                isClock:[],
                imageUrl:''
            },
            rulesModify:{
                // team:[
                //     { validator: checkTeam, trigger: 'blur' }
                // ],
                role:[
                    { validator: checkRole, trigger: 'change' }
                ],
                isClock:[
                    { validator: checkClock, trigger: 'change' }
                ],
            },
            restaurants: [],//班组模糊查询列表
            roleOption:[],
            // 可打卡考勤机选择
            checkAll: false,
            machines: [],
            isIndeterminate: true,
            dialogTitle:'',
            dialogDetail:false,//查看详情dialog
            formDetail:{
                name:'',
                empId:'',
                plant:'',
                workshop:'',
                productionLine:'',
                department:'',
                // departChild:'',
                directName:'',
                actived:'',
                firstLevelMgrId:'',
                secondLevelMgrId:'',
                workHourType:'',
                workTeam:'',
                // isLoan:'',
                roleName:'',
                faceUrl:'',
                // machineName:''
            },
            multipleSelection: [],// 选中的员工信息
            employeeSelectIds:[],//已选的员工id
            // 人员导入dialog
            dialogImport:false,
            limitNum: 1,
            fileList: [],
            fileListE: [],
            header:{
                'Authorization' : localStorage.getItem('Auth-Token')
            },
            empId:'',//工号
            isSuccess:''//成功标识
        }
    },
    methods:{
        // 重置
        resetFormR(formName){
            this.$refs[formName].resetFields();
            this.$refs.multipleTable.clearSelection();
            this.currentPage = 1;
            this.initData();
        },

        // 取消
        resetForm(formName) {
            // this.$refs[formName].resetFields();
            this.isIndeterminate = true;
            this.$refs.multipleTable.clearSelection();
            this.dialogModify = false;
            // this.initData();
        },

        // 渲染数据 分页
        initData(){
            let data = {
                department:this.formInline.department,
                empId:this.formInline.number,
                imgStatus:this.formInline.isInput,
                name:this.formInline.name,
                offset:this.pageSize,
                plant:this.formInline.plant,
                productionLine:this.formInline.productLine,
                rows:this.currentPage,
                workTeam:this.formInline.team,
                workshop:this.formInline.workshop
            }
            this.$http.queryEmployeePage(data,(res)=>{

                if(res.code == 'SUCCESS'){
                    this.formInline.selfModify = (res.data.employeeImage).toString();
                    this.tableData = res.data.data;
                    this.total = res.data.count;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{

                this.$message.error(errRes.message);
            })
        },

        // 导出
        exportBtn(){
            let data = {
                department:this.formInline.department,
                empId:this.formInline.number,
                imgStatus:this.formInline.isInput,
                name:this.formInline.name,
                offset:this.pageSize,
                plant:this.formInline.plant,
                productionLine:this.formInline.productLine,
                rows:this.currentPage,
                workTeam:this.formInline.team,
                workshop:this.formInline.workshop
            }
            axios({
                url: 'api/admin/employeeController/exportEmployee',
                method: "POST",
                data:data,
                responseType:"blob",
                headers:{
                    // 'Content-Type':'application/json;charset=UTF-8',
                    'Authorization' : localStorage.getItem('Auth-Token')
                },
            }).then(function (res) {
                if(!res){
                    return
                }
                utils.downloadExcel(res.data,'员工信息导出 ','xlsx')//调用公共方法
            })
        },

        // 批量修改
        modifyBtn(){
            if(this.employeeSelectIds.length <= 0){
                this.$message.error("请先选择要修改的数据！");
            }else{
                this.dialogModify = true;
                this.dialogTitle = 0;
            }
        },

        // table查看详情
        handlelook(id) {
            this.dialogDetail = true;
            let data = {
                id:id
            }
            this.$http.queryEmployeeDetail(data,(res)=>{

                if(res.code == 'SUCCESS'){
                    this.formDetail = res.data;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{

                this.$message.error(errRes.message);
            })
        },

        // 关闭table查看详情
        cancelDetail(){
            this.formDetail = {
                name:'',
                empId:'',
                plant:'',
                workshop:'',
                productionLine:'',
                department:'',
                // departChild:'',
                directName:'',
                actived:'',
                firstLevelMgrId:'',
                secondLevelMgrId:'',
                workHourType:'',
                workTeam:'',
                // isLoan:'',
                roleName:'',
                faceUrl:'',
                // machineName:''
            }
        },

        // table编辑
        handleEdit(id) {
            let data = {
                id:id
            }
            this.$http.queryEmployeeDetail(data,(res)=>{

                if(res.code == 'SUCCESS'){
                    this.dialogModify = true;
                    this.dialogTitle = 1;
                    var newData = [];
                    var machineVos = res.data.machineVos;
                    if(machineVos != undefined){
                        for(let i = 0;i<machineVos.length;i++){
                            newData.push(machineVos[i].machineSn)
                        }
                    }else{
                        newData = []
                    }
                    // this.ruleModifyForm.team = res.data.workTeam;
                    this.ruleModifyForm.role = res.data.rnumber;
                    this.ruleModifyForm.isClock = newData;
                    this.ruleModifyForm.imageUrl = res.data.faceUrl;
                    this.empId = res.data.empId;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{

                this.$message.error(errRes.message);
            })
        },

        // // 班组下拉选
        // teamList(){
        //     this.$http.queryWorkItemList(null,(res)=>{
        //
        //         if(res.code == 'SUCCESS'){
        //             for(let i = 0;i<res.data.length;i++){
        //                 this.restaurants.push({"value":res.data[i]})
        //             }
        //         }else{
        //             this.$message.error(res.message);
        //         }
        //     },(errRes)=>{
        //
        //         this.$message.error(errRes.message);
        //     })
        // },

        // // 返回输入建议的方法，仅当你的输入建议数据 resolve 时，通过调用 callback(data:[]) 来返回它
        // querySearch(queryString, cb) {
        //     var restaurants = this.restaurants;
        //     var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
        //     // 调用 callback 返回建议列表的数据
        //     cb(results);
        // },

        // createFilter(queryString) {
        //     return (restaurant) => {
        //         return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
        //     };
        // },

        // // 在 Input 值改变时触发
        // handleSelect(item) {
        //     console.log(item);
        // },

        // 查询所有考勤机信息
        clockList(){
            this.$http.queryMachineListAll(null,(res)=>{

                if(res.code == 'SUCCESS'){
                   this.machines = res.data;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{

                this.$message.error(errRes.message);
            })
        },

        // 全选
        handleCheckAllChange(val) {
            this.ruleModifyForm.isClock = val ? this.machines : [];
            this.isIndeterminate = false;
        },

        // 单选
        handleCheckedChange(value) {
            let checkedCount = value.length;
            this.checkAll = checkedCount === this.machines.length;
            this.isIndeterminate = checkedCount > 0 && checkedCount < this.machines.length;
        },

        // 批量修改/编辑确定
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if(this.dialogTitle == 0){
                        let data = {
                            idArray:this.employeeSelectIds,
                            machines: this.ruleModifyForm.isClock
                        }
                        this.$http.updateEmployee(data,(res)=>{
                            if(res.code == 'SUCCESS'){
                                this.dialogModify = false;
                                this.$refs.multipleTable.clearSelection();
                                this.initData()
                                this.$message.success(res.message);
                            }else{
                                this.$message.error(res.message);
                            }
                        },(errRes)=>{
                            this.$message.error(errRes.message);
                        })
                    }else{
                        var idArr = [];
                        idArr.push(this.empId)
                        let data = {
                            idArray:idArr,
                            faceUrl: this.ruleModifyForm.imageUrl,
                            machines: this.ruleModifyForm.isClock,
                            rnumber:this.ruleModifyForm.role
                        }
                        this.$http.updateEmployee(data,(res)=>{
                            if(res.code == 'SUCCESS'){
                                this.dialogModify = false;
                                this.initData()
                                this.$message.success(res.message);
                            }else{
                                this.$message.error(res.message);
                            }
                        },(errRes)=>{
                            this.$message.error(errRes.message);
                        })
                    }
                } else {
                    return false;
                }
            });
        },

        // 文件上传成功时的钩子
        handleAvatarSuccess(res, file) {
            //后台返的数据
            this.ruleModifyForm.imageUrl = res.data;
            this.$message.success('图片上传成功!');
        },
        // 上传文件之前的钩子，参数为上传的文件，若返回 false 或者返回 Promise 且被 reject，则停止上传。
        beforeAvatarUpload(file) {
            const isJPG = file.type === 'image/jpeg';
            const isJPG1 = file.type === 'image/jpg';
            const isJPG2 = file.type === 'image/png';
            const isLt2M = file.size / 1024 / 1024 < 2;
            if (!isJPG && !isJPG1 && !isJPG2) {
            this.$message.error('上传头像图片只能是 JPG、PNG 格式!');
            }
            if (!isLt2M) {
            this.$message.error('上传头像图片大小不能超过 2MB!');
            }
            return (isJPG && isLt2M) || (isJPG1 && isLt2M) || (isJPG2 && isLt2M);
        },

        // 角色下拉选
        roleList(){
            this.$http.queryRoleListAll(null,(res)=>{
                if(res.code == 'SUCCESS'){
                   this.roleOption = res.data;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },

        // 表格全选
        handleSelectionChange(val) {
            this.multipleSelection = val;
            this.employeeSelectIds = val.map(item => {
                return item.empId
            })
        },

        // 获取row的key值
        getRowKeys(row) {
            return row.empId;
        },

        // 人员导入
        importBtn(){
            this.dialogImport = true;
        },
        // 上传文件之前的钩子，参数为上传的文件,若返回 false 或者返回 Promise 且被 reject，则停止上传
        beforeFileUpload (file){
            const extension = file.name.split('.')[1].toString().toLowerCase();
            if ((extension !== 'xls') && (extension !== 'xlsx')) {
                this.$message.warning('上传模板只能是 xls、xlsx格式!')
                return false
            }else{

            }
        },
        // 文件超出个数限制时的钩子
        exceedFile(files, fileList) {
            this.$message.warning({
                title: '警告',
                message: `只能选择 ${this.limitNum} 个文件，当前共选择了 ${files.length + fileList.length} 个`
            });
        },
        // 上传成功后的钩子
        handleFileSuccess(res, file) {
            if(res.code == 'SUCCESS'){
                if((this.fileList != '') && (this.fileListE != '')){
                    this.isSuccess = '0';//成功标识
                    this.$message.success("请再次点击'确定'进行第二次上传!");
                }else{
                    this.dialogImport = false;//关闭弹框
                    this.initData();
                    this.$message.success(res.message);
                }
            }else{
                this.$message.error(res.message);
            }
        },
        // 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用
        handleChange(file, fileList) {
            this.fileList = fileList;
        },

        // 上传文件之前的钩子，参数为上传的文件,若返回 false 或者返回 Promise 且被 reject，则停止上传
        beforeFileUploadE (file){
            const extension = file.name.split('.')[1].toString().toLowerCase();
            if ((extension !== 'xls') && (extension !== 'xlsx')) {
                this.$message.warning('上传模板只能是 xls、xlsx格式!')
                return false
            }else{

            }
        },
        // 文件超出个数限制时的钩子
        exceedFileE(files, fileList) {
            this.$message.warning({
                title: '警告',
                message: `只能选择 ${this.limitNum} 个文件，当前共选择了 ${files.length + fileList.length} 个`
            });
        },
        // 上传成功后的钩子
        handleFileSuccessE(res, file) {
            if(res.code == 'SUCCESS'){
                this.dialogImport = false;//关闭弹框
                this.initData();
                this.$message.success(res.message);
            }else{
                this.$message.error(res.message);
            }
        },
        // 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用
        handleChangeE(file, fileList) {
            this.fileListE = fileList;
        },

        // 确定
        submitUpload(){
            if((this.fileList == '') && (this.fileListE == '')){
                this.$message.error("请上传文件");
            }else if((this.fileList != '') && (this.fileListE == '')){
                this.$refs.uploadExcel.submit();
            }else if((this.fileList == '') && (this.fileListE != '')){
                this.$refs.uploadExcelE.submit();
            }else if((this.fileList != '') && (this.fileListE != '')){
                this.$refs.uploadExcel.submit();
                if(this.isSuccess == '0'){
                    this.$refs.uploadExcelE.submit();
                }
            }
        },
        // 取消
        cancelUpload(){
            this.fileList = [];
            this.fileListE = [];
            this.dialogImport = false;
        },

        // 员工自行修改照片
        changeImgUrl(){
            let data = {
                imgStatus:this.formInline.selfModify
            }
            this.$http.controlEmployeeImage(data,(res)=>{
                if(res.code == 'SUCCESS'){
                   this.$message.success(res.message);
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        }
    },
    mounted(){
        this.initData();
        // this.teamList();
        this.roleList();
        this.clockList();
    }
}
</script>
