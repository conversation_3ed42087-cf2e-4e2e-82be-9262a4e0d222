<template>
  <div class="leaveBox">
    <!-- 面包屑 start -->
    <el-breadcrumb separator="/">
      <el-breadcrumb-item>数据查询</el-breadcrumb-item>
      <el-breadcrumb-item>补卡记录查询</el-breadcrumb-item>
    </el-breadcrumb>
    <!-- 面包屑 end -->

    <!-- search start -->
    <div class="searchFrom">
      <el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
        <el-form-item label="姓名：" prop="name">
          <el-input v-model="formInline.name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="工号：" prop="number">
          <el-input v-model="formInline.number" placeholder="请输入工号"></el-input>
        </el-form-item>
        <el-form-item label="申请通过时间：" prop="finishTime">
          <el-date-picker v-model="formInline.finishTime" type="daterange" start-placeholder="开始时间" end-placeholder="结束时间"
            value-format="yyyy-MM-dd" range-separator="~" :clearable=false>
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="initData(currentPage = 1)">查询</el-button>
          <el-button @click="resetForm('formInline')">重置</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="exportBtn">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- search end -->

    <!-- table start -->
    <div class="tableContent">
      <table>
        <thead>
          <tr>
            <th>姓名</th>
            <th>工号</th>
            <th>补卡原因</th>
            <th>补卡日期</th>
            <th>备注</th>
            <th>进/出</th>
            <th>附件</th>
            <th>申请通过时间</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="tableData.length <= 0">
            <td colspan="8">暂无数据!</td>
          </tr>
          <tr v-for="(item, index) in tableData" :key='index' v-else>
            <td>{{ item.replenishUserName }}</td>
            <td>{{ item.replenishUser }}</td>
            <td>{{ item.typeName }}</td>
            <td>{{ item.replenishTime }}</td>
            <td>{{ item.reason }}</td>
            <td>{{ item.replenishStatusName }}</td>
            <td>
              <el-image v-if="item.imgUrl" :src="item.imgUrl" :preview-src-list="item.imgArray">
              </el-image>
            </td>
            <td>{{ item.finishTime }}</td>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- table end -->

    <!-- 分页 start -->
    <div class="paginationBox">
      <el-pagination v-show="total != 0" background layout="total, prev, pager, next, sizes, jumper" :total="total"
        :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData"
        @current-change="initData"></el-pagination>
    </div>
    <!-- 分页 end -->
  </div>
</template>
<script>
var axios = require('axios')// 引用axios
import utils from '../utils/index.js'//引用常用工具文件
export default {
  data() {
    return {
      // 头部查询
      formInline: {
        name: '',
        number: '',
        finishTime: ''
      },
      // 表格
      tableData: [],
      // 分页
      currentPage: 1,
      pageSize: 10,
      total: 0,
    }
  },
  methods: {
    // 重置
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.currentPage = 1;
      this.initData();
    },

    // 渲染数据 分页
    initData() {
      let data = {
        name: this.formInline.name,
        empId: this.formInline.number,
        offset: this.pageSize,
        rows: this.currentPage,
        finishStartDate: this.formInline.finishTime ? this.formInline.finishTime[0] : '',
        finishEndDate: this.formInline.finishTime ? this.formInline.finishTime[1] : '',
      }
      this.$http.queryReplenishPageList(data, (res) => {

        if (res.code == 'SUCCESS') {
          let resData = res.data.data;
          for (let i = 0; i < resData.length; i++) {
            if (resData[i].attaUrl) {
              resData[i]['imgUrl'] = resData[i].attaUrl.split(',')[0]
              resData[i]['imgArray'] = resData[i].attaUrl.split(',')
            }
          }
          this.tableData = resData;
          this.total = res.data.count;
        } else {
          this.$message.error(res.message);
        }
      }, (errRes) => {

        this.$message.error(errRes.message);
      })
    },

    // 导出
    exportBtn() {
      let data = {
        name: this.formInline.name,
        empId: this.formInline.number,
        offset: this.pageSize,
        rows: this.currentPage,
        finishStartDate: this.formInline.finishTime ? this.formInline.finishTime[0] : '',
        finishEndDate: this.formInline.finishTime ? this.formInline.finishTime[1] : '',
      }
      axios({
        url: 'api/admin/replenishAttendanceController/exportReplenishData',
        method: "POST",
        data: data,
        responseType: "blob",
        headers: {
          // 'Content-Type':'application/json;charset=UTF-8',
          'Authorization': localStorage.getItem('Auth-Token')
        },
      }).then(function (res) {
        if (!res) {
          return
        }
        utils.downloadExcel(res.data, '补卡记录导出 ', 'xlsx')//调用公共方法
      })
    },
  },
  mounted() {
    this.initData();
  }
}
</script>
