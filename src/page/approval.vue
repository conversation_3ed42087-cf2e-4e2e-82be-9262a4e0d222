<template>
    <div class="auxiliaryBox">
        <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>考勤设置</el-breadcrumb-item>
            <el-breadcrumb-item :to="{ path: '/auxiliary-information' }">辅助基础信息</el-breadcrumb-item>
            <el-breadcrumb-item>审批人员维护</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end --> 

        <!-- search start -->
        <div class="searchFrom">
            <el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
                <el-form-item label="医生工号：" prop="doctor">
                    <el-input v-model="formInline.doctor" placeholder="请输入医生工号"></el-input>
                </el-form-item>
                <el-form-item label="EHS：" prop="health">
                    <el-input v-model="formInline.health" placeholder="请输入EHS"></el-input>
                </el-form-item>
                <el-form-item label="薪资福利经理：" prop="welfare">
                    <el-input v-model="formInline.welfare" placeholder="请输入薪资福利经理"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="initData()">查询</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableContent">
            <table>
                <thead>
                    <tr>
                        <th>厂区</th>
                        <th>医生</th>
                        <th>EHS</th>
                        <th>薪资经理</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="tableData.length <= 0">
                        <td colspan="5">暂无数据!</td>
                    </tr>
                    <tr v-for="(item,index) in tableData" :key='index' v-else>
                        <td>{{item.plant}}</td>
                        <td>{{item.doctorName}}</td>
                        <td>{{item.healthName}}</td>
                        <td>{{item.welfareName}}</td>
                        <td>
                            <el-button type="text" @click="handleEdit(item)">编辑</el-button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- table end -->

        <!-- 编辑dialog start -->
        <el-dialog
            title="编辑"
            :visible.sync="dialogAdd"
            width="60%"
            :close-on-click-modal='false'
            @close="resetForm('ruleAddForm')"
            >
            <el-form :model="ruleAddForm" :rules="rulesAdd" ref="ruleAddForm" label-width="150px" class="demo-ruleForm">
                <el-form-item label="医生：" prop="doctor">
                    <el-select 
                        v-model="ruleAddForm.doctor" 
                        filterable 
                        multiple 
                        remote
                        :remote-method="remoteMethodDoctor"
                        placeholder="请选择医生" 
                        style="width:100%;"
                        :clearable = false>
                        <el-option
                            v-for="item in doctorOption"
                            :key="item.empId"
                            :label="item.empId+'|'+item.name"
                            :value="item.empId+'|'+item.name">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="EHS：" prop="health">
                    <el-select 
                        v-model="ruleAddForm.health" 
                        filterable 
                        remote
                        :remote-method="remoteMethodHealth"
                        placeholder="请选择EHS" 
                        style="width:100%;"
                        :clearable = false>
                        <el-option
                            v-for="item in healthOption"
                            :key="item.empId"
                            :label="item.empId+'|'+item.name"
                            :value="item.empId+'|'+item.name">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="薪资福利经理：" prop="welfare">
                    <el-select 
                        v-model="ruleAddForm.welfare" 
                        filterable 
                        remote
                        :remote-method="remoteMethodWelfare"
                        placeholder="请选择薪资福利经理" 
                        style="width:100%;"
                        :clearable = false>
                        <el-option
                            v-for="item in welfareOption"
                            :key="item.empId"
                            :label="item.empId+'|'+item.name"
                            :value="item.empId+'|'+item.name">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleAddForm')">确定</el-button>
                    <el-button @click="resetForm('ruleAddForm')">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 编辑dialog end -->
    </div>
</template>
<script>
export default {
    data(){
        return{
            // 头部查询
            formInline: {
                doctor:'',
                health:'',
                welfare:''
            },
            // 表格
            tableData:[],
            dialogAdd:false,//编辑dialog
            // 编辑Form
            ruleAddForm:{
                doctor:'',
                health:'',
                welfare:''
            },
            rulesAdd:{
                doctor: [
                    { required: true, message: '请选择医生', trigger: 'change' }
                ],
                health: [
                    { required: true, message: '请选择EHS', trigger: 'change' }
                ],
                welfare: [
                    { required: true, message: '请选择薪资福利经理', trigger: 'change' }
                ]
            },
            doctorOption:[],
            healthOption:[],
            welfareOption:[],
            checkId:''
        }
    },
    methods:{
        // 渲染数据 分页
        initData(){
            let data = {
                doctor:this.formInline.doctor,
                health:this.formInline.health,
                welfare:this.formInline.welfare
            }
            this.$http.queryCheckUserList(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    var resData = res.data;
                    // for(let i = 0;i<resData.length;i++){
                    //     var doctorJson = [];
                    //     var doctorArr = resData[i].dataMap;
                    //     for(let j = 0;j<doctorArr.length;j++){
                    //         doctorJson.push(doctorArr[j].name)
                    //     }
                    //     resData[i]['doctorName'] = doctorJson.join(',');
                    // }
                    this.tableData = resData;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },

        // table编辑
        handleEdit(item) {
            if(item.doctorName && item.doctorName != ''){
                this.ruleAddForm.doctor = item.doctorName.split(',');
            }else{
                this.ruleAddForm.doctor = [];
            }
            this.ruleAddForm.health = item.healthName;
            this.ruleAddForm.welfare = item.welfareName;
            this.checkId = item.checkId;
            this.dialogAdd = true;
        },

        // 添加/编辑确定
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    var doctor = this.ruleAddForm.doctor.map(item => {
                        return item.split('|')[0]
                    });;

                    let data = {
                        checkId:this.checkId,
                        doctor:doctor.join(','),
                        health:this.ruleAddForm.health.split('|')[0],
                        welfare:this.ruleAddForm.welfare.split('|')[0]
                    }
                    this.$http.updateCheckUser(data,(res)=>{
                        if(res.code == 'SUCCESS'){
                            this.dialogAdd = false;
                            this.$refs[formName].resetFields();
                            this.initData()
                            this.$message.success(res.message);
                        }else{
                            this.$message.error(res.message);
                        }
                    },(errRes)=>{
                        this.$message.error(errRes.message);
                    })
                } else {
                    return false;
                }
            });
        },

        // 取消
        resetForm(formName){
            this.$refs[formName].resetFields();
            this.dialogAdd = false;
        },

        // // 人员 下拉选
        // employList(){
        //     let data = {
        //         empId:'',
        //         plant:'',
        //     }
        //     this.$http.queryEmployeeByWorkTeam(data,(res)=>{
        //         if(res.code == 'SUCCESS'){
        //             this.doctorOption = res.data.data;
        //             this.healthOption = res.data.data;
        //             this.welfareOption = res.data.data;
        //         }else{
        //             this.$message.error(res.message);
        //         }
        //     },(errRes)=>{
        //         this.$message.error(errRes.message);
        //     })
        // },

        remoteMethodDoctor(query){
            if (query !== '') {
                let data = {
                    empId:query,
                    plant:'',
                }
                this.$http.queryEmployeeByWorkTeam(data,(res)=>{
                    if(res.code == 'SUCCESS'){
                        this.doctorOption = res.data.data;
                    }else{
                        this.$message.error(res.message);
                    }
                },(errRes)=>{
                    this.$message.error(errRes.message);
                })
            }else{
                this.doctorOption = [];
            }
        },

        remoteMethodHealth(query){
            if (query !== '') {
                let data = {
                    empId:query,
                    plant:'',
                }
                this.$http.queryEmployeeByWorkTeam(data,(res)=>{
                    if(res.code == 'SUCCESS'){
                        this.healthOption = res.data.data;
                    }else{
                        this.$message.error(res.message);
                    }
                },(errRes)=>{
                    this.$message.error(errRes.message);
                })
            }else{
                this.healthOption = [];
            }
        },

        remoteMethodWelfare(query){
            if (query !== '') {
                let data = {
                    empId:query,
                    plant:'',
                }
                this.$http.queryEmployeeByWorkTeam(data,(res)=>{
                    if(res.code == 'SUCCESS'){
                        this.welfareOption = res.data.data;
                    }else{
                        this.$message.error(res.message);
                    }
                },(errRes)=>{
                    this.$message.error(errRes.message);
                })
            }else{
                this.welfareOption = [];
            }
        },
    },
    mounted(){
        this.initData();
        // this.employList();
    }
}
</script>
