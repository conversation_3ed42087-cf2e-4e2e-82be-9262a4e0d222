<template>
    <div class="auxiliaryBox">
        <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>考勤设置</el-breadcrumb-item>
            <el-breadcrumb-item :to="{ path: '/auxiliary-information' }">辅助基础信息</el-breadcrumb-item>
            <el-breadcrumb-item>邮件接收人</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
                <el-form-item label="厂别：" prop="plant">
                    <el-select v-model="formInline.plant" placeholder="请选择厂别" clearable>
                        <el-option
                            v-for="item in plantOptions"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="车间：" prop="workshop">
                    <el-input v-model="formInline.workshop" placeholder="请输入车间" clearable></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="initData(currentPage = 1)">查询</el-button>
                    <el-button type="primary" @click="addBtn">新增</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableContent">
            <table>
                <thead>
                    <tr>
                        <th>厂别</th>
                        <th>车间</th>
                        <th>主管</th>
                        <th>经理</th>
                        <th>厂长</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="tableData.length <= 0">
                        <td colspan="6">暂无数据!</td>
                    </tr>
                    <tr v-for="(item,index) in tableData" :key='index' v-else>
                        <td>{{item.PLANT}}</td>
                        <td>{{item.WORKSHOP}}</td>
                        <td>{{item.LEADER_EMAIL}}</td>
                        <td>{{item.MANAGER_EMAIL}}</td>
                        <td>{{item.DIRECTOR_EMAIL}}</td>
                        <td>
                            <el-button type="text" @click="handleEdit(item.EMAIL_ID)">编辑</el-button>
                            <el-button type="text" @click="handleDelete(item.EMAIL_ID)">删除</el-button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- table end -->

        <!-- 分页 start -->
        <div class="paginationBox">
            <el-pagination v-show="total!=0" background layout="total, prev, pager, next, sizes, jumper" :total="total" :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData" @current-change="initData"></el-pagination>
        </div>
        <!-- 分页 end -->

        <!-- 新建dialog start -->
        <el-dialog
            :title="dialogTitle == 0 ? '新建' : '编辑'"
            :visible.sync="dialogAdd"
            width="60%"
            :close-on-click-modal='false'
            @close="resetForm('ruleAddForm')"
            >
            <el-form :model="ruleAddForm" :rules="rulesAdd" ref="ruleAddForm" label-width="100px" class="demo-ruleForm">
                <el-form-item label="厂别：" prop="plant">
                    <el-select v-model="ruleAddForm.plant" placeholder="请选择厂别" style="width: 100%">
                        <el-option
                            v-for="item in plantOptions"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="车间：" prop="workshop">
                    <el-input v-model="ruleAddForm.workshop" placeholder="请输入车间"></el-input>
                </el-form-item>
                <el-form-item label="主管邮箱：" prop="leaderEmail">
                    <el-input v-model="ruleAddForm.leaderEmail" placeholder="请输入主管邮箱"></el-input>
                </el-form-item>
                <el-form-item label="经理邮箱：" prop="managerEmail">
                    <el-input v-model="ruleAddForm.managerEmail" placeholder="请输入经理邮箱"></el-input>
                </el-form-item>
                <el-form-item label="厂长邮箱：" prop="directorEmail">
                    <el-input v-model="ruleAddForm.directorEmail" placeholder="请输入厂长邮箱"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleAddForm')">确定</el-button>
                    <el-button @click="resetForm('ruleAddForm')">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 新建dialog end -->
    </div>
</template>
<script>
export default {
    data(){
        return{
            // 头部查询
            formInline: {
                plant: '',
                workshop: ''
            },
            // 表格
            tableData:[],
            // 分页
            currentPage:1,
            pageSize:10,
            total:0,
            // 厂别选项
            plantOptions:[],
            dialogAdd:false,//新建dialog
            // 新建Form
            ruleAddForm:{
                plant: '',
                workshop: '',
                leaderEmail: '',
                managerEmail: '',
                directorEmail: ''
            },
            rulesAdd:{
                plant: [
                    { required: true, message: '请输入厂别', trigger: 'blur' }
                ],
                workshop: [
                    { required: true, message: '请输入车间', trigger: 'blur' }
                ],
                leaderEmail: [
                    { required: true, message: '请输入主管邮箱', trigger: 'blur' },
                    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
                ],
                managerEmail: [
                    { required: true, message: '请输入经理邮箱', trigger: 'blur' },
                    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
                ],
                directorEmail: [
                    { required: true, message: '请输入厂长邮箱', trigger: 'blur' },
                    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
                ]
            },
            dialogTitle:'',
            currentId: ''
        }
    },
    methods:{
        // 获取厂别选项
        getPlantOptions(){
          this.$http.queryNewPlantList(null,(res)=>{

            if(res.code == 'SUCCESS'){
              this.plantOptions = res.data;
            }else{
              this.$message.error(res.message);
            }
          },(errRes)=>{
            this.$message.error(errRes.message);
          })
        },
        // 渲染数据 分页
        initData(){
            let data = {
                plant: this.formInline.plant,
                workshop: this.formInline.workshop,
                offset: this.pageSize,
                rows: this.currentPage,
            }
            // 这里需要替换为实际的API接口
            this.$http.queryEmailReceiverPage(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.tableData = res.data.data;
                    this.total = res.data.count;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },
        // 新建
        addBtn() {
            this.dialogAdd = true;
            this.dialogTitle = 0;
            this.ruleAddForm = {
                plant: '',
                workshop: '',
                leaderEmail: '',
                managerEmail: '',
                directorEmail: ''
            }
        },

        // table编辑
        handleEdit(id) {
            let data = {
              emailId: id
            }
            // 这里需要替换为实际的API接口
            this.$http.queryEmailReceiverDetail(data,(res)=>{
                if(res.code == 'SUCCESS'){
                    this.dialogAdd = true;
                    this.dialogTitle = 1;
                    this.ruleAddForm.plant = res.data.PLANT;
                    this.ruleAddForm.workshop = res.data.WORKSHOP;
                    this.ruleAddForm.leaderEmail = res.data.LEADER_EMAIL;
                    this.ruleAddForm.managerEmail = res.data.MANAGER_EMAIL;
                    this.ruleAddForm.directorEmail = res.data.DIRECTOR_EMAIL;
                    this.currentId = res.data.EMAIL_ID;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                this.$message.error(errRes.message);
            })
        },

        // 添加/编辑确定
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if(this.dialogTitle == 0){
                        let data = {
                            plant: this.ruleAddForm.plant,
                            workshop: this.ruleAddForm.workshop,
                            leaderEmail: this.ruleAddForm.leaderEmail,
                            managerEmail: this.ruleAddForm.managerEmail,
                            directorEmail: this.ruleAddForm.directorEmail
                        }
                        // 这里需要替换为实际的API接口
                        this.$http.addEmailReceiver(data,(res)=>{
                            if(res.code == 'SUCCESS'){
                                this.dialogAdd = false;
                                this.$refs[formName].resetFields();
                                this.initData()
                                this.$message.success(res.message);
                            }else{
                                this.$message.error(res.message);
                            }
                        },(errRes)=>{
                            this.$message.error(errRes.message);
                        })
                    }else{
                        let data = {
                            emailId: this.currentId,
                            plant: this.ruleAddForm.plant,
                            workshop: this.ruleAddForm.workshop,
                            leaderEmail: this.ruleAddForm.leaderEmail,
                            managerEmail: this.ruleAddForm.managerEmail,
                            directorEmail: this.ruleAddForm.directorEmail
                        }
                        // 这里需要替换为实际的API接口
                        this.$http.updateEmailReceiver(data,(res)=>{
                            if(res.code == 'SUCCESS'){
                                this.dialogAdd = false;
                                this.$refs[formName].resetFields();
                                this.initData()
                                this.$message.success(res.message);
                            }else{
                                this.$message.error(res.message);
                            }
                        },(errRes)=>{
                            this.$message.error(errRes.message);
                        })
                    }
                } else {
                    return false;
                }
            });
        },

        // 取消
        resetForm(formName){
            this.$refs[formName].resetFields();
            this.dialogAdd = false;
        },

        // table删除
        handleDelete(id) {
            this.$confirm('确认删除？','提示',{
                type: 'warning',
            }).then(() => {
                let data = {
                    emailId: id
                }
                // 这里需要替换为实际的API接口
                this.$http.deleteEmailReceiver(data,(res)=>{
                    if(res.code == 'SUCCESS'){
                        this.initData();
                        this.$message.success(res.message);
                    }else{
                        this.$message.error(res.message);
                    }
                },(errRes)=>{
                    this.$message.error(errRes.message);
                })
            }).catch(() => {

            });
        }
    },
    mounted(){
        this.getPlantOptions();
        this.initData();
    }
}
</script>
