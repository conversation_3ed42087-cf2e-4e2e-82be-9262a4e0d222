<template>
    <div class="populationBox">
       <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>报告数据查询</el-breadcrumb-item>
            <el-breadcrumb-item>每月人数报告</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
                <el-form-item label="年：" prop="startYear" class="weekday">
                    <el-date-picker
                        v-model="formInline.startYear"
                        type="year"
                        placeholder="请选择年份"
                        value-format="yyyy"
                        :clearable = false>
                    </el-date-picker>
                    <!-- <el-form-item prop="startYear">
                        <el-date-picker
                            v-model="formInline.startYear"
                            type="year"
                            :picker-options="pickerOptionsStart"
                            placeholder="开始年份"
                            :clearable = false>
                        </el-date-picker>
                    </el-form-item>
                    <span>~</span>
                    <el-form-item prop="endYear" class="endWeek">
                        <el-date-picker
                            v-model="formInline.endYear"
                            type="year"
                            :picker-options="pickerOptionsEnd"
                            placeholder="结束年份"
                            :clearable = false>
                        </el-date-picker>
                    </el-form-item> -->
                </el-form-item>
                <el-form-item label="厂区+车间：" prop="moreLevel">
                    <el-cascader
                        v-model="formInline.moreLevel"
                        :options="moreOptions"
                        @change="handleChange"
                        :props="{ checkStrictly: true ,label: 'label',value: 'label', children: 'children'}"
                        placeholder="请选择"
                        clearable>
                    </el-cascader>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="initData" v-loading.fullscreen.lock="fullscreenLoading">查询</el-button>
                    <el-button type="primary" @click="exportBtn">导出</el-button>
                    <!-- <el-button type="primary">生成</el-button> -->
                </el-form-item>
            </el-form>
        </div>
        <!-- search end -->

        <!-- echart start -->
        <div v-for="(item,index) in tableData" :key='index' class="chartLines">
            <div :id="'chart_'+index" style="width:50%;height:400px;"></div>
            <div :id="'chartChild_'+index" style="width:50%;height:400px;"></div>
        </div>
        <!-- echart end -->

        <!-- 分页 start -->
        <!-- <div class="paginationBox">
            <el-pagination v-show="total!=0" background layout="total, prev, pager, next, sizes, jumper" :page-sizes="pageSizes" :total="total" :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData" @current-change="initData"></el-pagination>
        </div> -->
        <!-- 分页 end -->
    </div>
</template>
<script>
// import echarts from 'echarts'
const echarts = require('echarts');
var axios = require('axios')// 引用axios
import utils from '../utils/index.js'//引用常用工具文件
export default {
    data(){
        return{
            fullscreenLoading: false,
            // 头部查询
            formInline: {
                startYear:'',
                // endYear:''
                moreLevel:'',
            },
            moreOptions:[],
            // pickerOptionsStart: { //disabledDate 文档上：设置禁用状态，参数为当前日期，要求返回 Boolean
            //     disabledDate: time => {
            //         let endYearVal = this.formInline.endYear;
            //         if (endYearVal) {
            //             return time.getTime() > new Date(endYearVal).getTime();
            //         }
            //     }
            // },
            // pickerOptionsEnd: {
            //     disabledDate: time => { //disabledDate 文档上：设置禁用状态，参数为当前日期，要求返回 Boolean
            //         let beginYearVal = this.formInline.startYear;
            //         if (beginYearVal) {
            //             return time.getTime() < new Date(beginYearVal).getTime();
            //         }
            //     },
            // },
            tableData:[],
            // 分页
            currentPage:1,
            pageSize:2,
            total:0,
            pageSizes:[2,4],
            plant:'',
            workshop:'',
            picList:[]
        }
    },
    methods:{
        // 渲染数据 分页
        initData(){
            this.fullscreenLoading = true;
            let data = {
                year:this.formInline.startYear,
                offset:this.pageSize,
                rows:this.currentPage,
                plant:this.plant,
                workshop:this.workshop
            }
            this.picList = []
            this.$http.queryUserCountReportPage(data,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    this.fullscreenLoading = false;
                    
                    this.tableData = res.data.data;
                    this.total = res.data.count;

                    for(let i = 0;i<res.data.data.length;i++){
                        this.getChart(res.data.data[i],i);
                        this.getChartChild(res.data.data[i],i);
                        // this.$nextTick(()=> { this.getChart(res.data.data[i],i); getChartChild(res.data.data[i],i); }) 
                    }
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },

        // 导出
        exportBtn(){
            let data = {
                year:this.formInline.startYear,
                offset:this.pageSize,
                rows:this.currentPage,
                plant:this.plant,
                workshop:this.workshop,
                imgUrls:this.picList
            }
            axios({
                url: 'api/admin/userCountReportController/exportMonthlyHcReport',
                method: "POST",
                data:data,
                responseType:"blob",
                headers:{
                    // 'Content-Type':'application/json;charset=UTF-8',
                    'Authorization' : localStorage.getItem('Auth-Token')
                },
            }).then(function (res) {
                if(!res){
                    return
                }
                utils.downloadExcel(res.data,'每月人数报告 ','xlsx')//调用公共方法
            })
        },

        // EPZ Plant
        getChart(item,i){
            var dataList = item.dataList;
            var oneList = [];
            var twoList = [];
            var xList = [];
            for(let j = 0;j<dataList.length;j++){
                oneList.push(dataList[j].direct);
                twoList.push(dataList[j].indirect);
                xList.push(dataList[j].statisticsDate);
            }
            this.$nextTick(() => {
                let myChart = echarts.init(document.getElementById('chart_'+i));
                let option = {
                    // 标题
                    animation: false,
                    title: {
                        text: item.plant+"    "+item.workshop,
                        left:'center'
                    },
                    legend: {
                        // left:'center',
                        top:'40',
                        data:['直接人数','间接人数']
                    },
                    color:['#0068cc','#ff8000'],
                    // 直角坐标系内绘图网格
                    grid:{
                        top:'80',
                        containLabel: true//grid 区域是否包含坐标轴的刻度标签。
                    },
                    // 提示
                    tooltip: {
                        //触发类型,axis:坐标轴触发，主要在柱状图，折线图等会使用类目轴的图表中使用。
                        trigger: "axis",
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效，默认为直线'
                            type: "shadow"
                        }
                    },
                    //直角坐标系 grid 中的 x 轴
                    xAxis: [
                        {
                            //'category' 类目轴
                            type: "category",
                            boundaryGap:true,
                            // 坐标轴刻度相关设置
                            axisTick : {
                                show:false
                            },
                            axisLabel: {
                                interval:0,//0表示强制显示所有标签，设置为1的话，隔一个标签显示一个标签，以此类推
                                rotate:45,//代表逆时针旋转45度
                            },
                            //坐标数据
                            data: xList
                        }
                    ],
                    //直角坐标系 grid 中的 y 轴
                    yAxis: [
                        {
                            //'value' 数值轴，适用于连续数据
                            type: "value",
                            // 坐标轴轴线相关设置
                            axisLine: {
                                show:false
                            },
                            // 坐标轴刻度相关设置
                            axisTick : {
                                show:false
                            },
                        }
                    ],
                    //系列列表。每个系列通过 type 决定自己的图表类型
                    series: [
                        {
                            type:'bar',
                            name:'直接人数',
                            stack: '人数',
                            label: {//图形上的文本标签
                                normal: {
                                    show: true,
                                    position: 'inside',
                                    // color:'#333333'
                                    formatter: function (params) {
                                        if (params.value > 0) {
                                            return params.value;
                                        } else {
                                            return '';
                                        }
                                    }
                                }
                            },
                            data:oneList
                        },
                        {
                            type:'bar',
                            name:'间接人数',
                            stack: '人数',
                            label: {//图形上的文本标签
                                normal: {
                                    show: true,
                                    position: 'inside',
                                    // color:'#333333'
                                    formatter: function (params) {
                                        if (params.value > 0) {
                                            return params.value;
                                        } else {
                                            return '';
                                        }
                                    }
                                }
                            },
                            data:twoList
                        },
                    ]
                }
                myChart.setOption(option);
                this.picList.push(myChart.getDataURL())
            })
        },

        // chartChild
        getChartChild(item,i){
            var dataList = item.dataList;
            var xList = [];
            var legendData = [];
            var series = [];
            for(let m = 0;m<dataList.length;m++){
                xList.push(dataList[m].statisticsDate);
                if(dataList[m].userCountList != null){
                    for(let n = 0;n<dataList[m].userCountList.length;n++){
                        let dataArr = [];
                        dataArr.push({
                            value:[m,dataList[m].userCountList[n].direct]
                        })
                        legendData.push(dataList[m].userCountList[n].position)

                        series.push({
                            type:'bar',
                            name:dataList[m].userCountList[n].position,
                            stack: '人数',
                            label: {//图形上的文本标签
                                normal: {
                                    show: true,
                                    position: 'inside',
                                    // color:'#333333',
                                    formatter: function (params) {
                                        if (params.value[1] > 0) {
                                            return params.value[1];
                                        } else {
                                            return '';
                                        }
                                    }
                                }
                            },
                            data:dataArr
                        })
                    }
                } 
            }
            this.$nextTick(() => {
                let myChart = echarts.init(document.getElementById('chartChild_'+i));
                let option = {
                    // 标题
                    animation: false,
                    title: {
                        text: item.plant+"    "+item.workshop,
                        left:'center'
                    },
                    legend: {
                        // left:'center',
                        top:'40',
                        data:legendData
                    },
                    // color:['#0068cc','#ff8000'],
                    // 直角坐标系内绘图网格
                    grid:{
                        top:'80',
                        containLabel: true//grid 区域是否包含坐标轴的刻度标签。
                    },
                    // 提示
                    tooltip: {
                        //触发类型,axis:坐标轴触发，主要在柱状图，折线图等会使用类目轴的图表中使用。
                        trigger: "axis",
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效，默认为直线'
                            type: "shadow"
                        }
                    },
                    //直角坐标系 grid 中的 x 轴
                    xAxis: [
                        {
                            //'category' 类目轴
                            type: "category",
                            boundaryGap:true,
                            // 坐标轴刻度相关设置
                            axisTick : {
                                show:false
                            },
                            axisLabel: {
                                interval:0,//0表示强制显示所有标签，设置为1的话，隔一个标签显示一个标签，以此类推
                                rotate:45,//代表逆时针旋转45度
                            },
                            //坐标数据
                            data: xList
                        }
                    ],
                    //直角坐标系 grid 中的 y 轴
                    yAxis: [
                        {
                            //'value' 数值轴，适用于连续数据
                            type: "value",
                            // 坐标轴轴线相关设置
                            axisLine: {
                                show:false
                            },
                            // 坐标轴刻度相关设置
                            axisTick : {
                                show:false
                            },
                        }
                    ],
                    //系列列表。每个系列通过 type 决定自己的图表类型
                    series: series
                }
                myChart.setOption(option);
                this.picList.push(myChart.getDataURL())
            })
        },

        // 厂区车间选择
        handleChange(value) {
            this.plant = value[0];
            this.workshop = value[1];
        },

        // 所有厂区车间信息
        getAllMessage(){
            let data = {
                plant:''
            }
            this.$http.queryPlantWorkshopAll(data,(res)=>{
                
                if(res.code == 'SUCCESS'){
                    var tempArr = [];
                    var nowArr = [];
                    for (var i = 0; i < res.data.data.length; i++) {
                        if (tempArr.indexOf(res.data.data[i].plant) === -1) {
                            nowArr.push({
                                label: res.data.data[i].plant,
                                children: [res.data.data[i]]
                            });
                            tempArr.push(res.data.data[i].plant);
                        } else {
                            for (var j = 0; j < nowArr.length; j++) {
                                if (nowArr[j].label == res.data.data[i].plant) {
                                    nowArr[j].children.push(res.data.data[i]);
                                    break;
                                }
                            }
                        }
                    }
                    for(let m = 0;m<nowArr.length;m++){
                        for(let n = 0;n<nowArr[m].children.length;n++){
                            nowArr[m].children[n]["label"] = nowArr[m].children[n].workshop;
                        }
                    }
                    this.moreOptions = nowArr;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{
                
                this.$message.error(errRes.message);
            })
        },
    },
    mounted(){
        var datD = new Date(); //当前格林时间 
        var nianD = datD.getFullYear();//当前年份 
        this.formInline.startYear = nianD.toString();

        // this.initData();
        this.getAllMessage();
    }
}
</script>
