<template>
    <div class="attendanceExceptionQueryBox">
       <!-- 面包屑 start -->
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>报告数据查询</el-breadcrumb-item>
            <el-breadcrumb-item>每日考勤异常报告</el-breadcrumb-item>
        </el-breadcrumb>
        <!-- 面包屑 end -->

        <!-- search start -->
        <div class="searchFrom">
            <el-form :inline="true" :model="formInline" ref="formInline" class="demo-form-inline">
                <div class="form-left">
                    <el-form-item label="日期：" prop="term">
                        <el-date-picker
                            v-model="formInline.term"
                            type="date"
                            placeholder="请选择日期"
                            value-format="yyyy-MM-dd"
                            :clearable = true>
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="工号：" prop="empId">
                        <el-input v-model="formInline.empId" placeholder="请输入工号"></el-input>
                    </el-form-item>
                    <el-form-item label="姓名：" prop="name">
                        <el-input v-model="formInline.name" placeholder="请输入姓名"></el-input>
                    </el-form-item>
                    <el-form-item label="工厂：" prop="eplant">
                        <el-input v-model="formInline.eplant" placeholder="请输入工厂"></el-input>
                    </el-form-item>
                    <el-form-item label="班组：" prop="workTeam">
                        <el-input v-model="formInline.workTeam" placeholder="请输入班组"></el-input>
                    </el-form-item>
                    <el-form-item label="考勤月：" prop="month">
                      <el-date-picker
                        v-model="formInline.month"
                        type="date"
                        placeholder="请选择考勤月"
                        value-format="yyyy-MM"
                        :clearable = true>
                      </el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="initData(currentPage = 1)">查询</el-button>
                        <!-- <el-button type="primary" @click="exportBtn">导出</el-button> -->
                    </el-form-item>
                </div>
            </el-form>
        </div>
        <!-- search end -->

        <!-- table start -->
        <div class="tableInfo">
            <span class="scrollTip">💡 表格内容较多，可横向滚动查看完整信息</span>
        </div>
        <div class="tableContent">
            <table>
                <thead>
                    <tr>
                        <th>日期</th>
                        <th>工号</th>
                        <th>姓名</th>
                        <th>公司</th>
                        <th>班次</th>
                        <th>班组</th>
                        <th>生产线</th>
                        <th>VSM</th>
                        <th>异常类型</th>
                        <th>刷卡开始时间</th>
                        <th>刷卡结束时间</th>
                        <th>异常?</th>
                        <th>奇次卡次数</th>
                        <th>应工作时数</th>
                        <th>工作时数</th>
                        <th>旷工时数</th>
                        <th>迟到分钟</th>
                        <th>早退分钟</th>
                        <th>OT1.5加班</th>
                        <th>OT2.0加班</th>
                        <th>OT3.0加班</th>
                        <th>超时工作</th>
                        <th>转调休加班</th>
                        <th>用餐次数</th>
                        <th>事假</th>
                        <th>调休假（公司原因）</th>
                        <th>调休</th>
                        <th>年假</th>
                        <th>法定年假</th>
                        <th>福利年假</th>
                        <th>全薪病假</th>
                        <th>扣薪病假</th>
                        <th>工伤假</th>
                        <th>丧假</th>
                        <th>出差</th>
                        <th>婚假</th>
                        <th>产假检查假</th>
                        <th>陪产假</th>
                        <th>产假</th>
                        <th>难产假</th>
                        <th>哺乳假</th>
                        <th>晚育假</th>
                        <th>计划生育假</th>
                        <th>流产假</th>
                        <th>停产假</th>
                        <th>育儿假</th>
                        <th>护理假</th>
                        <th>轮班次数</th>
                        <th>分析状态</th>
                        <th>刷卡忽略</th>
                        <th>主管</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="tableData.length <= 0">
                        <td colspan="12">暂无数据!</td>
                    </tr>
                    <tr v-for="(item,index) in tableData" :key='index' v-else>
                        <td>{{item.TERM}}</td>
                        <td>{{item.EMP_ID}}</td>
                        <td>{{item.NAME}}</td>
                        <td>{{item.EPLANT}}</td>
                        <td>{{item.SHIFT}}</td>
                        <td>{{item.WORK_TEAM}}</td>
                        <td>{{item.LINE_ID}}</td>
                        <td>{{item.VSM}}</td>
                        <td>{{item.EXCEPTION_TYPE}}</td>
                        <td>{{formatDateTime(item.BEGIN_TIME)}}</td>
                        <td>{{formatDateTime(item.END_TIME)}}</td>
                        <td>{{item.IS_EXCEPTION == 1 ? '是' : '否'}}</td>
                        <td>{{item.CDER}}</td>
                        <td>{{item.PNHR}}</td>
                        <td>{{item.WKHR}}</td>
                        <td>{{item.UAHR}}</td>
                        <td>{{item.LIMN}}</td>
                        <td>{{item.EOMN}}</td>
                        <td>{{item.OT1}}</td>
                        <td>{{item.OT2}}</td>
                        <td>{{item.OT3}}</td>
                        <td>{{item.REWK}}</td>
                        <td>{{item.CLOT1}}</td>
                        <td>{{item.TNOM}}</td>
                        <td>{{item.PERL}}</td>
                        <td>{{item.OCCL}}</td>
                        <td>{{item.OTCL}}</td>
                        <td>{{item.ANNL}}</td>
                        <td>{{item.ANST}}</td>
                        <td>{{item.ANWE}}</td>
                        <td>{{item.PSKL}}</td>
                        <td>{{item.BPSL}}</td>
                        <td>{{item.ISJL}}</td>
                        <td>{{item.COML}}</td>
                        <td>{{item.FANF}}</td>
                        <td>{{item.MARL}}</td>
                        <td>{{item.MLPL}}</td>
                        <td>{{item.PATL}}</td>
                        <td>{{item.MATL}}</td>
                        <td>{{item.DSFL}}</td>
                        <td>{{item.BRFL}}</td>
                        <td>{{item.CIFL}}</td>
                        <td>{{item.FPGL}}</td>
                        <td>{{item.MSCL}}</td>
                        <td>{{item.POFL}}</td>
                        <td>{{item.CHILDL}}</td>
                        <td>{{item.NURSL}}</td>
                        <td>{{item.NBOS}}</td>
                        <td>{{item.ANALY_MODE}}</td>
                        <td>{{item.PROCESS_FLAG == 1 ? '是' : '否'}}</td>
                        <td>{{item.SUPERVISOR_NAME}}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- table end -->

        <!-- 分页 start -->
        <div class="paginationBox">
            <el-pagination v-show="total!=0" background layout="total, prev, pager, next, sizes, jumper" :total="total" :current-page.sync="currentPage" :page-size.sync="pageSize" @size-change="initData" @current-change="initData"></el-pagination>
        </div>
        <!-- 分页 end -->
    </div>
</template>
<script>
import utils from '../utils/index.js'//引用常用工具文件
export default {
    data(){
        return{
            // 头部查询
            formInline: {
                term:'',
                empId:'',
                name:'',
                eplant:'',
                workTeam:'',
                month:''
            },
            // 表格
            tableData:[],
            // 分页
            currentPage:1,
            pageSize:10,
            total:0,
        }
    },
    methods:{
        // 渲染数据
        initData(){
            let data = {
                empId:this.formInline.empId,
                name:this.formInline.name,
                term:this.formInline.term,
                eplant:this.formInline.eplant,
                workTeam:this.formInline.workTeam,
                month: this.formInline.month,
                offset:this.pageSize,
                rows:this.currentPage
            }
            this.$http.queryDailyAttendanceAnalysisPage(data,(res)=>{

                if(res.code == 'SUCCESS'){
                    this.tableData = res.data.varList;
                    this.total = res.data.count;
                }else{
                    this.$message.error(res.message);
                }
            },(errRes)=>{

                this.$message.error(errRes.message);
            })
        },

        // 导出
        exportBtn(){
            let data = {
                empId:this.formInline.empId,
                name:this.formInline.name,
                term:this.formInline.term,
                eplant:this.formInline.eplant,
                workTeam:this.formInline.workTeam,
                offset:this.pageSize,
                rows:this.currentPage
            }
            this.$http.exportDailyAttendanceAnalysis(data,(res)=>{
                if(!res){
                    return
                }
                utils.downloadExcel(res.data,'考勤异常查询 ','xlsx')//调用公共方法
            },(errRes)=>{
                this.$message.error('导出失败：' + errRes.message);
            })
        },

        // 格式化日期时间
        formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '';
            const date = new Date(dateTimeStr);
            if (isNaN(date.getTime())) return dateTimeStr;

            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}`;
        }
    },
    mounted(){
        this.initData();
    }
}
</script>
<style lang="scss" scoped>
@import '../style/scss/attendance-exception-query.scss';
</style>
