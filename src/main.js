// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
// 引用 ElementUI
// 低版本浏览器兼容
import "babel-polyfill"

import Es6Promise from 'es6-promise'
Es6Promise.polyfill()

import 'element-ui/lib/theme-chalk/index.css';
import ElementUI from 'element-ui';
Vue.use(ElementUI);
import Vue from 'vue'
import App from './App'
import router from './router'

// 引用API文件
import url from './api/url/url.js'
// 将API方法绑定到全局
Vue.prototype.$http = url

// // 引用工具文件
// import utils from './utils/index.js'
// // 将工具方法绑定到全局
// Vue.prototype.$utils = utils

// 引用 axios
// import axios from 'axios'
// Vue.prototype.$linkage = axios

import "./filter"

// 引用 vue2-datepick
import Calendar from 'vue2-datepick';
Vue.use(Calendar);

// 引入echarts
// import echarts from "echarts"
// Vue.prototype.$echarts = echarts

// 注册组件库 引入组件CSS样式
import vueHashCalendar from 'vue-hash-calendar'
import 'vue-hash-calendar/lib/vue-hash-calendar.css'
Vue.use(vueHashCalendar)

// 调用moment.js
import moment from 'moment'//导入文件
Vue.prototype.$moment = moment;//赋值使用
moment.locale('zh-cn');//需要汉化

// 滚动加载
import  infiniteScroll from 'vue-infinite-scroll'
Vue.use( infiniteScroll);

Vue.config.productionTip = false

/* eslint-disable no-new */
new Vue({
  el: '#app',
  router,
  components: { App },
  template: '<App/>'
})

