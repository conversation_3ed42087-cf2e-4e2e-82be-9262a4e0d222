import axios from "../index.js"

var auxiliaryInformation = {

    // 下载成本中心信息
	downloadCostCenter:function(data,success,failure){
		return axios.post("/admin/relationDataController/downloadCostCenter",data,success,failure);
    },

    // 下载职位信息
	downloadPosition:function(data,success,failure){
		return axios.post("/admin/relationDataController/downloadPosition",data,success,failure);
    },

    // 成本中心导入
	importCostCenter:function(data,success,failure){
		return axios.post("/admin/relationDataController/importCostCenter",data,success,failure);
    },

    // 职位信息导入
	importPosition:function(data,success,failure){
		return axios.post("/admin/relationDataController/importPosition",data,success,failure);
    },

    // 成本中心分页查询
	queryCostCenterPage:function(data,success,failure){
		return axios.post("/admin/relationDataController/queryCostCenterPage",data,success,failure);
    },

    // 分页查询部门信息
	queryDepartmentPage:function(data,success,failure){
		return axios.post("/admin/relationDataController/queryDepartmentPage",data,success,failure);
    },

    // 查询厂区信息
	queryPlantList:function(data,success,failure){
		return axios.post("/admin/relationDataController/queryPlantList",data,success,failure);
    },

  queryNewPlantList:function(data,success,failure){
    return axios.post("/admin/relationDataController/queryNewPlantList",data,success,failure);
  },

    // 职位信息分页查询
	queryPositionPage:function(data,success,failure){
		return axios.post("/admin/relationDataController/queryPositionPage",data,success,failure);
	},
}

export default auxiliaryInformation
