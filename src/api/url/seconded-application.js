import axios from "../index.js"

var SecondedApplication = {
    
    // 领班审批借调
	wxapproveBorrow:function(data,success,failure){
		return axios.wxPost("/admin/borrowController/approveBorrow",data,success,failure);
	},

	// 查询借调申请明细
	wxqueryBorrowDetail:function(data,success,failure){
		return axios.wxPost("/admin/borrowController/queryBorrowDetail",data,success,failure);
    },
    
    // 发起借调申请分页查询
    wxqueryBorrowPage:function(data,success,failure){
        return axios.wxPost("/admin/borrowController/queryBorrowPage",data,success,failure);
    },

    // 查询所有的领班
    wxqueryLeaderAll:function(data,success,failure){
        return axios.wxPost("/admin/borrowController/queryLeaderAll",data,success,failure);
    },

    // 查询待处理借调申请
    wxBorrowQueryPendingList:function(data,success,failure){
        return axios.wxPost("/admin/borrowController/queryPendingList",data,success,failure);
    },

    // 查询已处理借调申请
    wxqueryProcessedList:function(data,success,failure){
        return axios.wxPost("/admin/borrowController/queryProcessedList",data,success,failure);
    },

    // 提交借调申请
    wxsubmitBorrow:function(data,success,failure){
        return axios.wxPost("/admin/borrowController/submitBorrow",data,success,failure);
    },
}

export default SecondedApplication