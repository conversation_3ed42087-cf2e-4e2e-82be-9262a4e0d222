import axios from "../index.js"

var teamManagement = {
    
    // 初始化班组信息
    initWorkGroup:function(data,success,failure){
        return axios.post("/admin/workGroupController/initWorkGroup",data,success,failure);
    },

    // 查询班组详细信息
    queryWorkGroupDetail:function(data,success,failure){
        return axios.post("/admin/workGroupController/queryWorkGroupDetail",data,success,failure);
    },

    // 分页查询班组信息
    queryWorkGroupPage:function(data,success,failure){
        return axios.post("/admin/workGroupController/queryWorkGroupPage",data,success,failure);
    },

    // 修改班组信息
    updateWorkGroup:function(data,success,failure){
        return axios.post("/admin/workGroupController/updateWorkGroup",data,success,failure);
    },

    // 查询所有有效班组
    queryWorkGroupAll:function(data,success,failure){
        return axios.post("/admin/workGroupController/queryWorkGroupAll",data,success,failure);
    },

    // 查询所有的领班班组
    queryWorkGroupLeaderAll:function(data,success,failure){
        return axios.post("/admin/workGroupController/queryWorkGroupLeaderAll",data,success,failure);
    },

    // 根据班组查询班组下的人员(传厂区.车间.班组.工号)
    queryEmployeeByWorkTeam:function(data,success,failure){
        return axios.post("/admin/employeeController/queryEmployeeByWorkTeam",data,success,failure);
    },

    // 根据班组名称查询班组信息
    queryWorkGroupDetailByName:function(data,success,failure){
        return axios.post("/admin/workGroupController/queryWorkGroupDetailByName",data,success,failure);
    },
}

export default teamManagement