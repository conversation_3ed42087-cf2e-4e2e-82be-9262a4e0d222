import axios from "../index.js"

var attendanceSummary = {

    // 月度考勤汇总
    monthlyAttendance: function (data, success, failure) {
        return axios.post("/admin/monthlyAttendanceAnalysisStatusController/queryPage", data, success, failure);
    },
    // 考勤人员状态
    monthlyAttendanceEmployee: function (data, success, failure) {
        return axios.post("/admin/monthlyAttendanceAnalysisDetailController/queryPage", data, success, failure);
    },
    // 发起考勤确认
    initiateAttendanceConfirmation: function (data, success, failure) {
        return axios.post("/admin/monthlyAttendanceAnalysisStatusController/initiateAttendanceConfirmation", data, success, failure);
    },
    // 考勤统计
    wxMonthlyAttendanceConfirm: function (data, success, failure) {
        return axios.wxPost("/admin/wechatController/queryMonthlyAttendanceInfoForConfirm", data, success, failure);
    },
    // 考勤人员确认
    wxConfirmMonthlyAttendance: function (data, success, failure) {
        return axios.wxPost("/admin/wechatController/confirmMonthlyAttendance", data, success, failure);
    },
    // 导出考勤详情Excel
    exportAttendanceExcel: function (data, success, failure) {
        return axios.downloadPost("/admin/monthlyAttendanceAnalysisDetailController/exportExcel", data, success, failure);
    },
    // 重发考勤确认通知
    reSendAttendanceConfirmation: function (data, success, failure) {
        return axios.post("/admin/monthlyAttendanceAnalysisStatusController/reSendAttendanceConfirmation", data, success, failure);
    },
}

export default attendanceSummary 