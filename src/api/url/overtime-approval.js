import axios from "../index.js"

var overtimeApproval = {
    
    // 添加加班申请
	wxaddOvertimeApply:function(data,success,failure){
		return axios.wxPost("/admin/overtimeApplyController/addOvertimeApply",data,success,failure);
	},

    // 员工撤回加班操作
	wxcancelApprove:function(data,success,failure){
		return axios.wxPost("/admin/overtimeApplyController/cancelApprove",data,success,failure);
    },
    
    // 员工加班确认
    wxemployeeApprove:function(data,success,failure){
        return axios.wxPost("/admin/overtimeApplyController/employeeApprove",data,success,failure);
    },
    
    // 查询当前主管所有下级员工
    wxqueryChildUser:function(data,success,failure){
        return axios.wxPost("/admin/overtimeApplyController/queryChildUser",data,success,failure);
    },

    // 已处理加班审批
    wxqueryCompleteOvertimePage:function(data,success,failure){
        return axios.wxPost("/admin/overtimeApplyController/queryCompleteOvertimePage",data,success,failure);
    },

    // 我的加班申请
    wxqueryMyOvertimePage:function(data,success,failure){
        return axios.wxPost("/admin/overtimeApplyController/queryMyOvertimePage",data,success,failure);
    },

    // 查询加班申请详细信息
    wxqueryOvertimeDetail:function(data,success,failure){
        return axios.wxPost("/admin/overtimeApplyController/queryOvertimeDetail",data,success,failure);
    },

    // 待处理加班审批
    wxqueryPendingOvertime:function(data,success,failure){
        return axios.wxPost("/admin/overtimeApplyController/queryPendingOvertime",data,success,failure);
    },

    // 领班处理加班申请
    wxapproveOvertime:function(data,success,failure){
        return axios.wxPost("/admin/overtimeApplyController/approveOvertime",data,success,failure);
    },
    
    // 审批人审批加班申请
    wxapproveOvertimeApply:function(data,success,failure){
        return axios.wxPost("/admin/overtimeApplyController/approveOvertimeApply",data,success,failure);
    },

    // 加班确认状态(只需要传processInstanceId)
    wxqueryOvertimeApplyType:function(data,success,failure){
        return axios.wxPost("/admin/overtimeApplyController/queryOvertimeApplyType",data,success,failure);
    },

    // 查询待处理加班确认
    wxqueryOvertimePage:function(data,success,failure){
        return axios.wxPost("/admin/overtimeApplyController/queryOvertimePage",data,success,failure);
    },

    // 查询我确认的加班申请(员工加班)
    wxqueryOvertimeValidList:function(data,success,failure){
        return axios.wxPost("/admin/overtimeApplyController/queryOvertimeValidList",data,success,failure);
    },

    // WEB分页查询加班记录
    queryOvertimeApplyPage:function(data,success,failure){
        return axios.post("/admin/overtimeApplyController/queryOvertimeApplyPage",data,success,failure);
    },

    // 加班时长
    calculateOvertimeTime:function(data,success,failure){
        return axios.wxPost("/admin/overtimeApplyController/calculateOvertimeTime",data,success,failure);
    },

    // 查询加班申请员工答复明细
    queryOvertimeUserAllByProcess:function(data,success,failure){
        return axios.wxPost("/admin/overtimeApplyController/queryOvertimeUserAllByProcess",data,success,failure);
    },
}

export default overtimeApproval