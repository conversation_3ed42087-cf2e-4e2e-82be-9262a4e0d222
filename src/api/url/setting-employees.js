import axios from "../index.js"

var settingEmployees = {
    
    // 添加不计工时员工信息
    addOutRangeEmployee:function(data,success,failure){
        return axios.post("/admin/outRangeEmployeeController/addOutRangeEmployee",data,success,failure);
    },

    // 删除不计工时员工信息
    deleteOutRangeEmployee:function(data,success,failure){
        return axios.post("/admin/outRangeEmployeeController/deleteOutRangeEmployee",data,success,failure);
    },

    // 查询不计工时员工信息
    queryOutRangeEmployeeDetail:function(data,success,failure){
        return axios.post("/admin/outRangeEmployeeController/queryOutRangeEmployeeDetail",data,success,failure);
    },

    // 分页查询不计工时员工信息
    queryOutRangeEmployeePage:function(data,success,failure){
        return axios.post("/admin/outRangeEmployeeController/queryOutRangeEmployeePage",data,success,failure);
    },
}

export default settingEmployees