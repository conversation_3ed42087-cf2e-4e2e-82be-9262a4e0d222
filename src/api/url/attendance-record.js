import axios from "../index.js"

var attendanceRecord = {
    
    // 添加考勤记录信息
    addAttendance:function(data,success,failure){
        return axios.post("/admin/attendanceController/addAttendance",data,success,failure);
    },

    // 导出考勤记录数据
    exportAttendance:function(data,success,failure){
        return axios.post("/admin/attendanceController/exportAttendance",data,success,failure);
    },

    // 分页查询考勤记录
    queryAttendancePage:function(data,success,failure){
        return axios.post("/admin/attendanceController/queryAttendancePage",data,success,failure);
    },

    // // 下载考勤记录
    // downloadAttendance:function(data,success,failure){
    //     return axios.post("/admin/attendanceController/downloadAttendance",data,success,failure);
    // },

    // 查询对应的领班下员工的连续工作天数(H5)
    wxqueryCautionEmployee:function(data,success,failure){
        return axios.wxPost("/admin/attendanceController/queryCautionEmployee",data,success,failure);
    },
}

export default attendanceRecord