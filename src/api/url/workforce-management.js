import axios from "../index.js"

var workforceManagement = {

    // 初始化当月排班
	initPlanSchedule:function(data,success,failure){
		return axios.post("/admin/scheduleController/initPlanSchedule",data,success,failure);
    },

    // 分页查询排班管理
	queryPlanSchedulePage:function(data,success,failure){
		return axios.post("/admin/scheduleController/queryPlanSchedulePage",data,success,failure);
	},

	// web端单个修改排班
	updateInternetWorkTeamSchedule:function(data,success,failure){
		return axios.post("/admin/scheduleController/updateInternetWorkTeamSchedule",data,success,failure);
	},


    // 添加排班规则
	addScheduleRule:function(data,success,failure){
		return axios.post("/admin/scheduleRuleController/addScheduleRule",data,success,failure);
    },

    // 分页查询排班规则
	queryScheduleRulePage:function(data,success,failure){
		return axios.post("/admin/scheduleRuleController/queryScheduleRulePage",data,success,failure);
	},

	// 单个删除排班规则
	deleteScheduleRule:function(data,success,failure){
		return axios.post("/admin/scheduleRuleController/deleteScheduleRule",data,success,failure);
	},

	// 单个修改排班规则
	updateScheduleRule:function(data,success,failure){
		return axios.post("/admin/scheduleRuleController/updateScheduleRule",data,success,failure);
	},

	// H5按日期查询排班(只需传年月)
	wxqueryPlanScheduleByDate:function(data,success,failure){
		return axios.wxPost("/admin/scheduleController/queryPlanScheduleByDate",data,success,failure);
	},

	// H5根据员工号修改排班调整
	wxupdateUserSchedule:function(data,success,failure){
		return axios.wxPost("/admin/scheduleController/updateUserSchedule",data,success,failure);
	},

	// H5根据日期进行调整排班
	wxupdateWorkTeamSchedule:function(data,success,failure){
		return axios.wxPost("/admin/scheduleController/updateWorkTeamSchedule",data,success,failure);
	},

	// 批量修改班次
	queryEmployeeSchedulePage:function(data,success,failure){
		return axios.post("/admin/scheduleController/queryEmployeeSchedulePage",data,success,failure);
	},

	// 批量修改班次 编辑
	updateEmployeeSchedule:function(data,success,failure){
		return axios.post("/admin/scheduleController/updateEmployeeSchedule",data,success,failure);
	},
}

export default workforceManagement
