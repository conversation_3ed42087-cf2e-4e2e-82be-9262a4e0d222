import axios from "../index.js"

var attendanceMachine = {
    
    // 单个添加考勤机信息
    addMachine:function(data,success,failure){
        return axios.post("/admin/machineController/addMachine",data,success,failure);
    },

    // 单个删除考勤机信息
    deleteMachine:function(data,success,failure){
        return axios.post("/admin/machineController/deleteMachine",data,success,failure);
    },

    // 根据Id查询考勤机信息
    queryMachineDetail:function(data,success,failure){
        return axios.post("/admin/machineController/queryMachineDetail",data,success,failure);
    },

    // 分页查询考勤机信息
    queryMachinePage:function(data,success,failure){
        return axios.post("/admin/machineController/queryMachinePage",data,success,failure);
    },

    // 单个修改考勤机信息
    updateMachine:function(data,success,failure){
        return axios.post("/admin/machineController/updateMachine",data,success,failure);
    },
}

export default attendanceMachine