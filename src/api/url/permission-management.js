import axios from "../index.js"

var permissionManagement = {
    
    // 查询所有菜单列表
	queryMenuAll:function(data,success,failure){
		return axios.post("/admin/userMenuController/queryMenuAll",data,success,failure);
	},

	// 查询所有角色
	queryRoleList:function(data,success,failure){
		return axios.post("/admin/roleMenuController/queryRoleList",data,success,failure);
	},

	// 添加角色
	addRole:function(data,success,failure){
		return axios.post("/admin/roleMenuController/addRole",data,success,failure);
	},

	// 删除角色信息(只需要传角色编号)
	deleteRole:function(data,success,failure){
		return axios.post("/admin/roleMenuController/deleteRole",data,success,failure);
	},

	// 查询角色信息(只需要传角色编号)
	queryRoleDetail:function(data,success,failure){
		return axios.post("/admin/roleMenuController/queryRoleDetail",data,success,failure);
	},

	// 修改角色信息
	updateRoleName:function(data,success,failure){
		return axios.post("/admin/roleMenuController/updateRoleName",data,success,failure);
	},

	// 批量添加角色和菜单绑定关系
	addRoleMenuList:function(data,success,failure){
		return axios.post("/admin/roleMenuController/addRoleMenuList",data,success,failure);
	},

	// 下载角色成员
	downloadRoleEmployee:function(data,success,failure){
		return axios.post("/admin/roleMenuController/downloadRoleEmployee",data,success,failure);
	},

	// 角色成员导入
	importRoleEmployee:function(data,success,failure){
		return axios.post("/admin/roleMenuController/importRoleEmployee",data,success,failure);
	},

	// 根据角色编号分页员工列表
	queryEmployeeByRole:function(data,success,failure){
		return axios.post("/admin/roleMenuController/queryEmployeeByRole",data,success,failure);
	},

	// 添加角色成员
	addEmployeeByRole:function(data,success,failure){
		return axios.post("/admin/roleMenuController/addEmployeeByRole",data,success,failure);
	},

	// 删除角色下员工信息
	deleteEmployeeByRole:function(data,success,failure){
		return axios.post("/admin/roleMenuController/deleteEmployeeByRole",data,success,failure);
	},

	// 根据角色编号查询角色与菜单绑定关系
	queryRoleMenuByRoleNumber:function(data,success,failure){
		return axios.post("/admin/roleMenuController/queryRoleMenuByRoleNumber",data,success,failure);
	},
	
}

export default permissionManagement