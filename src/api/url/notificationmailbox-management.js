import axios from "../index.js"

var notificationmailboxManagement = {
    
    // 单个添加邮箱地址
	addEmail:function(data,success,failure){
		return axios.post("/admin/emailController/addEmail",data,success,failure);
    },
    
    // 单个删除邮箱地址
	deleteEmail:function(data,success,failure){
		return axios.post("/admin/emailController/deleteEmail",data,success,failure);
    },
    
    // 分页查询邮箱信息
	queryEmailPage:function(data,success,failure){
		return axios.post("/admin/emailController/queryEmailPage",data,success,failure);
    },
    
    // 单个修改邮箱地址
	updateEmail:function(data,success,failure){
		return axios.post("/admin/emailController/updateEmail",data,success,failure);
	}
}

export default notificationmailboxManagement