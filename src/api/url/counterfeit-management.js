import axios from "../index.js"

var counterfeitManagement = {
    
    // 单个添加假别信息
    addLeaveType:function(data,success,failure){
        return axios.post("/admin/leaveTypeController/addLeaveType",data,success,failure);
    },

    // 单个删除信息
    deleteLeaveType:function(data,success,failure){
        return axios.post("/admin/leaveTypeController/deleteLeaveType",data,success,failure);
    },

    // 查询假别信息
    queryLeaveTypeDetail:function(data,success,failure){
        return axios.post("/admin/leaveTypeController/queryLeaveTypeDetail",data,success,failure);
    },

    // 分页查询假别信息
    queryLeaveTypePage:function(data,success,failure){
        return axios.post("/admin/leaveTypeController/queryLeaveTypePage",data,success,failure);
    },

    // 单个修改信息
    updateLeaveType:function(data,success,failure){
        return axios.post("/admin/leaveTypeController/updateLeaveType",data,success,failure);
    },
}

export default counterfeitManagement