import axios from "../index.js"

var reportEmail = {

  queryEmailReceiverPage:function(data,success,failure){
		return axios.post("/admin/reportEmail/queryPage",data,success,failure);
    },

  queryEmailReceiverDetail:function(data,success,failure){
    return axios.post("/admin/reportEmail/queryDetail",data,success,failure);
  },

  addEmailReceiver:function(data,success,failure){
    return axios.post("/admin/reportEmail/add",data,success,failure);
  },

  updateEmailReceiver:function(data,success,failure){
    return axios.post("/admin/reportEmail/update",data,success,failure);
  },

  deleteEmailReceiver:function(data,success,failure){
    return axios.post("/admin/reportEmail/delete",data,success,failure);
  },

}

export default reportEmail
