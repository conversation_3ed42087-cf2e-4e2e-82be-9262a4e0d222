import axios from "../index.js"

var login = {
    
    // 用户登录
	login:function(data,success,failure){
		return axios.post("/admin/userController/userLogin",data,success,failure);
	},

	// 查询角色菜单
	queryRoleMenuList:function(data,success,failure){
		return axios.post("/admin/userController/queryRoleMenuList",data,success,failure);
	},

	// 判断是否会是领班
	queryEmployeeIsLeader:function(data,success,failure){
		return axios.post("/admin/employeeController/queryEmployeeIsLeader",data,success,failure);
	},

	// 修改密码
	updateUserPassword:function(data,success,failure){
		return axios.post("/admin/userController/updateUserPassword",data,success,failure);
	},
}

export default login