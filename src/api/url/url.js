
// 登录
import login from './login.js'
// 员工管理
import employeeManagement from './employee-management.js'
// 考勤机管理
import attendanceMachine from './attendance-machine.js'
// 出勤记录查询
import attendanceRecord from './attendance-record.js'
// 工作日历管理
import calendarManagement from './calendar-management.js'
// 班次管理
import shiftManagement from './shift-management.js'
// 班组管理
import teamManagement from './team-management.js'
// 线路管理
import busManagement from './bus-management.js'
// 辅助基础信息
import auxiliaryInformation from './auxiliary-information.js'
// 邮件接收人
import reportEmail from './report-email.js'
// 产线和VSM对照表
import plVsm from './pl-vsm.js'
// 假别管理
import counterfeitManagement from './counterfeit-management.js'
// 加班类别管理
import overtimeManagement from './overtime-management.js'
// 出勤异常查询
import attendanceException from './attendance-exception.js'
// 不计工时员工设置
import settingEmployees from './setting-employees.js'
// 员工班车统计
import staffShuttle from './staff-shuttle.js'
// 排班管理
import workforceManagement from './workforce-management.js'
// 财务月
import financial from './financial.js'
// 每月人数报告
import monthlyPopulation from './monthly-population.js'
// 月加班汇总报告
import monthlyOvertime from './monthly-overtime.js'
// 直接人力工时报告
import directManpower from './direct-manpower.js'
// 通知邮箱管理
import notificationmailboxManagement from './notificationmailbox-management.js'
// 权限管理
import permissionManagement from './permission-management.js'
// 撤销申请
import withdrawalApplication from './withdrawal-application.js'
// CEC签核权限管理
import cecManagement from './cec-management'
// 假期余额管理
import holidayBalance from './holiday-balance.js'


// 微信
import wechatManagement from './wechat-management.js'
// 请假审批
import leaveApproval from './leave-approval.js'
// 加班审批
import overtimeApproval from './overtime-approval.js'
// 补卡申请
import supplementarycartApplication from './supplementarycart-application.js'
// 借调申请
import SecondedApplication from './seconded-application.js'
// 考勤汇总
import Attendanceummary from './attendance-summary.js'

let allUrlList = Object.assign(
    {},login,employeeManagement,attendanceMachine,attendanceRecord,calendarManagement
    ,shiftManagement,teamManagement,busManagement,auxiliaryInformation,counterfeitManagement,overtimeManagement
    ,attendanceException,settingEmployees,staffShuttle,workforceManagement,financial,monthlyPopulation
    ,monthlyOvertime,directManpower,permissionManagement,withdrawalApplication,
    wechatManagement,leaveApproval,overtimeApproval,supplementarycartApplication,SecondedApplication,
    notificationmailboxManagement,cecManagement,holidayBalance,Attendanceummary,reportEmail,plVsm
);
export default allUrlList
