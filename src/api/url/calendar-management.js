import axios from "../index.js"

var calendarManagement = {
    
    // 初始化本年的周末与休息日
    initWorkCalendar:function(data,success,failure){
        return axios.post("/admin/workCalendarController/initWorkCalendar",data,success,failure);
    },

    // 查询月份的工作日历
    queryWorkCalendarList:function(data,success,failure){
        return axios.post("/admin/workCalendarController/queryWorkCalendarList",data,success,failure);
    },

    // 批量修改工作日历
    updateWorkCalendarList:function(data,success,failure){
        return axios.post("/admin/workCalendarController/updateWorkCalendarList",data,success,failure);
    },
}

export default calendarManagement