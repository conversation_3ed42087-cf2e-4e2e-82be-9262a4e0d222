import axios from "../index.js"

var wechatManagement = {

    // 登录页跳转
	wxlanding:function(data,success,failure){
		return axios.wxGet("/admin/wechatController/landing",data,success,failure);
    },

	// 查询某员工某日的打卡记录
	wxqueryAttendance:function(data,success,failure){
		return axios.wxPost("/admin/wechatController/queryAttendance",data,success,failure);
    },

    // 查询考勤统计
	wxqueryAttendanceAnalysisData:function(data,success,failure){
		return axios.wxPost("/admin/wechatController/queryAttendanceAnalysisData",data,success,failure);
    },

    // 查询员工个人信息
    wxqueryEmployeeDetail:function(data,success,failure){
        return axios.wxPost("/admin/wechatController/queryEmployeeDetail",data,success,failure);
    },

    // 修改员工头像
    wxupdateEmployeeFaceImage:function(data,success,failure){
        return axios.wxPost("/admin/wechatController/updateEmployeeFaceImage",data,success,failure);
    },

    // 绑定微信号
    wxupdateEmployeeOpenId:function(data,success,failure){
        return axios.wxGet("/admin/wechatController/updateEmployeeOpenId",data,success,failure);
    },

    // 查询某员工某月打卡日志
    wxqueryAttendanceMonth:function(data,success,failure){
        return axios.wxPost("/admin/wechatController/queryAttendanceMonth",data,success,failure);
    },

    // 获取jsskd配置信息
    wxjssdk:function(data,success,failure){
        return axios.wxGet("/admin/wx/jssdk",data,success,failure);
    },

    // 上传微信素材接口
    wxuploadMedia:function(data,success,failure){
        return axios.wxPost("/admin/wx/uploadMedia",data,success,failure);
    },

    // 查询某员工是否已经验证照片
    wxqueryAttendanceValid:function(data,success,failure){
        return axios.wxPost("/admin/wechatController/queryAttendanceValid",data,success,failure);
    },

    // 查询员工信息详情(判断 员工/领班)
    wxcheckEmployeeDetail:function(data,success,failure){
        return axios.wxPost("/admin/employeeController/checkEmployeeDetail",data,success,failure);
    },

    // 查询考勤日历排班情况(只需要传日期年月)
    wxqueryScheduleByEmployee:function(data,success,failure){
        return axios.wxPost("/admin/scheduleController/queryScheduleByEmployee",data,success,failure);
    },

    // 考勤 查看排班
    wxqueryBorrowUserDetail:function(data,success,failure){
        return axios.wxPost("/admin/borrowController/queryBorrowUserDetail",data,success,failure);
    },

    // 某一月考勤
    wxqueryAttendanceMonthByKayan: function (data, success, failure) {
      return axios.wxPost("/admin/wechatController/queryAttendanceMonthByKayan", data, success, failure);
    },
    // 某一天考勤
    wxqueryAttendanceByKayan: function (data, success, failure) {
      return axios.wxPost("/admin/wechatController/queryAttendanceByKayan", data, success, failure);
    },
    // wxSchedulinghange: function (data, success, failure) {
    //   return axios.wxPost("/admin/wechatController/listEmployeeWhichChangeSchedulesBySupervisor", data, success, failure);
    // },
}

export default wechatManagement
