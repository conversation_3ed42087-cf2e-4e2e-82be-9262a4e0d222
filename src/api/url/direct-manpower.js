import axios from "../index.js"

var directManpower = {
    
    // 根据年月查询财务月数据
	queryFinanceDateDetail:function(data,success,failure){
		return axios.post("/admin/financeDateController/queryFinanceDateDetail",data,success,failure);
	},

	// 直接人力工时报告数据导入
	importHCDetail:function(data,success,failure){
		return axios.post("/admin/reportHcController/importHCDetail",data,success,failure);
	},

	// 查询直接人力报告
	queryReportHCPage:function(data,success,failure){
		return axios.post("admin/reportHcController/queryReportHCPage",data,success,failure);
	},
}

export default directManpower