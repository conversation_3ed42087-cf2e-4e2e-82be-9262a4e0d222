import axios from "../index.js"

var leaveApproval = {

  // 添加请假申请
  wxaddLeave: function (data, success, failure) {
    return axios.wxPost("/admin/leaveController/addLeave", data, success, failure);
  },

  // 审批工作流
  wxapproveProcess: function (data, success, failure) {
    return axios.wxPost("/admin/leaveController/approveProcess", data, success, failure);
  },

  // 取消请假
  wxcancelLeave: function (data, success, failure) {
    return axios.wxPost("/admin/leaveController/cancelLeave", data, success, failure);
  },

  //查询请假详细信息
  wxqueryLeaveDetail: function (data, success, failure) {
    return axios.wxPost("/admin/leaveController/queryLeaveDetail", data, success, failure);
  },

  // 查询请假申请记录
  wxqueryLeavePage: function (data, success, failure) {
    return axios.wxPost("/admin/leaveController/queryLeavePage", data, success, failure);
  },

  // 查询审批数量
  wxqueryProcessNumber: function (data, success, failure) {
    return axios.wxPost("/admin/leaveController/queryProcessNumber", data, success, failure);
  },

  // 查询所有的假别
  wxqueryLeaveTypeAll: function (data, success, failure) {
    return axios.wxPost("/admin/leaveTypeController/queryLeaveTypeAll", data, success, failure);
  },

  // 查询请假审批人
  wxqueryProcessPerson: function (data, success, failure) {
    return axios.wxPost("/admin/leaveController/queryProcessPerson", data, success, failure);
  },

  // 查询待处理请假
  wxqueryProcessList: function (data, success, failure) {
    return axios.wxPost("/admin/leaveController/queryProcessList", data, success, failure);
  },

  // 分页查询已处理请假数据
  wxqueryCompleteList: function (data, success, failure) {
    return axios.wxPost("/admin/leaveController/queryCompleteList", data, success, failure);
  },

  // 查询所有加班类别
  wxqueryOvertimeType: function (data, success, failure) {
    return axios.wxPost("/admin/overtimeTypeController/queryOvertimeType", data, success, failure);
  },

  // WEB查询请假申请记录
  queryLeaveInternetPage: function (data, success, failure) {
    return axios.post("/admin/leaveController/queryLeaveInternetPage", data, success, failure);
  },

  // 分页查询补卡记录
  queryReplenishPageList: function (data, success, failure) {
    return axios.post("/admin/replenishAttendanceController/queryReplenishPageList", data, success, failure);
  },

  // H5查询请假申请记录数量
  wxqueryLeaveTypeCount: function (data, success, failure) {
    return axios.wxPost("/admin/leaveController/queryLeaveTypeCount", data, success, failure);
  },
  
  // 计算请假时长(传开始和结束时间以及请假类型)
  wxsumLeaveCount: function (data, success, failure) {
    return axios.wxPost("/admin/leaveController/sumLeaveCount", data, success, failure);
  },
  wxQueryHolidayBalance: function (data, success, failure) {
    return axios.wxPost("/admin/leaveController/queryHolidayBalance",data, success, failure);
  },
}

export default leaveApproval