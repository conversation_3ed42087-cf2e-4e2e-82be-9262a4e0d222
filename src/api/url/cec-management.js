import axios from "../index.js"

var cecManagement = {

    // 单个添加
    addCec:function(data,success,failure){
        return axios.post("/admin/cecController/add",data,success,failure);
    },

    // 单个删除信息
    deleteCec:function(data,success,failure){
        return axios.post("/admin/cecController/delete",data,success,failure);
    },

    // 根据Id查询
    queryCecDetail:function(data,success,failure){
        return axios.post("/admin/cecController/detail",data,success,failure);
    },

    // 分页查询班次信息
    queryCecPage:function(data,success,failure){
        return axios.post("/admin/cecController/page",data,success,failure);
    },

    // 单个修改信息
    updateCec:function(data,success,failure){
        return axios.post("/admin/cecController/update",data,success,failure);
    },

    // 查询所有
    queryCecAll:function(data,success,failure){
        return axios.post("/admin/cecController/all",data,success,failure);
    },

}

export default cecManagement
