import axios from "../index.js"

var busManagement = {
    
    // 添加班车信息
    addLine:function(data,success,failure){
        return axios.post("/admin/lineController/addLine",data,success,failure);
    },

    // 删除班车信息
    deleteLine:function(data,success,failure){
        return axios.post("/admin/lineController/deleteLine",data,success,failure);
    },

    // 根据Id查询班车信息
    queryLineDetail:function(data,success,failure){
        return axios.post("/admin/lineController/queryLineDetail",data,success,failure);
    },

    // 分页查询班车信息
    queryLinePage:function(data,success,failure){
        return axios.post("/admin/lineController/queryLinePage",data,success,failure);
    },

    // 修改班车信息
    updateLine:function(data,success,failure){
        return axios.post("/admin/lineController/updateLine",data,success,failure);
    },

    // 获取所有班车信息(H5)
    wxqueryLineAll:function(data,success,failure){
        return axios.wxPost("/admin/lineController/queryLineAll",data,success,failure);
    },


    // 单个添加线路管理信息
    addStation:function(data,success,failure){
        return axios.post("/admin/stationController/addStation",data,success,failure);
    },

    // 单个删除线路管理信息
    deleteStation:function(data,success,failure){
        return axios.post("/admin/stationController/deleteStation",data,success,failure);
    },

    // 根据Id查询线路管理信息
    queryStationDetail:function(data,success,failure){
        return axios.post("/admin/stationController/queryStationDetail",data,success,failure);
    },

    // 分页查询线路管理信息
    queryStationPage:function(data,success,failure){
        return axios.post("/admin/stationController/queryStationPage",data,success,failure);
    },

    // 修改线路管理信息
    updateStation:function(data,success,failure){
        return axios.post("/admin/stationController/updateStation",data,success,failure);
    },

    // 根据线路ID查询所有站点信息(只传线路ID)(H5)
    wxqueryStationByLineAll:function(data,success,failure){
        return axios.wxPost("/admin/stationController/queryStationByLineAll",data,success,failure);
    },
}

export default busManagement