import axios from "../index.js"

var financial = {
    
    // 添加财务月时间
    addFinanceDate:function(data,success,failure){
        return axios.post("/admin/financeDateController/addFinanceDate",data,success,failure);
    },

    // 删除财务月时间(只需要传Id)
    deleteFinanceDate:function(data,success,failure){
        return axios.post("/admin/financeDateController/deleteFinanceDate",data,success,failure);
    },

    // 查询财务月时间明细(只需要传Id)
    queryFinanceDateDetail:function(data,success,failure){
        return axios.post("/admin/financeDateController/queryFinanceDateDetail",data,success,failure);
    },

    // 分页查询财务月时间
    queryFinanceDatePage:function(data,success,failure){
        return axios.post("/admin/financeDateController/queryFinanceDatePage",data,success,failure);
    },

    // 修改财务月时间
    updateFinanceDate:function(data,success,failure){
        return axios.post("/admin/financeDateController/updateFinanceDate",data,success,failure);
    },

    // 添加审核辅助信息
    addCheckUser:function(data,success,failure){
        return axios.post("/admin/checkUserController/addCheckUser",data,success,failure);
    },

    // 查看审核人员信息列表
    queryCheckUserList:function(data,success,failure){
        return axios.post("/admin/checkUserController/queryCheckUserList",data,success,failure);
    },

    // 修改审核辅助信息
    updateCheckUser:function(data,success,failure){
        return axios.post("/admin/checkUserController/updateCheckUser",data,success,failure);
    },
}

export default financial