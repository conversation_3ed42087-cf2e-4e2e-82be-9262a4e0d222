import axios from "../index.js"

var staffShuttle = {

    // 添加路线站点员工绑定
    addEmployeeLineStation:function(data,success,failure){
        return axios.wxPost("/admin/employeeLineStationController/addEmployeeLineStation",data,success,failure);
    },
    
    // 分页查询员工班车统计
    queryEmployeeLineStationPage:function(data,success,failure){
        return axios.post("/admin/employeeLineStationController/queryEmployeeLineStationPage",data,success,failure);
    },

    // H5分页查询员工班车统计
    wxqueryEmployeeLineDetail:function(data,success,failure){
        return axios.wxPost("/admin/employeeLineStationController/queryEmployeeLineDetail",data,success,failure);
    },
}

export default staffShuttle