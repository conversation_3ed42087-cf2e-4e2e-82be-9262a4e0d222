import axios from "../index.js"

var supplementarycartApplication = {
    
  // 审批人审批补卡申请
	wxApproveReplenish:function(data,success,failure){
		return axios.wxPost("/admin/replenishAttendanceController/approveReplenish",data,success,failure);
  },

  // 查询待处理补卡申请
	wxQueryPendingList:function(data,success,failure){
		return axios.wxPost("/admin/replenishAttendanceController/queryPendingList",data,success,failure);
  },

  // 查询已处理补卡申请
	wxQueryProcessedList:function(data,success,failure){
		return axios.wxPost("/admin/replenishAttendanceController/queryProcessedList",data,success,failure);
  },
    
  // 查看补卡申请详细
	wxQueryReplenishDetail:function(data,success,failure){
		return axios.wxPost("/admin/replenishAttendanceController/queryReplenishDetail",data,success,failure);
  },
    
  // 分页查询我的补卡申请
	wxQueryReplenishPage:function(data,success,failure){
		return axios.wxPost("/admin/replenishAttendanceController/queryReplenishPage",data,success,failure);
  },

  // 员工发起补卡申请
	wxSubmitReplenish:function(data,success,failure){
		return axios.wxPost("/admin/replenishAttendanceController/submitReplenish",data,success,failure);
  }
}

export default supplementarycartApplication