import axios from "../index.js"

var monthlyOvertime = {
    
    // 分页查询月加班汇总报告
	queryReportOvertimePage:function(data,success,failure){
		return axios.post("/admin/reportOvertimeController/queryReportOvertimePage",data,success,failure);
	},

	// 月加班汇总报告数据导入
	importOvertimeDetail:function(data,success,failure){
		return axios.post("/admin/reportOvertimeController/importOvertimeDetail",data,success,failure);
	},

	// 查询所有车间信息 Workshop
	queryPlantWorkshopAll:function(data,success,failure){
		return axios.post("/admin/reportOvertimeController/queryPlantWorkshopAll",data,success,failure);
	},

	// 查询所有车间信息 VSM
	queryPlantVSMAll:function(data,success,failure){
		return axios.post("/admin/reportOvertimeController/queryPlantVSMAll",data,success,failure);
	},
}

export default monthlyOvertime