import axios from "../index.js"

var attendanceException = {
    
    // 考勤异常查询详情
	queryAttendanceAnalysisDetail:function(data,success,failure){
		return axios.post("/admin/attendanceAnalysisController/queryAttendanceAnalysisDetail",data,success,failure);
    },
    
    // 分页查询考勤异常
	queryAttendanceAnalysisPage:function(data,success,failure){
		return axios.post("/admin/attendanceAnalysisController/queryAttendanceAnalysisPage",data,success,failure);
	},
	
	// 分页查询考勤异常报告
	queryAttendanceReportPage:function(data,success,failure){
		return axios.post("/admin/attendanceAnalysisController/queryAttendanceReportPage",data,success,failure);
    },
    
    // 上传考勤分析表数据
	uploadAttendanceAnalysis:function(data,success,failure){
		return axios.post("/admin/attendanceAnalysisController/uploadAttendanceAnalysis",data,success,failure);
	},
	
	// 分页查询考勤异常（每日考勤分析）
	queryDailyAttendanceAnalysisPage:function(data,success,failure){
		return axios.post("/admin/dailyAttendanceAnalysisController/queryDailyAttendanceAnalysisPage",data,success,failure);
	},
	
	// 导出考勤异常（每日考勤分析）
	exportDailyAttendanceAnalysis:function(data,success,failure){
		return axios.downloadPost("/admin/dailyAttendanceAnalysisController/exportDailyAttendanceAnalysis",data,success,failure);
	},
}

export default attendanceException