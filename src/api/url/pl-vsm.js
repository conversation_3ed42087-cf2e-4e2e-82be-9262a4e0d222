import axios from "../index.js"

var plVsm = {

  queryPlVsmPage:function(data,success,failure){
		return axios.post("/admin/plVsmController/queryPage",data,success,failure);
    },

  queryPlVsmDetail:function(data,success,failure){
    return axios.post("/admin/plVsmController/queryDetail",data,success,failure);
  },

  addPlVsm:function(data,success,failure){
    return axios.post("/admin/plVsmController/add",data,success,failure);
  },

  updatePlVsm:function(data,success,failure){
    return axios.post("/admin/plVsmController/update",data,success,failure);
  },

  deletePlVsm:function(data,success,failure){
    return axios.post("/admin/plVsmController/delete",data,success,failure);
  },

}

export default plVsm 