import axios from "../index.js"

var shiftManagement = {
    
    // 单个添加班次信息
    addWorkType:function(data,success,failure){
        return axios.post("/admin/workTypeController/addWorkType",data,success,failure);
    },

    // 单个删除信息
    deleteWorkType:function(data,success,failure){
        return axios.post("/admin/workTypeController/deleteWorkType",data,success,failure);
    },

    // 根据Id查询班次信息
    queryWorkTypeDetail:function(data,success,failure){
        return axios.post("/admin/workTypeController/queryWorkTypeDetail",data,success,failure);
    },

    // 分页查询班次信息
    queryWorkTypePage:function(data,success,failure){
        return axios.post("/admin/workTypeController/queryWorkTypePage",data,success,failure);
    },

    // 单个修改信息
    updateWorkType:function(data,success,failure){
        return axios.post("/admin/workTypeController/updateWorkType",data,success,failure);
    },

    // 查询所有班次信息
    queryWorkTypeAll:function(data,success,failure){
        return axios.post("/admin/workTypeController/queryWorkTypeAll",data,success,failure);
    },

    // 查询所有班次信息(H5)
    wxqueryPhoneWorkTypeAll:function(data,success,failure){
        return axios.wxPost("/admin/workTypeController/queryPhoneWorkTypeAll",data,success,failure);
    },
}

export default shiftManagement