import axios from "../index.js"

var employeeManagement = {

    // 单个添加员工
    addEmployee:function(data,success,failure){
        return axios.post("/admin/employeeController/addEmployee",data,success,failure);
    },

    // 删除员工信息
    deleteEmployee:function(data,success,failure){
        return axios.post("/admin/employeeController/deleteEmployee",data,success,failure);
    },

    // 员工信息导出
    exportEmployee:function(data,success,failure){
        return axios.post("/admin/employeeController/exportEmployee",data,success,failure);
    },

    // 员工信息导入
    importEmployee:function(data,success,failure){
        return axios.post("/admin/employeeController/importEmployee",data,success,failure);
    },

    // 导入职工表信息数据
    importEmployeeTool:function(data,success,failure){
        return axios.post("/admin/employeeController/importEmployeeTool",data,success,failure);
    },

    // 查询员工信息详情
    queryEmployeeDetail:function(data,success,failure){
        return axios.post("/admin/employeeController/queryEmployeeDetail",data,success,failure);
    },

    // 分页查询员工信息
    queryEmployeePage:function(data,success,failure){
        return axios.post("/admin/employeeController/queryEmployeePage",data,success,failure);
    },

    // 查询所有考勤机信息
    queryMachineListAll:function(data,success,failure){
        return axios.post("/admin/employeeController/queryMachineListAll",data,success,failure);
    },

    // 查询所有角色信息
    queryRoleListAll:function(data,success,failure){
        return axios.post("/admin/employeeController/queryRoleListAll",data,success,failure);
    },

    // 查询所有班组信息集合
    queryWorkItemList:function(data,success,failure){
        return axios.post("/admin/employeeController/queryWorkItemList",data,success,failure);
    },

    // 修改员工信息
    updateEmployee:function(data,success,failure){
        return axios.post("/admin/employeeController/updateEmployee",data,success,failure);
    },

    // 控制员工是否允许修改图片(只需要传imgStatus字段 1：不允许)
    controlEmployeeImage:function(data,success,failure){
        return axios.post("/admin/employeeController/controlEmployeeImage",data,success,failure);
    },

    // H5根据员工号查询员工信息详情(根据openId查询的接口都是查询员工信息)
    wxqueryPhoneEmployeeDetail:function(data,success,failure){
        return axios.wxPost("/admin/employeeController/queryPhoneEmployeeDetail",data,success,failure);
    },

    // H5根据员工号查询的接口(没传的话默认取当前登录人)
    wxqueryDetailByEmpId:function(data,success,failure){
        return axios.wxPost("/admin/employeeController/queryPhoneEmployeeDetailByEmpId",data,success,failure);
    },

    // H5设置是否代理(只需要传是否代理即可)
    wxupdateEmployeeAgency:function(data,success,failure){
        return axios.wxPost("/admin/employeeController/updateEmployeeAgency",data,success,failure);
    },

    // H5设置员工代理人
    wxupdateEmployeeAssignee:function(data,success,failure){
        return axios.wxPost("/admin/employeeController/updateEmployeeAssignee",data,success,failure);
    },

    // 查询待删除企业微信员工信息
    queryEmployeeToBeDelete: function (data, success, failure) {
      return axios.post("/admin/employeeController/queryEmployeeToBeDeleteThroughWeWorkPage", data, success, failure);
    },
    // 查询已删除企业微信员工信息
    queryEmployeeDeleted: function (data, success, failure) {
      return axios.post("/admin/employeeController/queryEmployeeDeletedThroughWeWorkPage", data, success, failure);
    },
    deleteAllWeWorkEmp: function (data, success, failure) {
      return axios.post("/admin/employeeController/deleteAllWeWorkEmp", data, success, failure);
    }
}

export default employeeManagement
