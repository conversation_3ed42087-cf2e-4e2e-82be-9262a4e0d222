// 配置API接口地址
var root = 'api'

// 引用axios
var axios = require('axios')

// // 把Request PayLoad 转成 Form Data
// var qs = require('qs')

// 自定义判断元素类型JS
function toType (obj) {
  return ({}).toString.call(obj).match(/\s([a-zA-Z]+)/)[1].toLowerCase()
}

// 参数过滤函数
function filterNull (o) {
  for (var key in o) {
    if (o[key] === null) {
      delete o[key]
    }
    if (toType(o[key]) === 'string') {
      o[key] = o[key].trim()
    } else if (toType(o[key]) === 'object') {
      o[key] = filterNull(o[key])
    } else if (toType(o[key]) === 'array') {
      o[key] = filterNull(o[key])
    }
  }
  return o
}
/*
  接口处理函数
  这个函数每个项目都是不一样的，我现在调整的是适用于
  https://cnodejs.org/api/v1 的接口，如果是其他接口
  需要根据接口的参数进行调整。参考说明文档地址：
  https://cnodejs.org/topic/5378720ed6e2d16149fa16bd
  主要是，不同的接口的成功标识和失败提示是不一致的。
  另外，不同的项目的处理方法也是不一致的，这里出错就是简单的alert
*/

function apiAxios (method, url, params, success, failure) {
  if (params) {
    params = filterNull(params)
  }
  axios({
    method: method,
    url: url,
    data: method === 'POST' || method === 'PUT' ? params : null,
    params: method === 'GET' || method === 'DELETE' ? params : null,

    // // 允许请求的数据在传到服务器之前进行转 把Request PayLoad 转成 Form Data
    // transformRequest: [function (params) {
    //   // return qs.stringify(params, {arrayFormat: 'brackets'});
    //   return qs.stringify(params)
    // }],

    // 自定义的要被发送的头信息
    headers:{
      // 'Content-Type':'application/json;charset=UTF-8',
      'Authorization' : localStorage.getItem('Auth-Token')
    },

    baseURL: root,

    //　是否跨域请求
    withCredentials: false
  })
  .then(function (res) {
    if (res.data.success === true) {
      if (success) {
        success(res.data)
      }
    } else {
      if (failure) {
        failure(res.data)
      } else {
        window.alert('error: ' + JSON.stringify(res.data))
      }
    }
  })
  .catch(function (err) {
    let res = err.response
    if (err) {
      window.alert('api error, HTTP CODE: ' + res.status)
    }
  })
}

// 上传图片
function uploadApiAxios (method, url, params, success, failure) {
  if (params) {
    params = filterNull(params)
  }
  axios({
    method: method,
    url: url,
    data: method === 'POST' || method === 'PUT' ? params : null,
    params: method === 'GET' || method === 'DELETE' ? params : null,

    // // 允许请求的数据在传到服务器之前进行转 把Request PayLoad 转成 Form Data
    // transformRequest: [function (params) {
    //     // return qs.stringify(params, {arrayFormat: 'brackets'});
    //     return params
    // }],

    // 自定义的要被发送的头信息
    headers:{
      // 'Content-Type':'multipart/form-data'
      'Authorization' : localStorage.getItem('Auth-Token')
    },

    baseURL: root,

    //　是否跨域请求
    withCredentials: false
  })
  .then(function (res) {
    if(res.data.success === true){
      if(success){
        success(res.data)
      }
    }else{
      if(failure){
        failure(res.data)
      }
    }

  })
  .catch(function (err) {
    
  })
}  

// 文件下载接口
function downloadApiAxios (method, url, params, success, failure) {
  if (params) {
    params = filterNull(params)
  }
  axios({
    method: method,
    url: url,
    data: method === 'POST' || method === 'PUT' ? params : null,
    params: method === 'GET' || method === 'DELETE' ? params : null,
    responseType: 'blob', // 设置响应类型为blob用于文件下载

    // 自定义的要被发送的头信息
    headers:{
      'Authorization' : localStorage.getItem('Auth-Token')
    },

    baseURL: root,

    //　是否跨域请求
    withCredentials: false
  })
  .then(function (res) {
    if (success) {
      success(res) // 直接返回response对象，包含blob数据
    }
  })
  .catch(function (err) {
    if (failure) {
      failure(err)
    } else {
      let res = err.response
      if (err) {
        window.alert('api error, HTTP CODE: ' + res.status)
      }
    }
  })
}

// 微信对接接口
function wxApiAxios (method, url, params, success, failure) {
    if (params) {
        params = filterNull(params)
    }
    axios({
        method: method,
        url: url,
        data: method === 'POST' || method === 'PUT' ? params : null,
        params: method === 'GET' || method === 'DELETE' ? params : null,

        // // 允许请求的数据在传到服务器之前进行转 把Request PayLoad 转成 Form Data
        // transformRequest: [function (params) {
        //   // return qs.stringify(params, {arrayFormat: 'brackets'});
        //   return qs.stringify(params);
        // }],

        // 自定义的要被发送的头信息
        headers:{
            'Content-Type':'application/json;charset=UTF-8',
            'WxCpOpenid' : localStorage.getItem('openId')
        },

        baseURL: root,

        //　是否跨域请求
        withCredentials: false
    })
    .then(function (res) {
        if (res.data.success === true) {
            if (success) {
            success(res.data)
            }
        } else {
            if (failure) {
            failure(res.data)
            } else {
            window.alert('error: ' + JSON.stringify(res.data))
            }
        }
    })
    .catch(function (err) {
        let res = err.response
        if (err) {
            window.alert('api error, HTTP CODE: ' + res.status)
        }
    })
}

// 返回在vue模板中的调用接口
export default {
  get: function (url, params, success, failure) {
    return apiAxios('GET', url, params, success, failure)
  },
  post: function (url, params, success, failure) {
    return apiAxios('POST', url, params, success, failure)
  },
  put: function (url, params, success, failure) {
    return apiAxios('PUT', url, params, success, failure)
  },
  delete: function (url, params, success, failure) {
    return apiAxios('DELETE', url, params, success, failure)
  },
  // 上传图片
  uploadPost: function (url, params, success, failure) {
    return uploadApiAxios('POST', url, params, success, failure)
  },
  // 微信对接接口
  wxGet: function (url, params, success, failure) {
    return wxApiAxios('GET', url, params, success, failure)
  },
  wxPost: function (url, params, success, failure) {
    return wxApiAxios('POST', url, params, success, failure)
  },
  // 文件下载接口
  downloadGet: function (url, params, success, failure) {
    return downloadApiAxios('GET', url, params, success, failure)
  },
  downloadPost: function (url, params, success, failure) {
    return downloadApiAxios('POST', url, params, success, failure)
  }
}