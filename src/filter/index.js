import Vue from 'vue';

// YYYY-MM-DD hh-mm
Vue.filter('timeToYMDHM', function (value) {
    if (value){
        let time = new Date(value)
            ,year = time.getUTCFullYear()
            ,month = time.getUTCMonth()+1
            ,day = time.getUTCDate()
            ,hour = time.getUTCHours()
            ,min = time.getUTCMinutes();
        if(month<10) month = '0'+month;
        if(min<10) min = '0'+min;
        return year+'-'+month+'-'+day+' '+hour+':'+min;
    }
});

// YYYY-MM-DD hh-mm-ss
Vue.filter('timeToYMDHMS', function (value) {
    if (value){
        let time = new Date(value)
            ,year = time.getUTCFullYear()
            ,month = time.getUTCMonth()+1
            ,day = time.getUTCDate()
            ,hour = time.getUTCHours()
            ,min = time.getUTCMinutes()
            ,second = time.getUTCSeconds();
        if(month<10) month = '0'+month;
        if(min<10) min = '0'+min;
        return year+'-'+month+'-'+day+' '+hour+':'+min+':'+second;
    }
});

// YYYY-MM-DD(w) hh-mm-ss
Vue.filter('timeToYMDWHMS', function (value) {
    if (value){
        let week=["星期日","星期一","星期二","星期三","星期四","星期五","星期六"];
        let time = new Date(value)
            ,year = time.getUTCFullYear()
            ,month = time.getUTCMonth()+1
            ,day = time.getUTCDate()
            ,weekday = week[time.getDay()]
            ,hour = time.getUTCHours()
            ,min = time.getUTCMinutes()
            ,second = time.getUTCSeconds();
        if(month<10) month = '0'+month;
        if(min<10) min = '0'+min;
        return year+'-'+month+'-'+day+'（'+weekday+'） '+hour+':'+min+':'+second;
    }
});

// YYYY-MM-DD
Vue.filter('timeToYMD', function (value) {
    if (value){
        const time = new Date(value);
        const year = time.getUTCFullYear();
        let month = time.getUTCMonth()+1;
        if(month<10) month = '0'+month;
        const day = time.getUTCDate();
        return year+'-'+month+'-'+day;
    }
});

// hh-mm
Vue.filter('timeToHM', function (value) {
    if (value){
        const time = new Date(value);
        const hour = time.getUTCHours();
        let min = time.getUTCMinutes();
        if(min<10) min = '0'+min;
        return hour+':'+min;
    }
});

// hh-mm-ss
Vue.filter('timeToHMS', function (value) {
    if (value){
        let time = new Date(value);
        let hour = time.getUTCHours();
        let min = time.getUTCMinutes();
        let seconds = time.getUTCSeconds();
        if(hour<10) hour = '0'+hour;
        if(min<10) min = '0'+min;
        if(seconds<10) seconds = '0'+seconds;
        return hour+':'+min+':'+seconds;
    }
});
// 时间转换 Y年m月d日
Vue.filter('toYmdStr', function (value){
    let dateTime = new Date(value);
    let Year = dateTime.getFullYear();
    let Month = (dateTime.getMonth()+1);
    Month = Month < 10 ? ('0' + Month) : Month;
    let Day = dateTime.getDate();
    Day = Day < 10 ? ('0' + Day) : Day;
    return Year + '年' + Month + '月' + Day + '日';
});

Vue.filter('splitToYmdHms', function (value){
    if (value != undefined && value != null) {
        let ymd = value.split(' ')[0];
        let hms = value.split(' ')[1].split('.')[0];
        return ymd + ' ' + hms;
    }
});

// 时间戳转时间
Vue.filter('formatDate',function (value) {
    // var val = JSON.parse(value)
    var date = new Date(value*1000);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
    var Y = date.getFullYear();
    var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1);
    var D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
    var h = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
    var m = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
    var s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();

    return Y + '-' + M + '-' + D + ' ' + h + ':' + m + ':' + s;
});

// 特殊时间格式2019-01-03T00:18:21.000+0000转化成正常格式
Vue.filter('formatNormal',function (value) {
    var dateNormal = new Date(value).toJSON();
    return new Date(+new Date(dateNormal) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')    
});
// 特殊时间格式2019-01-03T00:18:21.000+0000转化成正常格式
Vue.filter('dateNormal',function (value) {
    let d = new Date(value);
    let month = (d.getMonth() + 1) < 10 ? '0'+(d.getMonth() + 1) : (d.getMonth() + 1);
    let day = d.getDate()<10 ? '0'+d.getDate() : d.getDate();
    let hours = d.getHours()<10 ? '0'+d.getHours() : d.getHours();
    let min = d.getMinutes()<10 ? '0'+d.getMinutes() : d.getMinutes();
    let sec = d.getSeconds()<10 ? '0'+d.getSeconds() : d.getSeconds();
    let times= month + '-' + day + ' ' + hours + ':' + min;
    return times
});

/**
 * 自定义指令 v-reset-page，以解决 iOS 12 中键盘收起后页面底部有留白的问题
 */

Vue.directive('resetPage', {
    inserted: function (el) {
      // 监听键盘收起事件
      document.body.addEventListener('focusout', () => {
        if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
          //软键盘收起的事件处理
          setTimeout(() => {
            const scrollHeight = document.documentElement.scrollTop || document.body.scrollTop || 0
            window.scrollTo(0, Math.max(scrollHeight - 1, 0))
          }, 100)
        }
      })
    }
  })


