import Vue from 'vue'
import Router from 'vue-router'
import{Loading,Message} from'element-ui'
/**
 * 重写路由的push方法
 */
const routerPush = Router.prototype.push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch(error=> error)
}
// 模板
const Index = () => import('@/page/index')
// 登录
const Login = () => import('@/page/login')
// 员工管理
const EmployeeManagement = () => import('@/page/employee-management')
// 员工管理
const EmployeeSetting = () => import('@/page/employee-setting')
// // 删除企业微信
const EmployeeDelete = () => import('@/page/employee-delete')
// 考勤机管理
const AttendanceMachine = () => import('@/page/attendance-machine')
// 假期余额管理
const HolidayBalance = () => import('@/page/holiday-balance')
// 考勤设置
const AttendanceSetting = () => import('@/page/attendance-setting')
// 工作日历管理
const CalendarManagement = () => import('@/page/calendar-management')
// 班组管理
const TeamManagement = () => import('@/page/team-management')
// 班次管理
const ShiftManagement = () => import('@/page/shift-management')
 // 假别管理
const CounterfeitManagement = () => import('@/page/counterfeit-management')
// 排班管理
const WorkforceManagement = () => import('@/page/workforce-management')
// 线路管理
const BusManagement = () => import('@/page/bus-management')
// 编辑线路
const EditRoadline = () => import('@/page/edit-roadline')
// 加班类别管理
const OvertimeManagement = () => import('@/page/overtime-management')
// 数据查询
const DataQuery = () => import('@/page/data-query')
// 出勤异常查询
const AttendanceException = () => import('@/page/attendance-exception')
// 出勤异常查询（报告）
const AttendanceExceptionQuery = () => import('@/page/attendance-exception-query')
// 出勤记录查询
const AttendanceRecord = () => import('@/page/attendance-record')
// 加班记录查询
const OvertimeRecord = () => import('@/page/overtime-record')
// 请假记录查询
const LeaveRecord = () => import('@/page/leave-record')
// 报告数据查询
const ReportData = () => import('@/page/report-data')
// 考勤异常报告
const AttendanceReport = () => import('@/page/attendance-report')
// 直接人力工时报告
const DirectManpower = () => import('@/page/direct-manpower')
// 每月人数报告
const MonthlyPopulation = () => import('@/page/monthly-population')
// 月加班汇总报告
const MonthlyOvertime = () => import('@/page/monthly-overtime')
// 员工班车统计
const StaffShuttle = () => import('@/page/staff-shuttle')
//  不计工时员工设置
const SettingEmployees = () => import('@/page/setting-employees')
// 生成数据
const GenerateData = () => import('@/page/generate-data')
// 排班规则
const SchedulingRules = () => import('@/page/scheduling-rules')
// 辅助基础信息
const AuxiliaryInformation = () => import('@/page/auxiliary-information')
// 邮件接收人
const ReportEmail = () => import('@/page/report-email')
// 产线和VSM对照表
const PlVsm = () => import('@/page/pl-vsm')
// 车间
const Workshop = () => import('@/page/workshop')
// 职位
const Position = () => import('@/page/position')
// 部门
const Department = () => import('@/page/department')
// 财务月
const Financial = () => import('@/page/financial')
// 通知邮箱管理
const NotificationmailboxManagement = () => import('@/page/notificationmailbox-management')
// 批量修改班次
const BatchModification = () => import('@/page/batch-modification')
// 补卡记录查询
const ReplacecardRecord = () => import('@/page/replacecard-record')
// 审批人员维护
const Approval = () => import('@/page/approval')
// 权限管理
const PermissionManagement = () => import('@/page/permission-management')
// 授权
const Mandate = () => import('@/page/mandate')
// 人员管理
const PersonnelManagement = () => import('@/page/personnel-management')
// 申请管理
const ApplicationManagement = () => import('@/page/application-management')
// 撤销申请
const WithdrawalApplication = () => import('@/page/withdrawal-application')
// CEC签核权限管理
const CecManagement = () => import('@/page/cec-management')
// 考勤汇总
const Attendanceummary = () => import('@/page/attendance-summary')
// 考勤汇总 员工状态
const employeeAttendance = () => import('@/page/employee-attendance')

// 微信端
// 模板
const WechatModal = () => import('@/page/weChat/wechat-modal')
// 微信绑定
const Binding = () => import('@/page/weChat/binding')
// 个人信息
const PersonalInformation = () => import('@/page/weChat/personal-information')
// 考勤记录
const TimeTag = () => import('@/page/weChat/time-tag')
// 考勤统计
const AttendanceStatistics = () => import('@/page/weChat/attendance-statistics')
// 我的工作台
const MyWorkbench = () => import('@/page/weChat/my-workbench')
// 员工休息提醒
const RestReminder = () => import('@/page/weChat/rest-reminder')
// 加班审批
const OvertimeApproval = () => import('@/page/weChat/overtime-approval')
// 请假审批
const LeaveApproval = () => import('@/page/weChat/leave-approval')
// 借调申请
const SecondedApplication = () => import('@/page/weChat/seconded-application')
// 补卡申请
const SupplementarycardApplication = () => import('@/page/weChat/supplementarycard-application')
// 查看排班
const SchedulingCheck = () => import('@/page/weChat/scheduling-check')
// 按人员排班
const Scheduling = () => import('@/page/weChat/scheduling')
// 按人员排班 选择日期
const SchedulingDateshow = () => import('@/page/weChat/scheduling-dateshow')
// 按日期排班
const SchedulingDate = () => import('@/page/weChat/scheduling-date')
// 按日期排班 选择人员
const SchedulingPersonnel = () => import('@/page/weChat/scheduling-personnel')
// 人脸管理
const FaceManagement = () => import('@/page/weChat/face-management')
// 选择加班员工
const OvertimeEmployees = () => import('@/page/weChat/overtime-employees')
// 员工 加班确认
const OvertimeConfirmation = () => import('@/page/weChat/overtime-confirmation')
// 加班申请
const OvertimeApplication = () => import('@/page/weChat/overtime-application')
// 加班状态
const OvertimeStatus = () => import('@/page/weChat/overtime-status')
// 加班批量审批
const ApprovalBatch = () => import('@/page/weChat/approval-batch')
// 审批加班申请
const ApproverApproval = () => import('@/page/weChat/approver-approval')
// 请假详情
const LeaveDetails = () => import('@/page/weChat/leave-details')
// 补卡详情
const CardDetails = () => import('@/page/weChat/card-details')
// 审批人 审批加班申请 确认
const ApproverConfirmation = () => import('@/page/weChat/approver-confirmation')
// 领班：查看员工确认情况/ 提交加班申请
const CheckConfirmation = () => import('@/page/weChat/check-confirmation')
// 被借调领班 确认
const SecondedConfirmation = () => import('@/page/weChat/seconded-confirmation')
// 借调 已确认
const SecondedConfirmed = () => import('@/page/weChat/seconded-confirmed')
// 加班确认状态
const OvertimestatusConfirmation = () => import('@/page/weChat/overtimestatus-confirmation')

Vue.use(Router)

// export default new Router({
const router =  new Router({
  routes: [
    {
      path: '/',
      name: 'login',
      meta:{
        title:'登录'
      },
      component: Login
    },
    {
      path: '/index',
      name: 'index',
      meta:{
        title:'模板'
      },
      component: Index,
      children:[
        {
          path: '/employee-setting',
          name: 'employee-setting',
          meta: {
            title: '员工管理'
          },
          redirect:'/employee-management',
          component: EmployeeSetting,
          children: [
            {
              path: '/employee-management',
              name: 'employee-management',
              meta: {
                title: '员工管理'
              },
              component: EmployeeManagement,

            },
            {
              path: '/employee-delete',
              name: 'employee-delete',
              meta: {
                title: '删除企业微信'
              },
              component: EmployeeDelete,

            },
          ]
        },
        {
          path:'/attendance-machine',
          name:'attendance-machine',
          meta:{
            title:'考勤机管理'
          },
          component:AttendanceMachine,
        },
        {
          path:'/attendance-setting',
          name:'attendance-setting',
          meta:{
            title:'考勤设置'
          },
          component:AttendanceSetting,
          children:[
            {
              path:'/calendar-management',
              name:'calendar-management',
              meta:{
                title:'工作日历管理'
              },
              component:CalendarManagement
            },
            {
              path:'/team-management',
              name:'team-management',
              meta:{
                title:'班组管理'
              },
              component:TeamManagement
            },
            {
              path:'/shift-management',
              name:'shift-management',
              meta:{
                title:'班次管理'
              },
              component:ShiftManagement
            },
            {
              path:'/counterfeit-management',
              name:'counterfeit-management',
              meta:{
                title:'假别管理'
              },
              component:CounterfeitManagement
            },
            {
              path: '/holiday-balance-management',
              name: 'holiday-balance-management',
              meta: {
                title: '假期余额管理'
              },
              component: HolidayBalance
            },
            {
              path:'/workforce-management',
              name:'workforce-management',
              meta:{
                title:'排班管理'
              },
              component:WorkforceManagement
            },
            {
              path:'/scheduling-rules',
              name:'scheduling-rules',
              meta:{
                title:'排班规则'
              },
              component:SchedulingRules
            },
            {
              path:'/bus-management',
              name:'bus-management',
              meta:{
                title:'线路管理'
              },
              component:BusManagement
            },
            {
              path:'/edit-roadline',
              name:'edit-roadline',
              meta:{
                title:'编辑线路'
              },
              component:EditRoadline
            },
            {
              path:'/overtime-management',
              name:'overtime-management',
              meta:{
                title:'加班类别管理'
              },
              component:OvertimeManagement
            },
            {
              path:'/auxiliary-information',
              name:'auxiliary-information',
              meta:{
                title:'辅助基础信息'
              },
              component:AuxiliaryInformation
            },
            {
              path:'/notificationmailbox-management',
              name:'notificationmailbox-management',
              meta:{
                title:'通知邮箱管理'
              },
              component:NotificationmailboxManagement
            },
            {
              path:'/cec-management',
              name:'cec-management',
              meta:{
                title:'CEC签核权限管理'
              },
              component:CecManagement
            },
            {
              path:'/batch-modification',
              name:'batch-modification',
              meta:{
                title:'批量修改班次'
              },
              component:BatchModification
            },
            {
              path:'/workshop',
              name:'workshop',
              meta:{
                title:'车间'
              },
              component:Workshop
            },
            {
              path:'/position',
              name:'position',
              meta:{
                title:'职位'
              },
              component:Position
            },
            {
              path:'/department',
              name:'department',
              meta:{
                title:'部门'
              },
              component:Department
            },
            {
              path:'/financial',
              name:'financial',
              meta:{
                title:'财务月'
              },
              component:Financial
            },
            {
              path:'/approval',
              name:'approval',
              meta:{
                title:'审批人员维护'
              },
              component:Approval
            },
            {
              path:'/report-email',
              name:'report-email',
              meta:{
                title:'邮件接收人'
              },
              component:ReportEmail
            },
            {
              path:'/pl-vsm',
              name:'pl-vsm',
              meta:{
                title:'产线和VSM对照表'
              },
              component:PlVsm
            },
            {
              path: '/attendance-summary',
              name: 'attendance-summary',
              meta: {
                title: '考勤汇总'
              },
              component: Attendanceummary,
              children: [
                {
                  path: '/employee-attendance',
                  name: 'employee-attendance',
                  meta: {
                    title: '确认人员'
                  },
                  component: employeeAttendance
                },]
            },
          ]
        },
        {
          path:'/data-query',
          name:'data-query',
          meta:{
            title:'数据查询'
          },
          component:DataQuery,
          children:[
            {
              path:'/attendance-exception',
              name:'attendance-exception',
              meta:{
                title:'出勤异常查询'
              },
              component:AttendanceException
            },
            {
              path:'/attendance-record',
              name:'attendance-record',
              meta:{
                title:'出勤记录查询'
              },
              component:AttendanceRecord
            },
            {
              path:'/overtime-record',
              name:'overtime-record',
              meta:{
                title:'加班记录查询'
              },
              component:OvertimeRecord
            },
            {
              path:'/leave-record',
              name:'leave-record',
              meta:{
                title:'请假记录查询'
              },
              component:LeaveRecord
            },
            {
              path:'/replacecard-record',
              name:'replacecard-record',
              meta:{
                title:'补卡记录查询'
              },
              component:ReplacecardRecord
            },
          ]
        },
        {
          path:'/report-data',
          name:'report-data',
          meta:{
            title:'报告数据查询'
          },
          component:ReportData,
          children:[
            {
              path:'/attendance-report',
              name:'attendance-report',
              meta:{
                title:'考勤异常报告'
              },
              component:AttendanceReport
            },
            {
              path:'/direct-manpower',
              name:'direct-manpower',
              meta:{
                title:'直接人力工时报告'
              },
              component:DirectManpower
            },
            {
              path:'/generate-data',
              name:'generate-data',
              meta:{
                title:'生成数据'
              },
              component:GenerateData
            },
            {
              path:'/monthly-population',
              name:'monthly-population',
              meta:{
                title:'每月人数报告'
              },
              component:MonthlyPopulation
            },
            {
              path:'/monthly-overtime',
              name:'monthly-overtime',
              meta:{
                title:'月加班汇总报告'
              },
              component:MonthlyOvertime
            },
            {
              path:'/staff-shuttle',
              name:'staff-shuttle',
              meta:{
                title:'员工班车统计'
              },
              component:StaffShuttle
            },
            {
              path:'/setting-employees',
              name:'setting-employees',
              meta:{
                title:'不计工时员工设置'
              },
              component:SettingEmployees
            },
            {
              path:'/attendance-exception-query',
              name:'attendance-exception-query',
              meta:{
                title:'每日考勤异常报告'
              },
              component:AttendanceExceptionQuery
            },
          ]
        },
        {
          path:'/permission-management',
          name:'permission-management',
          meta:{
            title:'权限管理'
          },
          component:PermissionManagement,
        },
        {
          path:'/mandate',
          name:'mandate',
          meta:{
            title:'授权'
          },
          component:Mandate,
        },
        {
          path:'/personnel-management',
          name:'personnel-management',
          meta:{
            title:'人员管理'
          },
          component:PersonnelManagement,
        },
        {
          path:'/application-management',
          name:'application-management',
          meta:{
            title:'申请管理'
          },
          component:ApplicationManagement,
          children:[
            {
              path:'/withdrawal-application',
              name:'withdrawal-application',
              meta:{
                title:'撤销申请'
              },
              component:WithdrawalApplication
            }
          ]
        }
      ]
    },


    // 微信端
    {
      path: '/wechat-modal',
      name: 'wechat-modal',
      meta:{
        title:'微信模板'
      },
      component: WechatModal,
      children:[
        {
          path:'/binding',
          name:'binding',
          meta:{
            title:'微信绑定'
          },
          component:Binding,
        },
        {
          path:'/personal-information',
          name:'personal-information',
          meta:{
            title:'个人信息'
          },
          component:PersonalInformation,
        },
        {
          path:'/time-tag',
          name:'time-tag',
          meta:{
            title:'考勤记录'
          },
          component:TimeTag,
        },
        {
          path:'/attendance-statistics',
          name:'attendance-statistics',
          meta:{
            title:'考勤统计'
          },
          component:AttendanceStatistics,
        },
        {
          path:'/my-workbench',
          name:'my-workbench',
          meta:{
            title:'我的工作台'
          },
          component:MyWorkbench,
        },
        {
          path:'/rest-reminder',
          name:'rest-reminder',
          meta:{
            title:'员工休息提醒'
          },
          component:RestReminder,
        },
        {
          path:'/overtime-approval',
          name:'overtime-approval',
          meta:{
            title:'加班审批'
          },
          component:OvertimeApproval,
        },
        {
          path:'/approval-batch',
          name:'approval-batch',
          meta:{
            title:'批量审批'
          },
          component:ApprovalBatch,
        },
        {
          path:'/leave-approval',
          name:'leave-approval',
          meta:{
            title:'请假审批'
          },
          component:LeaveApproval,
        },
        {
          path:'/seconded-application',
          name:'seconded-application',
          meta:{
            title:'借调申请'
          },
          component:SecondedApplication,
        },
        {
          path:'/supplementarycard-application',
          name:'supplementarycard-application',
          meta:{
            title:'补卡申请'
          },
          component:SupplementarycardApplication,
        },
        {
          path:'/scheduling',
          name:'scheduling',
          meta:{
            title:'排班'
          },
          component:Scheduling,
        },
        {
          path:'/face-management',
          name:'face-management',
          meta:{
            title:'人脸管理'
          },
          component:FaceManagement,
        },
        {
          path:'/overtime-employees',
          name:'overtime-employees',
          meta:{
            title:'选择加班员工'
          },
          component:OvertimeEmployees,
        },
        {
          path:'/overtime-confirmation',
          name:'overtime-confirmation',
          meta:{
            title:'加班确认'
          },
          component:OvertimeConfirmation,
        },
        {
          path:'/overtime-application',
          name:'overtime-application',
          meta:{
            title:'加班申请'
          },
          component:OvertimeApplication,
        },
        {
          path:'/overtime-status',
          name:'overtime-status',
          meta:{
            title:'加班确认状态'
          },
          component:OvertimeStatus,
        },
        {
          path:'/overtimestatus-confirmation',
          name:'overtimestatus-confirmation',
          meta:{
            title:'加班确认状态'
          },
          component:OvertimestatusConfirmation,
        },
        {
          path:'/approver-approval',
          name:'approver-approval',
          meta:{
            title:'审批加班申请'
          },
          component:ApproverApproval,
        },
        {
          path:'/leave-details',
          name:'leave-details',
          meta:{
            title:'请假详情'
          },
          component:LeaveDetails,
        },
        {
          path:'/card-details',
          name:'card-details',
          meta:{
            title:'补卡详情'
          },
          component:CardDetails,
        },
        {
          path:'/approver-confirmation',
          name:'approver-confirmation',
          meta:{
            title:'审批人确认加班申请'
          },
          component:ApproverConfirmation,
        },
        {
          path:'/check-confirmation',
          name:'check-confirmation',
          meta:{
            title:'领班确认加班申请'
          },
          component:CheckConfirmation,
        },
        {
          path:'/seconded-confirmation',
          name:'seconded-confirmation',
          meta:{
            title:'领班确认借调申请'
          },
          component:SecondedConfirmation,
        },
        {
          path:'/seconded-confirmed',
          name:'seconded-confirmed',
          meta:{
            title:'已确认借调申请'
          },
          component:SecondedConfirmed,
        },
        {
          path:'/scheduling-dateshow',
          name:'scheduling-dateshow',
          meta:{
            title:'选择日期'
          },
          component:SchedulingDateshow,
        },
        {
          path:'/scheduling-date',
          name:'scheduling-date',
          meta:{
            title:'按日期排班'
          },
          component:SchedulingDate,
        },
        {
          path:'/scheduling-personnel',
          name:'scheduling-personnel',
          meta:{
            title:'选择人员'
          },
          component:SchedulingPersonnel,
        },
        {
          path:'/scheduling-check',
          name:'scheduling-check',
          meta:{
            title:'查看排班'
          },
          component:SchedulingCheck,
        },
      ]
    }
  ]
})

let flag = navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i);
router.beforeEach((to, from, next) => {
  if(!flag){//pc
    let loginSession = JSON.parse(sessionStorage.getItem('userInfo'));
    if(loginSession == '' || loginSession == undefined){
      if(to.name == 'login'){
        next();
      }else{
        Message.error({
          showClose:true,
          message:"请先登录！",
          duration:2000,
          type: 'error',
        });
        next({name:"login"})
      }
    }else{
      next()
    }
  }else{//mobile
    // let wxSession = JSON.parse(sessionStorage.getItem('openidShow'));
    let wxSession = "1";
    if(wxSession == '' || wxSession == undefined){
      if(to.name == 'personal-information'){
        next()
      }
    }else{
      next()
    }
  }
})

export default router
