<template>
	<aside>
		<el-menu class="el-menu-vertical-demo" @open="handleOpen" @close="handleClose"
			:unique-opened="true"
			:default-active="$route.path" 
			router
		>	
			<template v-for="(item,index) in menuList"> 
				<el-submenu v-if="item.children && item.children.length >0" :index="item.path" :key="index" :unique-opened="false">
					<template slot="title">
                        <i :class="item.icon"></i>
						<span>{{item.name}}</span>
					</template>
					<el-menu-item-group>
						<el-menu-item v-for="(child,cindex) in item.children" :index="child.path" :key="cindex">{{child.name}}</el-menu-item>
					</el-menu-item-group>
				</el-submenu>
				<el-menu-item v-else :index="item.path" :key="index"><i :class="item.icon"></i>{{item.name}}</el-menu-item>
			</template>
    	</el-menu>
	</aside>
</template>
<script>
	export default {
		data(){
			return{
				menuList:[]
			}
		},
		methods: {
			handleOpen(key, keyPath) {
			
			},
			handleClose(key, keyPath) {
			
            },
            
            // 是否会是领班
            queryMenuLists(){
                this.$http.queryEmployeeIsLeader(null,(res)=>{
                    if(res.code == 'SUCCESS'){
                        var isLeader = res.data.isLeader;
                        if(isLeader == true){
                            this.menuList = [
                                {
                                    path:'/attendance-setting',
                                    name:'考勤设置',
                                    icon:'el-icon-s-tools',
                                    children:[
                                        {
                                            path:'/workforce-management',
                                            name:'排班管理',
                                        }
                                    ]
                                }
                            ]
                        }else{
                            this.menuList = [
                                {
                                    path:'/employee-management',
                                    name:'员工管理',
                                    icon:'el-icon-s-custom'
                                },
                                {
                                    path:'/attendance-machine',
                                    name:'考勤机管理',
                                    icon:'el-icon-s-cooperation'
                                },
                                {
                                    path:'/attendance-setting',
                                    name:'考勤设置',
                                    icon:'el-icon-s-tools',
                                    children:[
                                        {
                                            path:'/calendar-management',
                                            name:'工作日历管理',
                                        },
                                        {
                                            path:'/team-management',
                                            name:'班组管理',
                                        },
                                        {
                                            path:'/shift-management',
                                            name:'班次管理',
                                        },
                                        {
                                            path:'/counterfeit-management',
                                            name:'假别管理',
                                        },
                                        {
                                            path:'/workforce-management',
                                            name:'排班管理',
                                        },
                                        {
                                            path:'/bus-management',
                                            name:'线路管理',
                                        },
                                        {
                                            path:'/overtime-management',
                                            name:'加班类别管理',
                                        },
                                        {
                                            path:'/auxiliary-information',
                                            name:'辅助基础信息',
                                        },
                                        {
                                            path:'/notificationmailbox-management',
                                            name:'通知邮箱管理',
                                        }
                                    ]
                                },
                                {
                                    path:'/data-query',
                                    name:'数据查询',
                                    icon:'el-icon-s-order',
                                    children:[
                                        {
                                            path:'/attendance-exception',
                                            name:'出勤异常查询',
                                        },
                                        {
                                            path:'/attendance-record',
                                            name:'出勤记录查询',
                                        },
                                        {
                                            path:'/overtime-record',
                                            name:'加班记录查询',
                                        },
                                        {
                                            path:'/leave-record',
                                            name:'请假记录查询',
                                        },
                                        {
                                            path:'/replacecard-record',
                                            name:'补卡记录查询',
                                        }
                                    ]
                                },
                                {
                                    path:'/report-data',
                                    name:'报告数据查询',
                                    icon:'el-icon-s-data',
                                    children:[
                                        {
                                            path:'/attendance-report',
                                            name:'考勤异常报告',
                                        },
                                        {
                                            path:'/direct-manpower',
                                            name:'直接人力工时报告',
                                        },
                                        {
                                            path:'/monthly-population',
                                            name:'每月人数报告',
                                        },
                                        {
                                            path:'/monthly-overtime',
                                            name:'月加班汇总报告',
                                        },
                                        {
                                            path:'/staff-shuttle',
                                            name:'员工班车统计',
                                        },
                                        {
                                            path:'/setting-employees',
                                            name:'不计工时员工设置',
                                        }
                                    ]
                                },
                                {
                                    path:'/application-management',
                                    name:'申请管理',
                                    icon:'el-icon-s-order',
                                    children:[
                                        {
                                            path:'/withdrawal-application',
                                            name:'撤销申请',
                                        }
                                    ]
                                },
                                {
                                    path:'/permission-management',
                                    name:'权限管理',
                                    icon:'el-icon-s-custom'
                                }
                            ]
                        }
                    }else{
                        this.$message.error(res.message);
                    }
                },(errRes)=>{
                    this.$message.error(errRes.message);
                })
            }
        },
        mounted(){
            // this.queryMenuLists();
            this.menuList = JSON.parse(sessionStorage.getItem('menuList'));
        }
	}
</script>

