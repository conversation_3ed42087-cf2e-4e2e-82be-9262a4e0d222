// 登录 
@import './scss/login.scss';
// 模板页
@import './scss/index.scss';
// 员工管理
@import './scss/employee-management.scss';
// 考勤机管理
@import './scss/attendance-machine.scss';
// 工作日历管理
@import './scss/calendar-management.scss';
// 班组管理
@import './scss/team-management.scss';
// 班次管理
@import './scss/shift-management.scss';
// 假别管理
@import './scss/counterfeit-management.scss';
// 排班管理
@import './scss/workforce-management.scss';
// 线路管理
@import './scss/bus-management.scss';
// 编辑线路
@import './scss/edit-roadline.scss';
// 加班类别管理
@import './scss/overtime-management.scss';
// 出勤异常查询
@import './scss/attendance-exception.scss';
// 出勤记录查询
@import './scss/attendance-record.scss';
// 加班记录查询
@import './scss/overtime-record.scss';
// 请假记录查询
@import './scss/leave-record.scss';
// 考勤异常报告
@import './scss/attendance-report.scss';
// 考勤异常查询（报告）
@import './scss/attendance-exception-query.scss';
// 直接人力工时报告
@import './scss/direct-manpower.scss';
// 每月人数报告
@import './scss/monthly-population.scss';
// 月加班汇总报告
@import './scss/monthly-overtime.scss';
// 员工班车统计
@import './scss/staff-shuttle.scss';
// 不计工时员工设置
@import './scss/setting-employees.scss';
// 生成数据
@import './scss/generate-data.scss';
// 排班规则
@import './scss/scheduling-rules.scss';
// 辅助基础信息
@import './scss/auxiliary-information.scss';
// 通知邮箱管理
@import './scss/notificationmailbox-management.scss';
// 权限管理
@import './scss/permission-management.scss';
// 撤销申请
@import './scss/withdrawal-application.scss';




// 微信端
// 公共
@import './scss/weChat/wechat-index.scss';
// 微信绑定
@import './scss/weChat/binding.scss';
// 个人信息
@import './scss/weChat/personal-information.scss';
// 考勤记录
@import './scss/weChat/time-tag.scss';
// 考勤统计
@import './scss/weChat/attendance-statistics.scss';
// 我的工作台
@import './scss/weChat/my-workbench.scss';
// 员工休息提醒
@import './scss/weChat/rest-reminder.scss';
// 加班审批
@import './scss/weChat/overtime-approval.scss';
// 请假审批
@import './scss/weChat/leave-approval.scss';
// 借调申请
@import './scss/weChat/seconded-application.scss';
// 补卡申请
@import './scss/weChat/supplementarycard-application.scss';
// 排班
@import './scss/weChat/scheduling.scss';
// 人脸管理
@import './scss/weChat/face-management.scss';
// 选择加班员工 
@import './scss/weChat/overtime-employees.scss';
// 加班确认 
@import './scss/weChat/overtime-confirmation.scss';
// 加班申请
@import './scss/weChat/overtime-application.scss';
// 加班状态
@import './scss/weChat/overtime-status.scss';
// 审批加班申请
@import './scss/weChat/approver-approval.scss';
// 请假详情
@import './scss/weChat/leave-details.scss';
