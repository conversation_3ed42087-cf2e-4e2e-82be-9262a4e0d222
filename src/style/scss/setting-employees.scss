.settingBox{
    width: 100%;
    padding: 20px;
    box-sizing: border-box;
    background-color: #fff;
    .employeeTable{
        padding: 10px;
        .el-table{
            .el-table__header-wrapper{
                .el-table__header{
                    table-layout: inherit;
                    th{
                        text-align: center;
                        border-top: 1px solid #EBEEF5;
                        border-right: 1px solid #EBEEF5;
                        &:first-child{
                            width: 56px !important;
                            border-left: 1px solid #EBEEF5;
                        }
                    }
                }
                
            }
            .el-table__body-wrapper{
                .el-table__body{
                    table-layout: inherit;
                    td{
                        text-align: center;
                        border-right: 1px solid #EBEEF5;
                        &:first-child{
                            width: 56px !important;
                            border-left: 1px solid #EBEEF5;
                        }
                    }

                }
     
                
            }
        }
    }
    .selectNotice{
        margin-top: 30px;
    }
    .tableContent{
        margin: 20px 0;
    }
}