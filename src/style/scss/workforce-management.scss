.workforceBox{
    width: 100%;
    padding: 20px;
    box-sizing: border-box;
    background-color: #fff;
    .tableContant{
        table{
            th{
                text-align: center!important;
            }
            td{
                text-align: center!important;
                padding: 0!important;
                div{
                    padding: 0!important;
                    span{
                        display: inline-block;
                        width: 100%;
                        height: 100%;
                        padding: 12px 0;
                        cursor: pointer;
                        &.tdActive{
                            background-color: #ffff00;
                        }
                    }
                }
            }
        }
    }
    .tableFixed{
        position: relative;
        overflow-x: scroll;
        margin: 10px;
        width: 100%;
        table{
            width: 100%;
            table-layout: fixed;
            box-sizing: border-box;
            thead{
                width: 100%;
                tr{
                    width: 100%;
                    display: flex;
                    flex-wrap: nowrap;
                    th{
                        // flex: 1;
                        width: 90px;
                        line-height: 47px;
                        font-size: 14px;
                        color: #909399;
                        text-align: center;
                        border: 0.5px solid #EBEEF5;
                        box-sizing: border-box;
                    }
                }
            }
            tbody{
                width: 100%;
                tr{
                    width: 100%;
                    display: flex;
                    flex-wrap: nowrap;
                    &:hover>td{
                        background-color: #F5F7FA !important;
                        }
                    td{
                        // flex: 1;
                        width: 90px;
                        line-height: 47px;
                        font-size: 14px;
                        color: #606266;
                        text-align: center;
                        border: 0.5px solid #EBEEF5;
                        box-sizing: border-box;
                        &.tdActive{
                            background-color: #ffff00;
                        }
                    }
                }
            }
        }
        .tb1 {
            position: fixed;
            z-index: 1001;
            background-color: #fff;
            width: 360px!important;
            box-sizing: border-box;
            th{
                width: 120px;
                white-space:nowrap;
                overflow:hidden;
                text-overflow:ellipsis;
            }
        }
        .tb2 {
            position: sticky;
            top: 0px;
            margin-left: 360px;
            z-index: 1000;
            box-sizing: border-box;
            display: flex;
            flex-wrap: nowrap;
            width: 2790px!important;
            // tr{
            //     display: flex;
            //     flex-wrap: nowrap;
            //     th{
            //         flex: 1;
            //     }
            // }
        }
        .tb3 {
            position: sticky;
            left: 0px;
            z-index: 1000;
            float: left;
            background-color: #fff;
            width: 360px!important;
            box-sizing: border-box;
            td{
                width: 120px;
                white-space:nowrap;
                overflow:hidden;
                text-overflow:ellipsis;
            }
        }
        .tb4 {
            position: absolute;
            left: 360px;
            box-sizing: border-box;
            display: flex;
            flex-wrap: nowrap;
            width: 2790px!important;
            // tr{
            //     display: flex;
            //     flex-wrap: nowrap;
            //     td{
            //         flex: 1;
            //     }
            // }
        }
        .tb1_1 {
            position: fixed;
            z-index: 1001;
            background-color: #fff;
            width: 600px!important;
            box-sizing: border-box;
            th{
                width: 120px;
                white-space:nowrap;
                overflow:hidden;
                text-overflow:ellipsis;
            }
        }
        .tb2_1 {
            position: sticky;
            top: 0px;
            margin-left: 600px;
            z-index: 1000;
            box-sizing: border-box;
            display: flex;
            flex-wrap: nowrap;
            width: 2790px!important;
        }
        .tb3_1 {
            position: sticky;
            left: 0px;
            z-index: 1000;
            float: left;
            background-color: #fff;
            width: 600px!important;
            box-sizing: border-box;
            td{
                width: 120px;
                white-space:nowrap;
                overflow:hidden;
                text-overflow:ellipsis;
            }
        }
        .tb4_1 {
            position: absolute;
            left: 600px;
            box-sizing: border-box;
            display: flex;
            flex-wrap: nowrap;
            width: 2790px!important;
        }
    }
    // // 滚动条的宽度
    // .el-table__body-wrapper::-webkit-scrollbar {
    //     height: 20px; // 纵向滚动条 必写
    // }
    // // 滚动条的滑块
    // .el-table__body-wrapper::-webkit-scrollbar-thumb {
    //     background-color:#c4c2c2;
    // }
    // // span{
    // //     display: inline-block;
    // //     width: 100%;
    // //     height: 100%;
    // //     cursor: pointer;
    // // }
}