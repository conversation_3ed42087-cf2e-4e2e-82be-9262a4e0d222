.attendanceExceptionQueryBox{
    width: 100%;
    padding: 20px;
    box-sizing: border-box;
    background-color: #fff;
    
    // 为所有区域设置统一的右侧间距
    > .searchFrom,
    > .tableInfo,
    > .tableContent,
    > .paginationBox {
        margin-right: 20px; // 减少右侧间距到20px，与左侧padding保持一致
    }
    
    .searchFrom {
        margin-bottom: 20px;
        padding: 20px;
        background: #f5f5f5;
        border-radius: 4px;
        margin-right: 0;
        
        .demo-form-inline {
            display: flex;
            flex-wrap: wrap;
            align-items: flex-end;
            justify-content: space-between; // 改为space-between布局
            
            .el-form-item {
                margin-right: 20px;
                margin-bottom: 10px;
                flex-shrink: 0;
                
                &:last-child {
                    margin-right: 0; // 最后一个按钮组不需要右边距
                    margin-left: 0; // 移除auto margin
                }
                
                // 确保输入框有合适的宽度
                .el-input {
                    width: 180px;
                }
                
                .el-date-editor {
                    width: 180px;
                }
            }
            
            // 创建一个左侧表单项容器
            .form-left {
                display: flex;
                flex-wrap: wrap;
                align-items: flex-end;
                flex: 1;
                
                .el-form-item {
                    margin-right: 20px;
                }
            }
            
            // 右侧按钮组
            .form-right {
                display: flex;
                align-items: flex-end;
                margin-left: auto;
                padding-right: 0;
                
                .el-form-item {
                    margin-right: 0;
                    margin-left: 0;
                    margin-bottom: 10px;
                    
                    .el-button {
                        margin-left: 10px;
                        
                        &:first-child {
                            margin-left: 0;
                        }
                    }
                }
            }
        }
    }

    .tableInfo {
        margin-bottom: 10px;
        margin-right: 20px; // 与搜索表单保持相同的右侧间距
        
        .scrollTip {
            font-size: 12px;
            color: #909399;
            background: #f4f4f5;
            padding: 6px 12px;
            border-radius: 4px;
            border-left: 3px solid #409eff;
            display: inline-block;
        }
    }

    .tableContent {
        margin-bottom: 20px;
        overflow-x: auto;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-right: 20px; // 与搜索表单保持相同的右侧间距
        
        // 滚动条样式
        &::-webkit-scrollbar {
            height: 8px;
        }
        
        &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        
        &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }
        
        &::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }
        
        table {
            width: 100%;
            min-width: 1200px; // 设置最小宽度确保表格不会过度压缩
            border-collapse: collapse;
        }

        th,
        td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
            white-space: nowrap; // 防止文字换行
            min-width: 80px; // 设置最小列宽
        }
        
        // 为特定列设置合适的宽度
        th:nth-child(1), td:nth-child(1) { // 员工姓名
            min-width: 100px;
        }
        
        th:nth-child(2), td:nth-child(2) { // 员工工号
            min-width: 120px;
        }
        
        th:nth-child(3), td:nth-child(3) { // 日期
            min-width: 100px;
        }
        
        th:nth-child(5), td:nth-child(5), // 开始时间
        th:nth-child(6), td:nth-child(6) { // 结束时间
            min-width: 140px;
        }
        
        th:nth-child(11), td:nth-child(11), // 产线ID
        th:nth-child(12), td:nth-child(12) { // 班组ID
            min-width: 120px;
        }

        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        tbody tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        tbody tr:hover {
            background-color: #f0f0f0;
        }
    }

    .paginationBox {
        display: flex;
        justify-content: center;
        margin-top: 20px;
        margin-right: 20px; // 与其他区域保持相同的右侧间距
    }
}