.personalBox{
    padding: 20px 10px 80px 10px;
    box-sizing: border-box;
    color: #333;
    .personalShow{
        .personalInfo{
            .personalDes{
                display: flex;
                font-size: 14px;
                .personalDesLeft{
                    flex: 1;
                    .personalList{
                        display: flex;
                        margin-bottom: 20px;
                        .personalLeft{
                            min-width: 42px;
                        }
                    }
                }
                .personImg{
                    width: 40%;
                    text-align: center;
                    img{
                        width: 75%;
                        vertical-align: middle;
                    }
                    .imgNotice{
                        margin-top: 10px;
                        font-size: 12px;
                        color: #666;
                    }
                }
            }
            .personalList{
                display: flex;
                margin-bottom: 20px;
            }
        }
        .route{
            font-size: 14px;
            .lineShow{
                display: flex;
                align-items: center;
                margin-bottom: 20px;
                .routeLeft{
                    display: flex;
                    align-items: center;
                    flex: 1;
                    // color: #0000FF;
                    div:first-child{
                        min-width: 84px;
                    }
                    .el-form{
                        width: 100%;
                        .el-form-item {
                            display: flex;
                            margin-bottom: 0;
                            .el-form-item__label{
                                padding-right: 0;
                                min-width: 84px;
                                color: #333;
                            }
                            .el-form-item__content{
                                flex: 1;
                                .el-input{
                                    .el-input__suffix{
                                        color: #33CC00;
                                    }
                                }
                            }
                        }
                    }
                }
                button{
                    padding: 5px 10px;
                    width: 100px;
                }
            }
        }

    }
    .bottomBtnBox{
        .notice{
            text-align: center;
            margin-bottom: 15px;
            font-size: 13px;
        }
    }
    .el-dialog{
        width: 90%;
        .el-dialog__header{
            text-align: center;
            .el-dialog__title{
                font-size: 16px;
            }
        }
        .el-button--primary{
            // background-color: #6A7BCD;
            // border: 1px solid #6A7BCD;
        }
    }
    .dialogNotice{
        .noticeShow{
            div{
                font-size: 14px;
                line-height: 25px;
            }
        }
    }
}