body{
    background-color: #fff;
}
textarea{
    resize: none !important;
}
.modalBox{

    .content{
        .returnLine{
            position: relative;
            display: flex;
            box-sizing: border-box;
            align-items: center;
            .headerTitle{
                padding: 15px 0 10px 0;
                flex: 1;
                text-align: center;
            }
            .return{
                position: absolute;
                left: 0;
                z-index: 50;
                padding: 10px;
                display: inline-block;
                img{
                    width: 20px;
                    height: 20px;
                    vertical-align: middle;
                };
                .svg-inline--fa {
                    color: #118EE9;
                    height:30px;
                    width: 20px;
                }
            }
            .returnNone{
               display: none !important;
            }
        }
       
        // nav
        .navList{
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            box-sizing: border-box;
            background-color: #F2F2F2;
            &>div{
                font-size: 14px;
                color: #169BD7;   
            }
            .ashContent{
                flex: 1;
                color: #807e7e;
                div{
                    margin-bottom: 5px;
                }
                div:last-child{
                    margin-bottom: 0;
                }
            }
        }
        .navListEnd{
            display: flex;
            justify-content: flex-end;
            padding: 10px;
            box-sizing: border-box;
            background-color: #F2F2F2;
            &>div{
                font-size: 14px;
                color: #169BD7;   
            }
            div:last-child{
                margin-left: 10px;
            }  
        }
        .navListBetween{
            display: flex;
            justify-content: space-between;
            padding: 10px;
            box-sizing: border-box;
            background-color: #F2F2F2;
            &>div{
                font-size: 14px;
                color: #807e7e;   
            }
        }
        // nav end

        // bottomBtn
        .bottomBtnBox{
            position: fixed;
            left: 0;
            right: 0;
            bottom: 0;
            margin-bottom: 0;
            padding: 10px !important;
            box-sizing: border-box;
            z-index: 100;
            .el-form-item__content{
                button{
                    width: 100%;
                    margin-left: 0;
                    margin-bottom: 10px;
                }
                .el-button--primary{
                    background-color: #118EE9;
                }
            }
            .el-button--primary{
                background-color: #118EE9;
            }
            .is-disabled{
                background-color: #a0cfff !important;
                border-color: #a0cfff !important;
            }
            button{
                width: 100%;
                margin-left: 0;
                margin-bottom: 10px;
                font-size: 16px;
                letter-spacing: 5px;
            }
            button:last-child{
                margin-bottom: 0;
            }
        }
        // bottomBtn end

        .el-dialog{
            width: 90%;
        }
        .dialogRemark{
            .el-dialog{
                border-radius: 10px;
                .el-dialog__header{
                    text-align: center;
                }
                .el-dialog__body{
                    .remark{
                        padding: 0 20px;
                        box-sizing: border-box;
                    }
                    padding: 0;
                    .footBtn{   
                        margin-bottom: 0;    
                        border-top: 1px solid #EFEFF4;         
                        .el-form-item__content{
                            display: flex;
                            justify-content: space-around;   
                            width: 100%;
                           
                            button{
                                flex: 1;
                                margin-left: 0;
                                padding: 15px 0;
                                font-size: 16px;
                            }
                            button:first-child{
                                color: #363636;
                                border-right: 1px solid #EFEFF4;
                            }
                        }
                        .el-form-item__content::before{
                            display: none;
                        }
                        .el-form-item__content::after{
                            display: none;
                        }
                    }
                    .employees-ruleForm{
                        .el-form-item{
                            display: flex;
                            padding: 0 10px;
                            label{
                                min-width: 80px;
                            }
                        }
                    }
                }
            }
        }
        
        .dialogNotice{
            .el-dialog__footer{
                .dialog-footer{
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    button{
                        min-width: 100px;
                    }
                }
                .dialog-footerCenter{
                    display: flex;
                    align-items: center;
                    justify-content: center!important;
                    button{
                        width: 100%;
                    
                    }
                }
            }
        }
        .dialogNoticeSmall{
            background-color: #fff !important;
            .el-dialog{
                width: 80%;
                border-radius: 10px;
                .el-dialog__footer{
                    padding: 0;
                    border-top: 1px solid #eee;
                    button{
                        font-size: 16px;
                    }
                }
                .el-dialog__header{
                    text-align: center;
                }
                .el-dialog__body{
                    padding:20px 10px;
                    box-sizing: border-box;
                    .noticeShow{
                        div{
                            font-size: 14px;
                            line-height: 20px;
                        }
                    }
                }
            } 
        }
        .backcolorBox{
            // position: absolute;
            // top: 42px;
            // bottom: 0;
            width: 100%;
            background-color: #EFEFF4;
        }
        .bottomBox{
            position: absolute;
            top: 42px;
            bottom: 0;
        }
        .flex-between{
            display: flex;
            justify-content: space-between;
        }
    }
    .greenBgMark{
        margin-right: 10px;
        display: inline-block;
        width: 10px;
        height: 10px;
        background-color: green;

    }
    .redBgMark{
        margin-right: 10px;
        display: inline-block;
        width: 10px;
        height: 10px;
        background-color: red;
    }               
    .grayBgMark{
        margin-right: 10px;
        display: inline-block;
        width: 10px;
        height: 10px;
        background-color: #999;
    }            
    .greenMark{
        color: green!important;
    }
    .redMark{
        color: red!important;
    }
    .grayMark{
        color: #999!important;
    }
    .approvalListsNothing{
        padding: 20px 0;
        text-align: center;
        color: #999;
    }
    .loadNothing{
        padding-bottom: 10px;
        text-align: center;
        color: #999;
    }
    .borrowMark{
        color: #409EFF;
    }
    
    // 日历
    .timeFormItem{
        .calendar{
            font-family: "Helvetica-Neue", "Helvetica", Arial, sans-serif;
            font-size: 4vw !important;
            line-height: initial !important;
        }
        .calendar_content{
            box-sizing: border-box;
            .calendar_title{
                .calendar_title_date{
                    span{
                        letter-spacing: 1px;
                    }
                }
                .calendar_confirm{
                    padding: 4vw 4.533333333333333vw;
                }
            }
        }
        .el-form-item__content{
            display: flex;
            align-items: center;
        }
        .el-icon-date{
            margin-right: 11px;
            color: #C0C4CC;
        }
    }
    .formItem{
        align-items: center;
        padding: 12px 10px;
        .el-form-item__label{
            line-height: initial;
        }
        .el-form-item__content{
            line-height: initial;
            .el-input__inner{
                line-height: initial;
                height: 22px;
                input{
                    padding: 10px 0;
                }
            }
            .el-select{
                .el-input {
                    .el-input__suffix{
                        top:10px;
                    }
                }
                .is-focus{
                    .el-input__suffix{
                        top:-7px;
                    }
                }

            }
            .el-date-editor{
                .el-input__prefix{
                    top:-10px;
                }

            }
        }
    }
    .lineFormItem{
        label{
            padding: 10px !important;
        }
    }

    * {
        box-sizing: border-box;
    }
}
.el-message{
    min-width: 320px;
}
.el-message-box{
    width: 90%;
}
.el-time-panel{
    left: initial;
    right:0 !important;

}
.el-scrollbar {
    >.el-scrollbar__bar {
        opacity: 1 !important;
    }
}

