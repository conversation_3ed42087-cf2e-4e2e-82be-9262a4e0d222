.leaveApprovalBox{
    padding-top: 10px;
    padding-bottom:63px;
    height: 100%;
    color: #333;
    .leaveApprovalDes{
        .el-form {
            .el-form-item{
                display: flex;
                justify-content: space-between;
                margin-bottom: 0;
                width: 100%;
                box-sizing: border-box;
                border-bottom: 1px solid #E4E4E4;
                .el-form-item__label{
                    color: #333;
                    min-width: 68px;
                }
                .el-form-item__content{
                    flex: 1;
                    .el-input{
                        input{
                            border: none; 
                            padding-left: 0;
                        }
                    }
                    .el-select{
                        width: 100%;
                        .el-input{
                            .el-input__inner{ 
                                border: none; 
                                padding-left: 0;
                            }
                        }
                    }
                    .el-date-editor{
                        width: 100%;
                        .el-input__inner{ 
                            padding-left: 0;
                            border: none; 
                        } 
                        .el-input__prefix{
                            left: inherit;
                            right: 5px !important;
                            .el-icon-time:before{
                                content: "\E78E";
                            }
                        }
                    }
                    .leaveNotice{
                        font-size: 12px;
                        color: #666;
                        text-align: left;
                    }
                   
                }
            }
            label{
                min-width: 50px;
            }
            .defFormItem{
                background-color: #fff;
            }
            .lineFormItem{
                display: inherit;
                padding: 0;
                &>label{
                    padding: 0 10px;
                    text-align: left;
                    width: 100%;
                }
                .el-form-item__content{
                    .el-textarea{
                        padding: 10px;
                        background-color: #fff;
                        box-sizing: border-box;
                        textarea{
                            padding: 0 !important;
                            border: none !important;
                            min-height: 50px !important;
                        }
                    }
                }
            }
            .addFormItem{
                margin: 10px;
                font-size: 14px;
                .approvalShowDes{
                    padding-bottom: 10px;
                    button{
                        display: inline-block;
                        padding: 0;
                        border: none;
                        line-height: initial;
                    }
                    button:hover{
                        background-color: #fff !important;
                    }
                    .el-icon-circle-plus-outline:before{
                        font-size: 45px;
                    }
                    .titleImg{
                        margin-bottom: 10px;
                    }
                    .imgLists{
                        display: flex;
                        flex-wrap: wrap;
                        align-items: center;
                        width: 100%;
                        .imgList{
                            position: relative;
                            width: 30%;
                            margin: 0 10px 0 0;
                            padding-bottom: 20px;
                            box-sizing: content-box;
                            img{
                                display: inline-block;
                                width: 100%;
                                height: 100%;
                            }
                            .deleteImg{
                                position: absolute;
                                right: -10px;
                                top: -10px;
                                font-size: 25px;
                                color: #ccc;
                                background-color: #fff;
                                border-radius: 100%;
                            }
                        }
                    }
                }
                .approvalProcess{
                    .approvalTitle{
                        margin-bottom: 10px;
                    }
                    .approvalProcessDes{
                        .approvalProcessList{
                            margin-left: 25px;
                            border-left: 1px solid #ccc;
                            padding-bottom: 15px;
                            box-sizing: border-box;
                            .circleShow{
                                margin-left: -20px;
                                margin-right: 10px;
                                display: inline-block;
                                width: 40px;
                                height: 40px;
                                line-height: 40px;
                                text-align: center;
                                border-radius: 100%;
                                border: 1px solid #7F7F7F;
                                background-color: #fff;
                                box-sizing: border-box;
                            }
                        }
                        .approvalProcessList:last-child{
                            border-left: none;
                        }
                    }
                }
            }
            .bottomBtnBox{
                background-color: #fff;
            }
        }
    }
}