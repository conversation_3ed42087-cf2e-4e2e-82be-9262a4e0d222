// .backcolorBox{
//     position: absolute;
//     top: 42px;
//     padding-bottom: 114px;
//     bottom: 0;
// }
.overtimeConfirmationBox{
    padding: 10px 0;
    padding-bottom: 114px;
    .overtimeConfirmationLists{
        font-size: 14px;
        color: #333;
        .list{
            padding: 15px 10px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: #fff;
            margin-bottom: 15px;
        }
        .addLists{
            background-color: #fff;
            border-radius: 5px;
            margin: 10px 10px 15px 10px;
            .lstTop{
                padding: 15px 10px;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                border-bottom: 1px solid #E4E4E4;
                .listTopLeft{
                    flex: 1;
                    display: flex;
                    align-items: center;
                    .timeShow{
                        flex: 1;
                        text-align: center;
                    }
                }
            }
            .overTimeEmployees{
                display: flex;
                align-items: center;
                padding: 15px 10px;
                justify-content: space-between;
                border-bottom: 1px solid #E4E4E4;
                .edit {
                    span:last-child::after{
                        content: '';
                    }
                    span::after{
                        content: ',';
                        color: #333;
                    }
                }
            }
            .overTimeEmployees:last-child{
                border: none;
            }
        }
        .foremanApplyBox{
            .foremanLists{
                .overTimeEmployees{
                    .overTimeEmployeesRight{
                        display: flex;
                        .edit {
                            span:last-child::after{
                                content: '';
                            }
                            span::after{
                                content: ',';
                                color: #333;
                            }
                        }
                    }
                }
            } 
        }
        .confirmStatus{
            background-color: #fff;
            padding: 20px 10px;
            .statusList{
                display: flex;
                margin-bottom: 10px;
            }
        }  
        .operateRecord{
            display: flex;
            align-items: center;
            padding: 0 10px;
            background-color: #fff;
            .recordList{
                margin-left: 10px;
                flex: 1;
                text-align: right;
                color: #666;
                .recordDes{
                    padding: 10px 10px 10px 0;
                    border-bottom: 1px solid #E4E4E4;
                    div{
                        margin: 5px 0;
                    }
                }
                .recordDes:last-child{
                    border-bottom:none;
                }
                
            }
        }
    }
    .overTimeEmployeeColor{
        // position: fixed;
        // bottom: 114px;
        // left: 0;
        // right: 0;
        width: 100%;
        display: flex;
        justify-content: space-around;
        box-sizing: border-box;
        padding:  20px;
        font-size: 15px;
    }
    .bottomBtnBox{
        background-color: #EFEFF4;
        
    }
}