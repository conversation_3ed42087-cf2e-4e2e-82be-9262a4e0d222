.overtimeEmployeesBox{
    margin-bottom: 62px;
    .employeesLists{
        .checkEmployees{
            margin-top: 10px;
            margin-bottom: 10px;
            .el-select{
                width: 100%;
                .el-input__inner{
                    border-right: none;
                    border-left: none;
                    border-radius: 0;
                }
            }
            .el-input {
                width: 100%;
                .el-input__inner{
                    border-right: none;
                    border-left: none;
                    border-radius: 0;
                }
            }
        }
        .checkAll{
            padding: 0 10px;
            box-sizing: border-box;
            width: 100%;
            text-align: right;
            line-height: 40px;
            border-bottom: 1px solid #E4E4E4;
            .el-checkbox__input{
                float: right;
                line-height: 40px;
                vertical-align: middle;
            }
            .el-checkbox__label{
                line-height: 40px;
                margin-right: 10px;
            }
        }
        .employeesList{
            &>label{
                padding: 0 10px;
                margin-right: 0;
                box-sizing: border-box;
                width: 100%;
                line-height: 40px;
                border-bottom: 1px solid #E4E4E4;
                .el-checkbox__input{
                    float: right;
                    line-height: 40px;
                }
                .el-checkbox__label{
                    padding: 0;
                }
            }
            .is-checked+.el-checkbox__label{
                color: #333;
                .el-checkbox__label{
                    color: #333;
                } 
            }
        }
    }
    .foremanLists{
        font-size: 14px;
        color: #333;
        .foremanList{
            padding: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #E4E4E4;
            .foremanListLeft{
                div{
                    margin-bottom: 10px;
                }
                div:last-child{
                    margin-bottom: 0;
                }
                .overtimeUser{
                    font-size: 15px;
                }
                .updateTime{
                    font-size: 13px;
                    color: #999;
                }
            }
        }
      
    }
    .foremanAllLists{
        .el-collapse{
            border-bottom:none!important;
            .el-collapse-item{
                width: 100%;
                padding: 0 10px;
                box-sizing: border-box;
                .el-collapse-item__header{
                    color: inherit!important;
                }
                .el-collapse-item__content{
                    color: inherit!important;
                    padding: 0 10px 10px 10px;
                }
                span::after {
                    content: ',';
                }
                span:last-child::after {
                    content: ''!important;
                }
            }
        }
    }
    .bottomBtnBox{
        background-color: #fff;
    }
}