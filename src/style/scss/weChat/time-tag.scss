.timeBox{
    font-size: 14px;
    .calendarShow{
        padding: 10px;
        box-sizing: border-box;
        .wh_container{
            .wh_content_all{
                background-color: #fff;
                .wh_top_changge  {
                    li{
                        margin: 12px 0;
                        color: #606266;
                        font-size: 16px;
                        height: inherit;
                        line-height: 30px;
                        letter-spacing: 1px;
                        .wh_jiantou1 {
                            width: 10px;
                            height: 10px;
                            border-top: 2px solid #606266;
                            border-left: 2px solid #606266;
                        }
                        .wh_jiantou2{
                            width: 10px;
                            height: 10px;
                            border-top: 2px solid #606266;
                            border-right: 2px solid #606266;
                        }
                    }
                }
                .wh_content{
                    padding: 0;
                    box-sizing: border-box;
                    justify-content: center;
                    .wh_content_item{
                      
                        height: 50px;
                        margin: 0 1px;
                        color: #606266;
                        font-size: 14px;
                        border-top: 3px solid #E9E9E9;
                        .wh_isMark{
                            position: relative;
                            background-color: #fff;  
                        }
                        .wh_isMark::after{
                            position: absolute;
                            top: 20px;
                            left: 13px;
                            content: '●';
                            color: #1890FF;
                            font-size: 5px;
                        }
                        .wh_item_date{
                            margin-top: 5px;
                            width: 30px;
                            height: 30px;
                            .wh_top_tag{
                                font-size: 14px;
                            }
                        }
                        .wh_chose_day{
                            color: #fff;
                            background-color: #1890FF;
                        }
                        .wh_isToday{
                            color: #fff;
                            background-color: #1890FF;

                        }
                    }
                }
            }
        }


        // element
        .el-calendar{
            .el-calendar__header{
                padding: 10px 0;
                border-bottom:none;
                box-sizing: border-box;
                .el-calendar__button-group{
                    .el-button-group{
                        button:nth-child(2){
                            span{
                                display: none;
                            }
                        }
                        .el-button:not(:first-child):not(:last-child)::before{
                            content: '当月';
                        }
                    }
                }
            }
            .el-calendar__body{
                padding: 0;
                .el-calendar-table{
                    thead{
                    }
                    tbody{
                        tr:first-child {
                            td{
                                border-top: none;
                            }
                        }
                        tr {
                            td{
                                text-align: right;
                                border-right: none;
                                border-bottom:none;
                                .el-calendar-day{
                                    padding:0;
                                    margin: 0 2px;
                                    height: 60px;
                                    border-top: 3px solid #E9E9E9;
                                    .dateShow{
                                        padding-top: 5px;
                                        p{
                                            display: inline-block;
                                            padding: 6px 5px;
                                            box-sizing: border-box;
                                        }
                                        .circleBox{
                                            .monthShow{
                                                .dayShow{
                                                    .circleShow{
                                                        display: inline-block;
                                                        padding: 6px 10px;
                                                        box-sizing: border-box;
                                                       .circleBlue{
                                                           display: inline-block;
                                                           width: 5px;
                                                           height: 5px;
                                                           background-color: #118EE9;
                                                           vertical-align: middle;
                                                           border-radius: 100%;
                                                       } 
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            td:first-child{
                                border-left: none;
                            }
                            .is-today{
                                .el-calendar-day{
                                    .dateShow{
                                        height: 100%;
                                        p{
                                            display: inline-block;
                                            border-radius: 100%;
                                            color: #fff;
                                            background-color: #118EE9;
                                        }
                                    }  
                                }
                            }
                            .is-selected{
                                background-color: #fff;
                                .el-calendar-day{
                                    .dateShow{
                                        .is-selected{
                                            display: inline-block;
                                            border-radius: 100%;
                                            color: #fff;
                                            background-color: #118EE9;
                                        }
                                    }
                                }
                                .el-calendar-day:hover{
                                    background-color: #fff;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    .clockInShow{
        padding: 0 10px;
        box-sizing: border-box;
        .clockInToday{
                padding: 15px 10px;
                box-sizing: border-box;
                border-top: 1px solid #CCCCCC;
                border-bottom: 1px solid #CCCCCC;
        }
        .clockInLists{
            padding-bottom: 10px;
            .clockInList{
                padding: 10px;
                box-sizing: border-box;
                .clockInTime{
                    margin-bottom: 10px;
                }
                .attendanceMachine{
                    font-size: 13px;
                    color: #999;
                }
            }
        }
    }
}