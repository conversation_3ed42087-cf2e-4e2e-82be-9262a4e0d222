.overtimeApplicationBox{
    padding-top: 10px;
    padding-bottom: 114px;
    height: 100%;
    .overtimeApplicationDes{
        .el-form {
            .el-form-item{
                display: flex;
                justify-content: space-between;
                margin-bottom: 0;
                box-sizing: border-box;
                .el-form-item__label{
                    color: #333;
                }
                .el-form-item__content{
                    flex: 1;
                    text-align: right;
                    .el-select{
                        .el-input{
                            .el-input__inner{
                                padding: 0 !important;
                                border: none;
                                text-align: right;
                            }
                            .el-input__suffix{
                                .el-input__suffix-inner{
                                    .el-icon-arrow-up::before{
                                        content: ''!important;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            .defFormItem{
                margin-bottom: 10px;
                background-color: #fff;
            }
           .defFormItem:last-child{
               margin-bottom: 400px !important;
           }
            .lineFormItem{
                display: inherit;
                padding: 0;
                &>label{
                    padding: 0 10px;
                    text-align: left;
                    width: 100%;
                }
                .el-form-item__content{
                    .el-textarea{
                        padding: 10px;
                        background-color: #fff;
                        box-sizing: border-box;
                        textarea{
                            padding: 0 !important;
                            border: none !important;
                            min-height: 50px !important;
                        }
                    }
                    .el-radio-group{
                        background-color: #fff;
                        width: 100%;
                        label{
                            padding: 0 10px;
                            box-sizing: border-box;
                            margin-right: 0 !important;
                            width: 100%;
                            text-align: left;
                            line-height: 40px;
                            border-bottom: 1px solid #E4E4E4;
                        }
                        label:last-child{
                            border-bottom: none;
                        }
                    }
                    .el-input{
                        background-color: #fff;
                        padding: 10px 0;
                        .el-input__inner{

                            border: none;
                            border-radius: initial;
                        }
                    }
                }
            }
            .addFormItem{
                margin: 10px;
                padding: 0;
                width: initial;
                color: #333;
                .el-form-item__content{
                    .addLists{
                        background-color: #fff;
                        border-radius: 5px;
                        margin-bottom: 10px;
                        .lstTop{
                            box-sizing: border-box;
                            border-bottom: 1px solid #E4E4E4;
                            .iconShow{
                                padding: 5px;
                                border-bottom: 1px solid #E4E4E4;
                                button{
                                    border: none;
                                    padding: 5px !important;
                                }
                                .el-icon-edit::before{
                                    font-size: 20px;
                                }
                                .el-icon-delete{
                                    font-size: 20px;
                                }
                            }
                        }
                        .listTopLeft {
                          display: flex;
                          align-items: center;
                          justify-content: center;
                          padding: 10px;
                          width: 100%;
                          box-sizing: border-box;
                          border-bottom: 1px solid #E4E4E4;
                          .el-date-editor:first-child {
                            margin-left: 0;
                          }

                          .el-input {
                            input {
                              padding: 0 !important;
                              border: none;
                              text-align: center;
                            }
                          }
                        }
                        .overTimeEmployees{
                            display: flex;
                            align-items: center;
                            padding: 10px;
                            justify-content: space-between;
                            border-bottom: 1px solid #E4E4E4;
                            .edit{
                                display: flex;
                                align-items: center;
                                .el-input{
                                    input{
                                        text-align: right;
                                        border: none;
                                    }
                                }
                                .is-disabled{
                                    .el-input__inner{
                                        color: #666;
                                       background-color: #fff;
                                    }
                                }
                            }
                        }
                        .overTimeEmployees:last-child{
                            border: none;
                        }
                        .overTimeEmployeesline{
                            border-bottom: 1px solid #E4E4E4;
                        }
                        .addSubmit{
                            margin: 10px;
                            box-sizing: border-box;
                        }
                    }
                }
            }
            .bottomBtnBox{
                background-color: #EFEFF4;
            }
        }
    }
}
