.applyCardBox{
    padding-top: 10px;
    padding-bottom:105px;
    .el-form {
        .listItem{
            align-items: end;
            .el-form-item__label{
              margin-bottom: 15px;
            }
        }
        .replenishStatus{
            .el-form-item__content{
                flex: 1;
                text-align: left !important;
                .el-radio-group{
                    display: flex;
                    label{
                        margin-bottom: 0 !important;
                        width: 100%;
                        text-align: left;
                    }
                }
            }
        }
        .el-form-item{
            display: flex;
            justify-content: space-between;
            margin-bottom: 0;
            width: 100%;
            box-sizing: border-box;
            border-bottom: 1px solid #E4E4E4;
            .el-form-item__label{
                color: #333;
            }
            .el-form-item__content{
                flex: 1;
                text-align: right;
                .el-input{
                    input{
                        padding-left: 0;
                        border: none; 
                    }
                }
                .el-date-editor{
                    width: 100%;
                    .el-input__inner{ 
                        padding-left: 0;
                        border: none; 
                    } 
                    .el-input__prefix{
                        left: inherit;
                        right: 5px !important;
                        .el-icon-time:before{
                            content: "\E78E";
                        }
                    }
                }
                .el-textarea{
                    background-color: #fff;
                    box-sizing: border-box;
                    textarea{
                        padding: 0;
                        border: none;
                        min-height: 50px !important;
                    }
                }
                .el-radio-group{
                    label{
                        margin-bottom: 15px;
                        width: 100%;
                        text-align: left;
                    }
                    label:last-child{
                        margin-bottom: 0 !important;
                    }
                }
            }
        }
        label{
            min-width: 65px;
        }
        .defFormItem{
            align-items: inherit;
            background-color: #fff;
        }
        .proposerShow{
            border-bottom: none;
            color: #666 !important;
            font-size: 14px;
            .applyList{
                display: flex;
                margin-bottom: 20px;
                div{
                    margin-right: 10px;
                }
                div:last-child{
                    margin-right: 0;
                }
            }
            .approvalShowDes{
                .titleImg{
                    padding-bottom: 10px;
                }
                .el-icon-circle-plus-outline{
                    margin-top: 10px;
                }
                .el-icon-circle-plus-outline:before{
                    color: #A09BBA;
                    font-size: 45px;
                }
                .approvalImgUrl{
                    display: flex;
                    width: 100%;
                    flex-wrap: wrap;
                    .imgList{
                        width: 30%;
                        margin: 0 10px 0 0;
                        padding-bottom: 20px;
                        box-sizing: content-box;
                        img{
                            display: inline-block;
                            width: 100%;
                            height: 100%;
                        }
                    }
                }
            }
            .approveArrayList{
                margin-bottom: 20px;
                .approveArray{
                    display: flex;
                    margin-bottom: 10px;
                }
                .approveArray:first-child{
                    div:first-child{
                        margin-right: 10px;
                    }
                }
                .approveReject{
                    padding: 5px;
                    background-color: #f0f0f0cc;
                }
            }
            .approveArrayList:last-child{
                margin-bottom: 0;
            }
        }
        .styleForm{
            .el-form-item__label{
                text-align: left;
                width: 80px;
            }
            .el-form-item__content{
                text-align: left;
                font-size: 13px;
                .is-disabled{
                    .el-input__inner{
                        background-color: #fff!important;
                        color: #333!important;
                        padding: 0;
                        font-size: 13px;;
                    }
                }
            }
        }
        .bottomBtnBox{
            background-color: #fff;
            .refuseBtn{
                background-color: #fff!important;
                color: #333!important;
                border-color: #efefef!important;
            }
        }
        .addFormItem{
            margin: 10px;
            font-size: 14px;
            .approvalShowDes{
                padding-bottom: 10px;
                button{
                    display: inline-block;
                    padding: 0;
                    border: none;
                    line-height: initial;
                }
                button:hover{
                    background-color: #fff !important;
                }
                .el-icon-circle-plus-outline:before{
                    font-size: 45px;
                }
                .titleImg{
                    margin-bottom: 10px;
                }
                .imgLists{
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;
                    width: 100%;
                    .imgList{
                        position: relative;
                        width: 30%;
                        margin: 0 10px 0 0;
                        padding-bottom: 20px;
                        box-sizing: content-box;
                        img{
                            display: inline-block;
                            width: 100%;
                            height: 100%;
                        }
                        .deleteImg{
                            position: absolute;
                            right: -10px;
                            top: -10px;
                            font-size: 25px;
                            color: #ccc;
                            background-color: #fff;
                            border-radius: 100%;
                        }
                    }
                }
            }
            .approvalProcess{
                .approvalTitle{
                    margin-bottom: 10px;
                }
                .approvalProcessDes{
                    .approvalProcessList{
                        margin-left: 25px;
                        border-left: 1px solid #ccc;
                        padding-bottom: 15px;
                        box-sizing: border-box;
                        .circleShow{
                            margin-left: -20px;
                            margin-right: 10px;
                            display: inline-block;
                            width: 40px;
                            height: 40px;
                            line-height: 40px;
                            text-align: center;
                            border-radius: 100%;
                            border: 1px solid #7F7F7F;
                            background-color: #fff;
                            box-sizing: border-box;
                        }
                    }
                    .approvalProcessList:last-child{
                        border-left: none;
                    }
                }
            }
        }
    }
    .el-dialog{
        .el-form {
            .remark{
                margin-bottom: 20px;
                border: none !important;
                .el-form-item__content{
                    .el-textarea{
                        padding: 0;
                        textarea{
                            padding: 10px;
                            border: 1px solid #DCDFE6 !important;
                        }
                    }
                }
            }
            .footBtn{
                padding: 0;
                border-bottom: none !important;
            }
        }
    }
}