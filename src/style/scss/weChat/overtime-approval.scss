.overtimeApprovalBox{
    height: 100%;
    .overtimeApprovalDes{
        padding-bottom: 60px;
        .flex-between{
            margin: 10px;
            .el-select{
                width: 49%;
            }
        }
        .approvalList{
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 10px;
            padding: 10px;
            box-sizing: border-box;
            font-size: 14px;
            color: #666;
            background-color: #fff;
            border-radius: 5px;
            border: 1px solid #E4E4E4;
            .listLeft{
                flex: 1;
                .leftDes{
                    display: flex;
                    margin-bottom: 5px;
                    .listContent{
                       flex: 1;
                    }
                    .proposerName{
                        font-size: 16px;
                        margin-bottom: 10px;
                        color: #333;
                        font-weight: 600;
                    }
                }
                .applyTime{
                    margin-top: 20px;
                }
            }
            .listRight{
                display: flex;
                .approvalStatus{
                    color: #118EE9;
                }

            }
        }
        .bottomBtnBox{
            background-color: #fff;
        }
    }
}