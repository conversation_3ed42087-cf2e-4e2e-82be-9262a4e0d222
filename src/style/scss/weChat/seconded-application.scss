.secondedApplicationBox{
    padding-top: 10px;
    padding-bottom:63px;
    height: 100%;
    .el-form {
        .el-form-item{
            display: flex;
            justify-content: space-between;
            margin-bottom: 0;
            width: 100%;
            box-sizing: border-box;
            border-bottom: 1px solid #E4E4E4;
            .el-form-item__label{
                color: #333;
            }
            .el-form-item__content{
                flex: 1;
                .is-disabled {
                    .el-input__inner{
                        background-color: #fff !important;
                    }
                }
                .secondedConfirm{
                    padding-left: 15px;
                }
                .el-select{
                    width: 100%;
                    .el-input{
                        input{
                            padding-left: 0;
                            border: none;
                        }
                    }
                }
                .el-input{
                    input{
                        padding-left: 0;
                        border: none;
                    }
                }
                .el-date-editor{
                    width: 100%;
                    .el-input__inner{ 
                        padding-left: 15px;
                        border: none; 
                    } 
                    .el-input__prefix{
                        left: inherit;
                        right: 5px !important;
                        .el-icon-time:before{
                            content: "\E78E";
                        }
                    }
                }
                .leaveNotice{
                    font-size: 12px;
                    color: #666;
                    text-align: left;
                }
            }
        }
        label{
            min-width: 68px;
            text-align: left;
        }
        .defFormItem{
            background-color: #fff;
        }
        // 借调确认
        .proposerShow{
            border-bottom: none;
            color: #666 !important;
            font-size: 14px;
            .applyList{
                display: flex;
                margin-bottom: 20px;
                div{
                    margin-right: 10px;
                }
                div:last-child{
                    margin-right: 0;
                }
            }
            .approveArrayList{
                margin-bottom: 20px;
                .approveArray{
                    display: flex;
                    margin-bottom: 10px;
                }
                .approveArray:first-child{
                    div:first-child{
                        margin-right: 10px;
                    }
                }
                .approveReject{
                    padding: 5px;
                    background-color: #f0f0f0cc;
                }
            }
            .approveArrayList:last-child{
                margin-bottom: 0;
            }
        }
        .styleForm{
            .el-form-item__content{
                .el-date-editor{
                    .el-input__inner{
                        background-color: #fff!important;
                        color: #333!important;
                        padding: 0;
                        font-size: 14px;;
                    }
                }
            }
        }
        // 借调确认
        .lineFormItem{
            display: inherit;
            padding: 0;
            &>label{
                padding: 0 10px;
                text-align: left;
                width: 100%;
            }
            .el-form-item__content{
                .el-textarea{
                    padding: 10px;
                    background-color: #fff;
                    box-sizing: border-box;
                    textarea{
                        padding: 0 !important;
                        border: none !important;
                        min-height: 50px !important;
                    }
                }
            }
        }
        .addFormItem{
            margin: 10px;
            font-size: 14px;
            .approvalShowDes{
                .el-icon-circle-plus-outline{
                    margin-top: 10px;
                }
                .el-icon-circle-plus-outline:before{
                    color: #A09BBA;
                    font-size: 45px;
                }
            }
            .approvalProcess{
                .approvalTitle{
                    margin-bottom: 10px;
                }
                .approvalProcessDes{
                    .approvalProcessList{
                        margin-left: 25px;
                        border-left: 1px solid #ccc;
                        padding-bottom: 15px;
                        box-sizing: border-box;
                        .circleShow{
                            margin-left: -20px;
                            margin-right: 10px;
                            display: inline-block;
                            width: 40px;
                            height: 40px;
                            line-height: 40px;
                            text-align: center;
                            border-radius: 100%;
                            border: 1px solid #7F7F7F;
                            background-color: #fff;
                            box-sizing: border-box;
                        }
                    }
                    .approvalProcessList:last-child{
                        border-left: none;
                    }
                }
            }
        }
        .bottomBtnBox{
            background-color: #fff;
        }
    }
    .dialogRemark{
        .el-form-item{
            border-top:1px solid #E4E4E4;
            margin-bottom: 20px;
        }
        .el-form-item:last-child{
            border-bottom: none;
        }
        .footBtn{
            border-top:1px solid #E4E4E4;
        }
    }
    .dialogReject{
        .el-form-item{
            border: none;
        }
    }
}