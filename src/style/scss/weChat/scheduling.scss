.schedulingBox{
    .navScheduling{
        display: flex;
        div{
            padding: 12px 0;
            flex: 1;
            text-align: center;
            color: #fff;
            font-size: 14px;
            border-radius: 3px;
        }
        div:first-child{
            margin-right: 1px;
        }
        .schedulingNav{
            background-color: #8B9CAC;
        }
        .schedulingNavActive{
            background-color: #999;
        }
    }
    // 日历
    .calendarBox{
        padding: 10px;
        .el-date-table-calendar {
            th[data-v-55be3324]{
                padding: 0;
                border-top: 3px solid #E9E9E9;
                border-bottom: none;
                line-height: 48px;
            }
            td {
                div[data-v-55be3324]{
                    margin: 0 1px;
                    border-top: 3px solid #E9E9E9;
                }
            }
        }
        .el-picker-panel-calendar[data-v-151d6f47]{
            border:none;
            box-shadow:none;
        }
        .el-date-table-calendar[data-v-55be3324]{
            font-size: 14px;
        }
    }
    
    .calendarDefinedBox{
        .el-date-table-calendar td div[data-v-55be3324]{
            min-height: 50px;
            div{
                // line-height: 23px;
                span{
                    margin: 6px 0;
                    display: inline-block;
                   
                }
            }
        }
    }
    // 人员
    .schedulingEmployeesBox{
        font-size: 14px;
        .block{
            padding: 10px;
            text-align: right;
            .el-date-editor{
                width: 135px;
            }
        }
        .checkEmployees{
            margin-bottom: 10px;
            .el-select{
                width: 100%;
                .el-input__inner{
                    border-right: none;
                    border-left: none;
                    border-radius: 0;
                }
            }
            .el-input{
                width: 100%;
                .el-input__inner{
                    border-right: none;
                    border-left: none;
                    border-radius: 0;
                }
            }
        }
        .employeesLists{
            .employeesList{
                padding: 15px 10px;
                border-bottom: 1px solid #E4E4E4;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
        }
    }

    // 考勤 排班
    .checkDetails{
        margin: 0 10px;
        padding: 12px 10px;
        font-size: 14px;
        border-top: 1px solid #E4E4E4;
        border-bottom: 1px solid #E4E4E4;
    }
    .el-dialog{
        .remark{
            display: flex;
            .el-form-item__content{
                flex: 1;
            }
        }
    }
}