.calendarBox{
    width: 100%;
    padding: 20px;
    box-sizing: border-box;
    background-color: #fff;
    // .divData{
    //     width: 100%;
    //     padding-top: 10px;
    //     padding-right: 10px;
    //     text-align: right;
    //     box-sizing: border-box;
    // }
    // .pChange{
    //     width: 100%;
    //     margin-top: 20px;
    //     display: flex;
    //     flex-wrap: nowrap;
    //     align-items: center;
    //     justify-content: center;
    //     span{
    //         display: block;
    //         flex: 1;
    //         padding: 6px 0;
    //         border: 1px solid #DCDFE6;
    //         color: #606266;
    //         font-size: 12px;
    //         text-align: center;
    //         cursor: pointer;
    //         &.work{
    //             border-top-left-radius: 10px;
    //             border-bottom-left-radius: 10px;
    //         }
    //         &.relax{
    //             border-right: none;
    //             border-left: none;
    //         }
    //         &.vacation{
    //             border-top-right-radius: 10px;
    //             border-bottom-right-radius: 10px;
    //         }
    //         &.workActive{
    //             color:#fff;
    //             background-color: #1890FF;
    //         }
    //         &.relaxActive{
    //             color:#fff;
    //             background-color: green;
    //         }
    //         &.vacationActive{
    //             color:#fff;
    //             background-color: red;
    //         }
    //     }
    // }

    .initBtn{
        text-align: right;
        margin: 10px 0;;
    }
    #data{ 
        width: 100%;
        min-width: 675px;
        #title{ 
            overflow: hidden; 
            list-style: none; 
            li{ 
                float: left; 
                width: 13%; 
                height: 70px; 
                line-height: 70px; 
                text-align: right;
                margin: 0 4px;
                box-sizing: border-box;
            } 
        } 
        #date{ 
            overflow: hidden; 
            list-style: none; 
            li{
                float: left; 
                width: 13.1%; 
                border-top: 4px solid #efefef;  
                text-align: center; 
                margin: 0 4px;
                padding: 10px 5px 5px 5px;
                cursor: pointer;
                box-sizing: border-box;
                span{
                    display: inline-block;
                    &.divData{
                        width: 100%;
                        text-align: right;
                    }
                    &.pChange{
                        width: 100%;
                        margin-top: 30px;
                        display: flex;
                        flex-wrap: nowrap;
                        align-items: center;
                        justify-content: center;
                        i{
                            display: block;
                            flex: 1;
                            padding: 6px 0;
                            border: 1px solid #DCDFE6;
                            color: #606266;
                            font-size: 12px;
                            text-align: center;
                            cursor: pointer;
                            &.work{
                                border-top-left-radius: 20px;
                                border-bottom-left-radius: 20px;
                            }
                            &.relax{
                                border-right: none;
                                border-left: none;
                            }
                            &.vacation{
                                border-top-right-radius: 20px;
                                border-bottom-right-radius: 20px;
                            }
                            &.workActive{
                                color:#fff;
                                background-color: #1890FF;
                            }
                            &.relaxActive{
                                color:#fff;
                                background-color: green;
                            }
                            &.vacationActive{
                                color:#fff;
                                background-color: red;
                            } 
                        }
                    }
                }
            } 
        } 
    } 
}