.directBox{
    width: 100%;
    padding: 20px;
    box-sizing: border-box;
    background-color: #fff;
    .weekday{
        .el-form-item__content{
            width: 270px;
            box-sizing: border-box;
            border-radius: 4px;
            border: 1px solid #DCDFE6;
            line-height: 30px;
            .el-form-item{
                width: 46%;
                margin-right: 0;
                .el-form-item__content{
                    width: 100%;
                    border: 1px solid #fff;
                    box-sizing: border-box;
                    .el-input{
                        width: 100%;
                        .el-input__inner{
                            width: 100%;
                            border: 1px solid #fff;
                            text-align: center;
                            box-sizing: border-box;
                        }
                        .el-input__prefix{
                            .el-input__icon{
                                line-height: 30px;
                            }
                        }
                    }
                    .el-input--suffix .el-input__inner{
                        padding-right: 0;
                    }
                }
                &.endWeek{
                    .el-input__prefix{
                        .el-icon-date:before {
                            content: "";
                        }
                    }
                    .el-input--suffix .el-input__inner{
                        padding-left: 0;
                    }
                }
            }
        }
    }
}