#app{
	.container {
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		width: 100%;
		.header {
			width: 100%;
			height: 60px;
			line-height: 60px;
			background-color: #fff;
			.logo {
				font-size: 22px;
				.logo-title{
					float: left;
					width: 230px;
					font-size: 22px;
					color: #fff;
					font-weight: bold;
					background-color: #00284D;
					text-align: center;
					margin-right: 25px;
				}	
			}
			.userInfo {
				text-align: right;
				padding-right: 25px;
				.fontIcon{
					margin: 0 25px;
				}
				.el-dropdown{
					box-sizing: border-box;
					.userInfo-inner {
						cursor: pointer;
						img {
							display: inline-block;
							width: 24px;
							height: 24px;
							border-radius: 100%;
							margin-right: 10px;
							border: 1px solid #efefef;
							box-sizing: border-box;
							vertical-align: middle;
						}
					}
				}
			}
		}
		.main {
			position: absolute;
			top: 60px;
			bottom: 0;
			left: 0;
			right: 0;
			width: 100%;
			overflow: hidden;
			background-color: #F0F2F5;
			.menu-collapsed{
				display: none!important;
			}
			aside {
				width: 230px;
				min-height: 100%;
				background-color: #001529;
				.el-menu{
					width: 100%;
					background-color: #001529;
					.el-menu-item{
						color: #A5ACB3;
						&:hover{
							color: #fff!important;
							background-color: #1890FF!important;
							i {
								color: #fff !important;
							}
						}
						i{
							margin-right: 10px;
							vertical-align: text-top;
							&:hover{
								color: #fff!important;
							}
						}
					}
					.el-submenu{
						.el-submenu__title{
							color: #A5ACB3;
							&:hover{
								color: #fff!important;
								background-color: #1890FF!important;
								i {
									color: #fff !important;
								}
							}
							i{
								margin-right: 10px;
								vertical-align: text-top;
								&:hover{
									color: #fff!important;
								}
							}
						}
						.el-menu-item{
							color: #A5ACB3;
							&:hover{
								color: #fff!important;
								background-color: #1890FF!important;
							}
						}
						// .is-active{
						// 	color: #fff!important;
						// 	background-color: #1890FF!important;
						// }
					}
					// .is-opened{
					// 	.el-submenu__title{
					// 		color: #fff!important;
					// 		background-color: #1890FF!important;
					// 	}
					// }
					.is-active{
						color: #fff!important;
						background-color: #1890FF!important;
						.el-submenu__title{
							color: #A5ACB3!important;
							background-color: #001529!important;
						}
					}
				}
			}
			.menu-expand{
				left: 0!important;
			}
			.content-container {
				position: absolute;
				right: 0px;
				top: 0px;
				bottom: 0px;
				left: 230px;
				overflow-y: scroll;
				padding: 20px;
				box-sizing: border-box;
				.content-wrapper {
					// 面包屑
					.el-breadcrumb{
						margin-bottom: 20px;
						.el-breadcrumb__inner{
							color: #9E9C9A;
						}
						.is-link{
							font-weight: normal;
						}
					}	
					// 头部搜索	
					.searchFrom{
						width: 100%;
						.el-form{
							.el-form-item{
								.el-form-item__label{
									min-width: 85px;
									text-align: right;
								}
								.el-form-item__content{
									.el-input__inner{
										height: 30px;
										line-height: 30px;
									}
									.el-button{
										padding: 7px 15px;
									}
									.el-input__icon{
										height: inherit;
									}
									.el-date-editor--daterange{
										width: 270px;
									}
									.el-range-separator{
										line-height: 24px;
						
									}
								}
							}
							.searchMore{
								margin-bottom: 0;
								.el-form-item{
									margin-bottom: 22px;
								}
							}
						}
					}
					.operateBtns{
						margin-bottom: 22px;
						.el-button{
							padding: 7px 15px;
						}
					}
					// 表格
					.tableContent{
						padding: 10px;
						width: 100%;
						box-sizing: border-box;
						table{
							width: 100%;
							thead{
								tr{
									th{
										padding: 12px 5px;
										line-height: 23px;
										font-size: 14px;
										color: #909399;
										text-align: center;
										border: 1px solid #EBEEF5;
										vertical-align: middle;
									}
								}
							}
							tbody{
								tr{
									&:hover>td{
										background-color: #F5F7FA !important;
										}
									td{
										padding: 12px 5px;
										font-size: 14px;
										color: #606266;
										text-align: center;
										border: 1px solid #EBEEF5;
										vertical-align: middle;
										a{
											color: #409EFF;
											text-decoration: none;
										}
									}
								}
							}
						}
					}
					// 分页
					.paginationBox{
						margin-top: 20px;
						text-align: right;
						.el-pagination{
							button{
								border: 1px solid #eee!important;
								border-radius: 5px;
							}
							.btn-next{
								padding: 0 5px !important;
							}
							.btn-prev{
								padding: 0 5px !important;
							}
						}   
						.el-pager  {
							li{
								padding: 0 4px;
								font-size: 13px;
								min-width: 30px;
								margin-right: 10px;
								border-radius: 5px;
								height: 28px;
								line-height: 26px;
								-webkit-box-sizing: border-box;
								box-sizing: border-box;
								text-align: center;
								color: #9A9A9A;
								border: 1px solid #eee;
								&:first-child{
									margin-left: 10px;
								}
							}	
						}
						.active {
							background-color: #409EFF;
							color: #fff!important;
							cursor: default;
							border: 1px solid #409EFF !important;
						}
						.el-pagination {
							span:not([class*=suffix]){
								min-width: 30px!important;
							}
						}
					}
				}
			}
		}
	}
}
.el-dropdown-menu a{
	color: #606266;
	text-decoration: none;
}
.el-table th.gutter{
    display: table-cell!important;
}
.el-table colgroup.gutter{
	display: table-cell!important;
}