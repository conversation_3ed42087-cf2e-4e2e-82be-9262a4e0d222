// 常用工具文件
export default {
	// 导出模板
	downloadExcel(res, fileName, fileType = 'xls'){
		var blob = new Blob([res], { type: res.type });
		var url = window.URL.createObjectURL(blob); // 创建下载的链接
		var link = document.createElement('a'); // 创建a标签
		var fileSuffix = `.${fileType}`; // 文件的后缀，默认是Excel的xls
		link.href = url;
		link.download = typeof fileName !== 'undefined' ? fileName + fileSuffix : new Date().getTime() + fileSuffix; // 下载后文件名
		document.body.appendChild(link);
		link.click(); // 点击下载
		document.body.removeChild(link); // 下载完成移除元素
		window.URL.revokeObjectURL(url); // 释放掉blob对象
	},

	// 下载模板
	downloadTxt(res, fileName, fileType = 'txt'){
		var blob = new Blob([res], { type: res.type });
		var url = window.URL.createObjectURL(blob); // 创建下载的链接
		var link = document.createElement('a'); // 创建a标签
		var fileSuffix = `.${fileType}`; // 文件的后缀
		link.href = url;
		link.download = typeof fileName !== 'undefined' ? fileName + fileSuffix : new Date().getTime() + fileSuffix; // 下载后文件名
		document.body.appendChild(link);
		link.click(); // 点击下载
		document.body.removeChild(link); // 下载完成移除元素
		window.URL.revokeObjectURL(url); // 释放掉blob对象
	}
}