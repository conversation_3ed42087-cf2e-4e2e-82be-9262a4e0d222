{"name": "vuedemo", "version": "1.0.0", "description": "A Vue.js project", "author": "Abby", "private": true, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "build": "node build/build.js"}, "dependencies": {"ajv": "^6.9.1", "babel-polyfill": "^6.26.0", "crypto-js": "^3.1.9-1", "dayjs": "^1.10.5", "echarts": "^4.4.0", "ele-calendar": "^1.2.0", "element-ui": "^2.12.0", "es6-promise": "^4.2.8", "js-base64": "^2.5.1", "js-sha1": "^0.6.0", "moment": "^2.27.0", "vue": "^2.5.2", "vue-calendar-component": "^2.8.2", "vue-hash-calendar": "^1.2.11", "vue-infinite-scroll": "^2.0.2", "vue-router": "^3.0.1", "vue2-datepick": "^3.0.11", "weixin-js-sdk": "^1.4.0-test"}, "devDependencies": {"autoprefixer": "^7.1.2", "axios": "^0.19.0", "babel-core": "^6.22.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.1", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-preset-env": "^1.7.0", "babel-preset-stage-2": "^6.22.0", "chalk": "^2.0.1", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^2.30.1", "node-notifier": "^5.1.2", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "rimraf": "^2.6.0", "sass-loader": "^7.3.1", "semver": "^5.3.0", "shelljs": "^0.7.6", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vue-loader": "^13.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.5.2", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-dev-server": "^2.9.1", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}