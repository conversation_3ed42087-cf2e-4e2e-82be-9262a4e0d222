FROM node:8-slim
RUN echo "deb http://archive.debian.org/debian/ stretch main" > /etc/apt/sources.list \
    && echo "deb http://archive.debian.org/debian-security stretch/updates main" >> /etc/apt/sources.list
RUN apt-get update  && apt-get install -y nginx
WORKDIR /usr/src/app
# COPY ["package.json", "package-lock.json*", "./"]
COPY . .
#RUN SASS_BINARY_SITE=https://npm.taobao.org/mirrors/node-sass/ npm install --registry=https://registry.npm.taobao.org
RUN npm config set proxy http://192.168.0.115:7897
RUN npm cache clean --force
RUN npm install --registry=https://registry.npmmirror.com
RUN npm run build
# qshell 传文件
RUN chmod a+x qshell
RUN ./qshell account 0eFlAzp0De82VJMd9FmqdQJfrn_88m9LywJXNGRe O0rRITg-TiWgpQT_OP7XsJP1f5Ym6EwxH72EWrpy cdn1
RUN ./qshell listbucket --prefix fras_static/ cdn1 -o tobeDel.log
RUN ./qshell batchdelete --force cdn1 -i tobeDel.log
RUN ./qshell qupload ./qiniuUpload.conf || true | tee upload.log

RUN ln -sf /dev/stdout /var/log/nginx/access.log \
        && ln -sf /dev/stderr /var/log/nginx/error.log
EXPOSE 80
RUN cp -r dist/* /var/www/html \
    && rm -rf /user/src/app
CMD ["nginx","-g","daemon off;"]
